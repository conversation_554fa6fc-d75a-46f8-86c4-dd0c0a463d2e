package com.cleevio.fundedmind.application.module.referral

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.referral.command.ReorderReferralsCommand
import com.cleevio.fundedmind.application.module.referral.command.ReorderReferralsCommand.ReferralOrderingInput
import com.cleevio.fundedmind.domain.referral.ReferralRepository
import com.cleevio.fundedmind.domain.referral.exception.ActiveReferralsMismatchException
import com.cleevio.fundedmind.domain.referral.exception.ReferralOrderCannotBeNegativeException
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class ReorderReferralsCommandHandlerTest @Autowired constructor(
    private val underTest: ReorderReferralsCommandHandler,
    private val referralRepository: ReferralRepository,
) : IntegrationTest() {

    @Test
    fun `should reorder referrals`() {
        // given
        dataHelper.getReferral(id = 1.toUUID(), listingOrder = 1)
        dataHelper.getReferral(id = 2.toUUID(), listingOrder = 2)
        dataHelper.getReferral(id = 3.toUUID(), listingOrder = 3)

        // when
        underTest.handle(
            ReorderReferralsCommand(
                referralOrderings = listOf(
                    ReferralOrderingInput(referralId = 1.toUUID(), newListingOrder = 2),
                    ReferralOrderingInput(referralId = 2.toUUID(), newListingOrder = 3),
                    ReferralOrderingInput(referralId = 3.toUUID(), newListingOrder = 1),
                ),
            ),
        )

        // then
        val referrals = referralRepository.findAll()
        referrals shouldHaveSize 3
        referrals.first { it.id == 1.toUUID() }.listingOrder shouldBe 2
        referrals.first { it.id == 2.toUUID() }.listingOrder shouldBe 3
        referrals.first { it.id == 3.toUUID() }.listingOrder shouldBe 1
    }

    @Test
    fun `should reorder referrals even if display order is not unique`() {
        // given
        dataHelper.getReferral(id = 1.toUUID(), listingOrder = 1)
        dataHelper.getReferral(id = 2.toUUID(), listingOrder = 2)
        dataHelper.getReferral(id = 3.toUUID(), listingOrder = 3)

        // when
        underTest.handle(
            ReorderReferralsCommand(
                referralOrderings = listOf(
                    ReferralOrderingInput(referralId = 1.toUUID(), newListingOrder = 1),
                    ReferralOrderingInput(referralId = 2.toUUID(), newListingOrder = 1),
                    ReferralOrderingInput(referralId = 3.toUUID(), newListingOrder = 1),
                ),
            ),
        )

        // then
        val referrals = referralRepository.findAll()
        referrals shouldHaveSize 3
        referrals.first { it.id == 1.toUUID() }.listingOrder shouldBe 1
        referrals.first { it.id == 2.toUUID() }.listingOrder shouldBe 1
        referrals.first { it.id == 3.toUUID() }.listingOrder shouldBe 1
    }

    @Test
    fun `should throw if there is a mismatch of referrals - referral is missing`() {
        dataHelper.getReferral(id = 1.toUUID(), listingOrder = 1)
        dataHelper.getReferral(id = 2.toUUID(), listingOrder = 2)

        shouldThrow<ActiveReferralsMismatchException> {
            underTest.handle(
                ReorderReferralsCommand(
                    referralOrderings = listOf(
                        ReferralOrderingInput(referralId = 1.toUUID(), newListingOrder = 1),
                    ),
                ),
            )
        }
    }

    @Test
    fun `should throw if display order is not positive or zero`() {
        dataHelper.getReferral(id = 1.toUUID(), listingOrder = 1)

        shouldThrow<ReferralOrderCannotBeNegativeException> {
            underTest.handle(
                ReorderReferralsCommand(
                    referralOrderings = listOf(
                        ReferralOrderingInput(referralId = 1.toUUID(), newListingOrder = -1),
                    ),
                ),
            )
        }
    }
}
