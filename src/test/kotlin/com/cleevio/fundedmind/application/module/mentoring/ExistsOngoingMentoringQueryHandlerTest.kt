package com.cleevio.fundedmind.application.module.mentoring

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.mentoring.query.ExistsOngoingMentoringQuery
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant

class ExistsOngoingMentoringQueryHandlerTest @Autowired constructor(
    private val underTest: ExistsOngoingMentoringQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should return ongoing mentoring`() {
        // given
        val student = dataHelper.getStudent(id = 1.toUUID())
        val trader = dataHelper.getTrader(id = 1.toUUID())
        val product = dataHelper.getProduct(id = 1.toUUID(), traderId = trader.id)

        dataHelper.getMentoring(
            id = 1.toUUID(),
            productId = product.id,
            studentId = student.id,
            sessionCount = 5,
            useSessions = 3,
        )

        // when
        val result = underTest.handle(
            ExistsOngoingMentoringQuery(studentId = student.id, traderId = trader.id),
        )

        // then
        result.mentoringId shouldBe 1.toUUID()
    }

    @Test
    fun `should return ongoing mentoring even if its expired`() {
        // given
        val student = dataHelper.getStudent(id = 1.toUUID())
        val trader = dataHelper.getTrader(id = 1.toUUID())
        val product = dataHelper.getProduct(id = 1.toUUID(), traderId = trader.id)

        dataHelper.getMentoring(
            id = 1.toUUID(),
            productId = product.id,
            studentId = student.id,
            sessionCount = 5,
            useSessions = 3,
            createdTimestamp = "2025-01-01T00:00:00.000Z".toInstant(),
            expiresAt = Instant.now().minusSeconds(1000),
        ).also {
            it.isExpired shouldBe true
        }

        // when
        val result = underTest.handle(
            ExistsOngoingMentoringQuery(studentId = student.id, traderId = trader.id),
        )

        // then
        result.mentoringId shouldBe 1.toUUID()
    }

    @Test
    fun `should return ongoing mentoring even if one was used and the second is underway`() {
        // given
        val student = dataHelper.getStudent(id = 1.toUUID())
        val trader = dataHelper.getTrader(id = 1.toUUID())
        val product = dataHelper.getProduct(id = 1.toUUID(), traderId = trader.id)

        dataHelper.getMentoring(
            id = 1.toUUID(),
            productId = product.id,
            studentId = student.id,
            sessionCount = 5,
            useSessions = 5, // fully used
        )
        dataHelper.getMentoring(
            id = 2.toUUID(),
            productId = product.id,
            studentId = student.id,
            sessionCount = 5,
            useSessions = 3, // not fully used
        )

        // when
        val result = underTest.handle(
            ExistsOngoingMentoringQuery(studentId = student.id, traderId = trader.id),
        )

        // then
        result.mentoringId shouldBe 2.toUUID()
    }

    @Test
    fun `should return no ongoing mentoring if its fully used`() {
        // given
        val student = dataHelper.getStudent(id = 1.toUUID())
        val trader = dataHelper.getTrader(id = 1.toUUID())
        val product = dataHelper.getProduct(id = 1.toUUID(), traderId = trader.id)

        dataHelper.getMentoring(
            id = 1.toUUID(),
            productId = product.id,
            studentId = student.id,
            sessionCount = 5,
            useSessions = 5,
        )

        // when
        val result = underTest.handle(
            ExistsOngoingMentoringQuery(studentId = student.id, traderId = trader.id),
        )

        // then
        result.mentoringId shouldBe null
    }

    @Test
    fun `should return no ongoing mentoring if there is none`() {
        // given
        val student = dataHelper.getStudent(id = 1.toUUID())
        val trader = dataHelper.getTrader(id = 1.toUUID())
        val product = dataHelper.getProduct(id = 1.toUUID(), traderId = trader.id)

        // when
        val result = underTest.handle(
            ExistsOngoingMentoringQuery(studentId = student.id, traderId = trader.id),
        )

        // then
        result.mentoringId shouldBe null
    }
}
