package com.cleevio.fundedmind.application.module.gamelevelreward

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.command.AppButtonWithLinkInput
import com.cleevio.fundedmind.application.module.gamelevelreward.command.CreateNewGameLevelRewardCommand
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.gamelevelreward.GameLevelRewardRepository
import com.cleevio.fundedmind.domain.gamelevelreward.constant.GameLevelRewardType
import com.cleevio.fundedmind.domain.gamelevelreward.exception.GameLevelRewardButtonWithoutLinkException
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class CreateNewGameLevelRewardCommandHandlerTest @Autowired constructor(
    private val underTest: CreateNewGameLevelRewardCommandHandler,
    private val gameLevelRewardRepository: GameLevelRewardRepository,
) : IntegrationTest() {

    @Test
    fun `should create new level reward without coupon code and button`() {
        // when
        val result = underTest.handle(
            defaultCommand(rewardCouponCode = null, rewardButton = null),
        )

        // then
        val gameLevelReward = gameLevelRewardRepository.findByIdOrNull(result.id)!!

        gameLevelReward.run {
            name shouldBe "Test Level Reward"
            gameLevel shouldBe GameLevel.ONE
            type shouldBe GameLevelRewardType.ONLINE
            description shouldBe "Test Description"
            rewardCouponCode shouldBe null
            rewardButton shouldBe null
            published shouldBe false
        }
    }

    @Test
    fun `should create new level reward with coupon code and without button`() {
        // when
        val result = underTest.handle(
            defaultCommand(rewardCouponCode = "TEST123", rewardButton = null),
        )

        // then
        val gameLevelReward = gameLevelRewardRepository.findByIdOrNull(result.id)!!

        gameLevelReward.run {
            rewardCouponCode shouldBe "TEST123"
            rewardButton shouldBe null
        }
    }

    @Test
    fun `should create new level reward with button and without coupon code`() {
        // when
        val result = underTest.handle(
            defaultCommand(
                rewardCouponCode = null,
                rewardButton = AppButtonWithLinkInput(
                    text = "Button",
                    color = Color.BLUE,
                    linkUrl = "https://example.com",
                ),
            ),
        )

        // then
        val gameLevelReward = gameLevelRewardRepository.findByIdOrNull(result.id)!!

        gameLevelReward.run {
            rewardCouponCode shouldBe null
            rewardButton!!.run {
                text shouldBe "Button"
                color shouldBe Color.BLUE
                linkUrl shouldBe "https://example.com"
            }
        }
    }

    @Test
    fun `should create level reward without description`() {
        // when
        val result = underTest.handle(
            defaultCommand(
                description = null,
                rewardCouponCode = null,
                rewardButton = null,
            ),
        )

        // then
        val gameLevelReward = gameLevelRewardRepository.findByIdOrNull(result.id)!!

        gameLevelReward.run {
            description shouldBe null
            rewardCouponCode shouldBe null
            rewardButton shouldBe null
        }
    }

    @Test
    fun `should throw when creating new level reward with button with empty link`() {
        // when & then
        shouldThrow<GameLevelRewardButtonWithoutLinkException> {
            underTest.handle(
                defaultCommand(
                    rewardCouponCode = null,
                    rewardButton = AppButtonWithLinkInput(
                        text = "Button",
                        color = Color.BLUE,
                        linkUrl = "",
                    ),
                ),
            )
        }
    }

    private fun defaultCommand(
        name: String = "Test Level Reward",
        gameLevel: GameLevel = GameLevel.ONE,
        type: GameLevelRewardType = GameLevelRewardType.ONLINE,
        description: String? = "Test Description",
        rewardCouponCode: String?,
        rewardButton: AppButtonWithLinkInput?,
    ) = CreateNewGameLevelRewardCommand(
        name = name,
        gameLevel = gameLevel,
        type = type,
        description = description,
        rewardCouponCode = rewardCouponCode,
        rewardButton = rewardButton,
    )
}
