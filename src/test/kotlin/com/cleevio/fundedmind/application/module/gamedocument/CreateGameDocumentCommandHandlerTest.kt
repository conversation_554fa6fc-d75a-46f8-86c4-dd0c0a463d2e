package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamedocument.command.CreateGameDocumentCommand
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.gamedocument.GameDocumentRepository
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.gamedocument.constant.IssuingCompany
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentPayoutAmountCannotBeNegativeException
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentPayoutAmountIsRequiredException
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentTruthScoreCannotBeNegativeException
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

class CreateGameDocumentCommandHandlerTest @Autowired constructor(
    private val underTest: CreateGameDocumentCommandHandler,
    private val gameDocumentRepository: GameDocumentRepository,
) : IntegrationTest() {

    @Test
    fun `should create new game document`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 103.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "John",
                lastName = "Doe",
                studentTier = StudentTier.MASTERCLASS,
            )
        }

        every {
            zapierService.newGameDocument(
                studentName = any(),
                gameDocumentId = any(),
                gameDocumentType = any(),
                payoutAmount = any(),
                payoutDate = any(),
                truthScore = any(),
                scoreMessage = any(),
            )
        } just Runs

        val result = underTest.handle(
            defaultCommand(
                studentId = user.id,
                type = GameDocumentType.PAYOUT,
                issuingCompany = IssuingCompany.FOR_TRADERS,
                payoutAmount = BigDecimal("1000.00"),
                reachedLevel = GameLevel.TWO,
                payoutDate = LocalDate.now(),
                truthScore = 95,
                scoreMessage = "Great achievement!",
            ),
        )

        gameDocumentRepository.findByIdOrNull(result.id)!!.run {
            studentId shouldBe user.id
            type shouldBe GameDocumentType.PAYOUT
            issuingCompany shouldBe IssuingCompany.FOR_TRADERS
            payoutAmount!! shouldBeEqualComparingTo BigDecimal("1000.00")
            previousLevel shouldBe GameLevel.ONE
            reachedLevel shouldBe GameLevel.TWO
            payoutDate shouldBe LocalDate.now()
            truthScore shouldBe 95
            scoreMessage shouldBe "Great achievement!"
            state shouldBe GameDocumentApprovalState.WAITING
            denyMessage shouldBe null
        }

        verify {
            zapierService.newGameDocument(
                studentName = "John Doe",
                gameDocumentId = result.id,
                gameDocumentType = GameDocumentType.PAYOUT,
                payoutAmount = BigDecimal("1000.00"),
                payoutDate = LocalDate.now(),
                truthScore = 95,
                scoreMessage = "Great achievement!",
            )
        }
    }

    @Test
    fun `should create certificate type game document`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 104.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "John",
                lastName = "Doe",
                studentTier = StudentTier.MASTERCLASS,
            )
        }

        every {
            zapierService.newGameDocument(
                studentName = any(),
                gameDocumentId = any(),
                gameDocumentType = any(),
                payoutAmount = any(),
                payoutDate = any(),
                truthScore = any(),
                scoreMessage = any(),
            )
        } just Runs

        val result = underTest.handle(
            defaultCommand(
                studentId = user.id,
                type = GameDocumentType.CERTIFICATE,
                payoutAmount = null,
                reachedLevel = GameLevel.THREE,
                scoreMessage = "Certificate awarded!",
                truthScore = 100,
            ),
        )

        gameDocumentRepository.findByIdOrNull(result.id)!!.run {
            studentId shouldBe user.id
            type shouldBe GameDocumentType.CERTIFICATE
            issuingCompany shouldBe IssuingCompany.FOR_TRADERS
            payoutAmount shouldBe null
            previousLevel shouldBe GameLevel.ONE
            reachedLevel shouldBe GameLevel.THREE
            payoutDate shouldBe LocalDate.now()
            truthScore shouldBe 100
            scoreMessage shouldBe "Certificate awarded!"
            state shouldBe GameDocumentApprovalState.WAITING
            denyMessage shouldBe null
        }

        verify {
            zapierService.newGameDocument(
                studentName = "John Doe",
                gameDocumentId = result.id,
                gameDocumentType = GameDocumentType.CERTIFICATE,
                payoutAmount = null,
                payoutDate = LocalDate.now(),
                truthScore = 100,
                scoreMessage = "Certificate awarded!",
            )
        }
    }

    @Test
    fun `should throw exception when payout amount is null for payout document`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 107.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Attempt to create with negative payout amount
        shouldThrow<GameDocumentPayoutAmountIsRequiredException> {
            underTest.handle(
                defaultCommand(
                    studentId = user.id,
                    type = GameDocumentType.PAYOUT,
                    payoutAmount = null,
                ),
            )
        }
    }

    @Test
    fun `should throw exception when payout amount is negative`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 107.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Attempt to create with negative payout amount
        shouldThrow<GameDocumentPayoutAmountCannotBeNegativeException> {
            underTest.handle(
                defaultCommand(
                    studentId = user.id,
                    type = GameDocumentType.PAYOUT,
                    payoutAmount = BigDecimal("-100.00"),
                ),
            )
        }
    }

    @Test
    fun `should throw exception when truth score is negative`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 108.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Attempt to create with negative truth score
        shouldThrow<GameDocumentTruthScoreCannotBeNegativeException> {
            underTest.handle(
                defaultCommand(
                    studentId = user.id,
                    truthScore = -10,
                ),
            )
        }
    }

    private fun defaultCommand(
        studentId: UUID,
        type: GameDocumentType = GameDocumentType.PAYOUT,
        issuingCompany: IssuingCompany = IssuingCompany.FOR_TRADERS,
        payoutAmount: BigDecimal? = BigDecimal("1000.00"),
        reachedLevel: GameLevel = GameLevel.TWO,
        payoutDate: LocalDate = LocalDate.now(),
        truthScore: Int = 95,
        scoreMessage: String? = "Great achievement!",
    ) = CreateGameDocumentCommand(
        studentId = studentId,
        type = type,
        issuingCompany = issuingCompany,
        payoutAmount = payoutAmount,
        reachedLevel = reachedLevel,
        payoutDate = payoutDate,
        truthScore = truthScore,
        scoreMessage = scoreMessage,
    )
}
