package com.cleevio.fundedmind.application.module.user.trader.service

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.domain.user.trader.TraderRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class CreateCrmTraderPropertyServiceTest @Autowired constructor(
    private val underTest: CreateCrmTraderPropertyService,
    private val traderRepository: TraderRepository,
) : IntegrationTest() {

    @Test
    fun `invoke should create custom property and update trader's hubspot property name`() {
        // Given
        val trader = dataHelper.getTrader(
            id = 1113.toUUID(),
            firstName = "John",
            lastName = "Doe",
        )
        every { hubspotService.createCustomerTextProperty(any(), any()) } just runs

        // When
        underTest.generateTraderCrmProperty(1113.toUUID())

        // Then
        verify {
            hubspotService.createCustomerTextProperty("mentor_00001113", "<PERSON><PERSON> John Doe")
        }
        val updatedTrader = traderRepository.getReferenceById(1113.toUUID())
        updatedTrader.hubspotPropertyNameOrThrow shouldBe "mentor_00001113"
    }
}
