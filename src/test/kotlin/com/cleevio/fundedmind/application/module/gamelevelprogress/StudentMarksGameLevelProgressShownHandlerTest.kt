package com.cleevio.fundedmind.application.module.gamelevelprogress

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamelevelprogress.command.StudentMarksGameLevelProgressShownCommand
import com.cleevio.fundedmind.application.module.gamelevelprogress.exception.GameLevelProgressNotFoundException
import com.cleevio.fundedmind.application.module.gamelevelprogress.exception.GameLevelProgressNotRelatedToStudentException
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.gamelevelprogress.GameLevelProgressRepository
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class StudentMarksGameLevelProgressShownHandlerTest @Autowired constructor(
    private val underTest: StudentMarksGameLevelProgressShownHandler,
    private val gameLevelProgressRepository: GameLevelProgressRepository,
) : IntegrationTest() {

    @Test
    fun `should mark student game level progress as shown`() {
        // Given
        val student = dataHelper.getStudent(id = 1.toUUID())
        val progress = dataHelper.getGameLevelProgress(
            id = 1.toUUID(),
            studentId = student.id,
            gameLevel = GameLevel.FIVE,
            achievedAt = "2024-01-01T10:00:00Z".toInstant(),
        )

        // When
        underTest.handle(
            StudentMarksGameLevelProgressShownCommand(
                gameLevelProgressId = progress.id,
                studentId = student.id,
            ),
        )

        // Then
        gameLevelProgressRepository.findByIdOrNull(progress.id)!!.run {
            shown shouldBe true
        }
    }

    @Test
    fun `should pass if marking already shown student game level progress`() {
        // Given
        val student = dataHelper.getStudent(id = 1.toUUID())
        val progress = dataHelper.getGameLevelProgress(
            id = 1.toUUID(),
            studentId = student.id,
            gameLevel = GameLevel.FIVE,
            achievedAt = "2024-01-01T10:00:00Z".toInstant(),
            entityModifier = { it.markAsShown() },
        )

        // When
        underTest.handle(
            StudentMarksGameLevelProgressShownCommand(
                gameLevelProgressId = progress.id,
                studentId = student.id,
            ),
        )

        // Then
        gameLevelProgressRepository.findByIdOrNull(progress.id)!!.run {
            shown shouldBe true // no change
        }
    }

    @Test
    fun `should throw exception when student game level progress not found`() {
        shouldThrow<GameLevelProgressNotFoundException> {
            underTest.handle(
                StudentMarksGameLevelProgressShownCommand(
                    gameLevelProgressId = 999.toUUID(),
                    studentId = 1.toUUID(),
                ),
            )
        }
    }

    @Test
    fun `should throw if progress is not related to student`() {
        // Given
        val progress = dataHelper.getGameLevelProgress(
            id = 1.toUUID(),
            studentId = dataHelper.getStudent(id = 1.toUUID()).id,
        )

        // When
        shouldThrow<GameLevelProgressNotRelatedToStudentException> {
            underTest.handle(
                StudentMarksGameLevelProgressShownCommand(
                    gameLevelProgressId = progress.id,
                    studentId = 999.toUUID(),
                ),
            )
        }
    }
}
