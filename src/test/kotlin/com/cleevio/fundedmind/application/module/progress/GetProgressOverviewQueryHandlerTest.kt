package com.cleevio.fundedmind.application.module.progress

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.progress.query.GetProgressOverviewQuery
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.course.Course
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetProgressOverviewQueryHandlerTest @Autowired constructor(
    private val underTest: GetProgressOverviewQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should get progress overview - user has finished some lessons`() {
        // given

        val user1 = dataHelper.getAppUser(id = 1.toUUID()) // finished all lessons
        val user2 = dataHelper.getAppUser(id = 2.toUUID()) // finished some lessons
        val user3 = dataHelper.getAppUser(id = 3.toUUID()) // finished no lessons

        val trader1 = dataHelper.getTrader(id = 1.toUUID())
        val trader2 = dataHelper.getTrader(id = 2.toUUID())

        // course with 2 modules (module 1 - 2 lessons, module 2 - 1 lesson)
        dataHelper.getCourse(traderId = trader1.id, courseCategory = CourseCategory.BASECAMP, entityModifier = {
            it.createPicturesAndPublish()
        }).also { course ->
            dataHelper.getCourseModule(courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id).also { lesson1 ->
                    dataHelper.getLessonProgress(
                        userId = user1.id,
                        lessonId = lesson1.id,
                        entityModifier = { it.finish() },
                    )
                    dataHelper.getLessonProgress(
                        userId = user2.id,
                        lessonId = lesson1.id,
                        entityModifier = { it.finish() },
                    )
                }
                dataHelper.getLesson(courseModuleId = module1.id).also { lesson2 ->
                    dataHelper.getLessonProgress(
                        userId = user1.id,
                        lessonId = lesson2.id,
                        entityModifier = { it.finish() },
                    )
                }
            }

            dataHelper.getCourseModule(courseId = course.id).also { module2 ->
                dataHelper.getLesson(courseModuleId = module2.id).also { lesson ->
                    dataHelper.getLessonProgress(
                        userId = user1.id,
                        lessonId = lesson.id,
                        entityModifier = { it.finish() },
                    )
                }
            }
        }
        // course with 1 module (1 lesson)
        dataHelper.getCourse(traderId = trader1.id, courseCategory = CourseCategory.TRADING_BASICS, entityModifier = {
            it.createPicturesAndPublish()
        }).also { course ->
            dataHelper.getCourseModule(courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id).also { lesson1 ->
                    dataHelper.getLessonProgress(
                        userId = user1.id,
                        lessonId = lesson1.id,
                        entityModifier = { it.finish() },
                    )
                }
            }
        }
        // course with 1 module (1 lesson)
        dataHelper.getCourse(traderId = trader1.id, courseCategory = CourseCategory.STRATEGY, entityModifier = {
            it.createPicturesAndPublish()
        }).also { course ->
            dataHelper.getCourseModule(courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id).also { lesson1 ->
                    dataHelper.getLessonProgress(
                        userId = user1.id,
                        lessonId = lesson1.id,
                        entityModifier = { it.finish() },
                    )
                    dataHelper.getLessonProgress(
                        userId = user2.id,
                        lessonId = lesson1.id,
                        entityModifier = { it.finish() },
                    )
                }
            }
        }
        // course with no modules
        dataHelper.getCourse(traderId = trader2.id, courseCategory = CourseCategory.STRATEGY, entityModifier = {
            it.createPicturesAndPublish()
        })
        // course with no modules
        dataHelper.getCourse(traderId = trader1.id, courseCategory = CourseCategory.RECORDING, entityModifier = {
            it.createPicturesAndPublish()
        })
        // course with 2 modules (module 1 - 1 lesson, module 2 - 1 lesson)
        dataHelper.getCourse(traderId = trader1.id, courseCategory = CourseCategory.ADD_ON, entityModifier = {
            it.createPicturesAndPublish()
        }).also { course ->
            dataHelper.getCourseModule(courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id).also { lesson1 ->
                    dataHelper.getLessonProgress(
                        userId = user1.id,
                        lessonId = lesson1.id,
                        entityModifier = { it.finish() },
                    )
                    dataHelper.getLessonProgress(
                        userId = user2.id,
                        lessonId = lesson1.id,
                        entityModifier = { it.finish() },
                    )
                }
            }
            dataHelper.getCourseModule(courseId = course.id).also { module2 ->
                dataHelper.getLesson(courseModuleId = module2.id).also { lesson ->
                    dataHelper.getLessonProgress(
                        userId = user1.id,
                        lessonId = lesson.id,
                        entityModifier = { it.finish() },
                    )
                }
            }
        }
        // course with 1 module (1 lesson)
        dataHelper.getCourse(traderId = trader2.id, courseCategory = CourseCategory.ADD_ON, entityModifier = {
            it.createPicturesAndPublish()
        }).also { course ->
            dataHelper.getCourseModule(courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id).also { lesson1 ->
                    dataHelper.getLessonProgress(
                        userId = user1.id,
                        lessonId = lesson1.id,
                        entityModifier = { it.finish() },
                    )
                }
            }
        }
        // course with 1 module (1 lesson)
        dataHelper.getCourse(traderId = trader1.id, courseCategory = CourseCategory.EXCLUSIVE, entityModifier = {
            it.createPicturesAndPublish()
        }).also { course ->
            dataHelper.getCourseModule(courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id).also { lesson1 ->
                    dataHelper.getLessonProgress(
                        userId = user1.id,
                        lessonId = lesson1.id,
                        entityModifier = { it.finish() },
                    )
                }
            }
        }
        // course with 1 module (2 lessons)
        dataHelper.getCourse(traderId = trader2.id, courseCategory = CourseCategory.EXCLUSIVE, entityModifier = {
            it.createPicturesAndPublish()
        }).also { course ->
            dataHelper.getCourseModule(courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id).also { lesson1 ->
                    dataHelper.getLessonProgress(
                        userId = user1.id,
                        lessonId = lesson1.id,
                        entityModifier = { it.finish() },
                    )
                }
                dataHelper.getLesson(courseModuleId = module1.id).also { lesson2 ->
                    dataHelper.getLessonProgress(
                        userId = user1.id,
                        lessonId = lesson2.id,
                        entityModifier = { it.finish() },
                    )
                    dataHelper.getLessonProgress(
                        userId = user2.id,
                        lessonId = lesson2.id,
                        entityModifier = { it.finish() },
                    )
                }
            }
        }
        // unpublished course with 2 lessons - do not include in overview
        dataHelper.getCourse(traderId = trader1.id, courseCategory = CourseCategory.EXCLUSIVE).also { course ->
            dataHelper.getCourseModule(courseId = course.id).also { module1 ->
                dataHelper.getLesson(courseModuleId = module1.id)
                dataHelper.getLesson(courseModuleId = module1.id)
            }
        }

        // when/then - user1 with all lessons finished
        underTest.handle(GetProgressOverviewQuery(userId = user1.id)).overview.run {
            this[CourseCategory.BASECAMP]!!.run {
                courseCategory shouldBe CourseCategory.BASECAMP
                lessonsCount shouldBe 3
                lessonsFinishedCount shouldBe 3
                isFinished shouldBe true
            }
            this[CourseCategory.TRADING_BASICS]!!.run {
                courseCategory shouldBe CourseCategory.TRADING_BASICS
                lessonsCount shouldBe 1
                lessonsFinishedCount shouldBe 1
                isFinished shouldBe true
            }
            this[CourseCategory.STRATEGY]!!.run {
                courseCategory shouldBe CourseCategory.STRATEGY
                lessonsCount shouldBe 1
                lessonsFinishedCount shouldBe 1
                isFinished shouldBe true
            }
            this[CourseCategory.RECORDING]!!.run {
                courseCategory shouldBe CourseCategory.RECORDING
                lessonsCount shouldBe 0
                lessonsFinishedCount shouldBe 0
                isFinished shouldBe true
            }
            this[CourseCategory.ADD_ON]!!.run {
                courseCategory shouldBe CourseCategory.ADD_ON
                lessonsCount shouldBe 3
                lessonsFinishedCount shouldBe 3
                isFinished shouldBe true
            }
            this[CourseCategory.EXCLUSIVE]!!.run {
                courseCategory shouldBe CourseCategory.EXCLUSIVE
                lessonsCount shouldBe 3
                lessonsFinishedCount shouldBe 3
                isFinished shouldBe true
            }
        }

        // when/then - user2 with some lessons finished
        underTest.handle(GetProgressOverviewQuery(userId = user2.id)).overview.run {
            this[CourseCategory.BASECAMP]!!.run {
                courseCategory shouldBe CourseCategory.BASECAMP
                lessonsCount shouldBe 3
                lessonsFinishedCount shouldBe 1
                isFinished shouldBe false
            }
            this[CourseCategory.TRADING_BASICS]!!.run {
                courseCategory shouldBe CourseCategory.TRADING_BASICS
                lessonsCount shouldBe 1
                lessonsFinishedCount shouldBe 0
                isFinished shouldBe false
            }
            this[CourseCategory.STRATEGY]!!.run {
                courseCategory shouldBe CourseCategory.STRATEGY
                lessonsCount shouldBe 1
                lessonsFinishedCount shouldBe 1
                isFinished shouldBe true
            }
            this[CourseCategory.RECORDING]!!.run {
                courseCategory shouldBe CourseCategory.RECORDING
                lessonsCount shouldBe 0
                lessonsFinishedCount shouldBe 0
                isFinished shouldBe true
            }
            this[CourseCategory.ADD_ON]!!.run {
                courseCategory shouldBe CourseCategory.ADD_ON
                lessonsCount shouldBe 3
                lessonsFinishedCount shouldBe 1
                isFinished shouldBe false
            }
            this[CourseCategory.EXCLUSIVE]!!.run {
                courseCategory shouldBe CourseCategory.EXCLUSIVE
                lessonsCount shouldBe 3
                lessonsFinishedCount shouldBe 1
                isFinished shouldBe false
            }
        }

        // when/then - user3 with no lessons finished
        underTest.handle(GetProgressOverviewQuery(userId = user3.id)).overview.run {
            this[CourseCategory.BASECAMP]!!.run {
                courseCategory shouldBe CourseCategory.BASECAMP
                lessonsCount shouldBe 3
                lessonsFinishedCount shouldBe 0
                isFinished shouldBe false
            }
            this[CourseCategory.TRADING_BASICS]!!.run {
                courseCategory shouldBe CourseCategory.TRADING_BASICS
                lessonsCount shouldBe 1
                lessonsFinishedCount shouldBe 0
                isFinished shouldBe false
            }
            this[CourseCategory.STRATEGY]!!.run {
                courseCategory shouldBe CourseCategory.STRATEGY
                lessonsCount shouldBe 1
                lessonsFinishedCount shouldBe 0
                isFinished shouldBe false
            }
            this[CourseCategory.RECORDING]!!.run {
                courseCategory shouldBe CourseCategory.RECORDING
                lessonsCount shouldBe 0
                lessonsFinishedCount shouldBe 0
                isFinished shouldBe true
            }
            this[CourseCategory.ADD_ON]!!.run {
                courseCategory shouldBe CourseCategory.ADD_ON
                lessonsCount shouldBe 3
                lessonsFinishedCount shouldBe 0
                isFinished shouldBe false
            }
            this[CourseCategory.EXCLUSIVE]!!.run {
                courseCategory shouldBe CourseCategory.EXCLUSIVE
                lessonsCount shouldBe 3
                lessonsFinishedCount shouldBe 0
                isFinished shouldBe false
            }
        }
    }

    private fun Course.createPicturesAndPublish() = with(this) {
        changeIntroPictureDesktop(fileId = dataHelper.getImage(type = FileType.COURSE_DESKTOP_INTRO_PHOTO).id)
        changeIntroPictureMobile(fileId = dataHelper.getImage(type = FileType.COURSE_MOBILE_INTRO_PHOTO).id)
        publish()
    }
}
