package com.cleevio.fundedmind.application.module.mentoring

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.mentoring.query.TraderGetsTheirMentoringsQuery
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class TraderGetsTheirMentoringsQueryHandlerTest @Autowired constructor(
    private val underTest: TraderGetsTheirMentoringsQueryHandler,
) : IntegrationTest() {

    @Test
    fun `trader gets their mentorings - verify mappings`() {
        // given
        val trader1 = dataHelper.getTrader(id = 1.toUUID())
        val trader2 = dataHelper.getTrader(id = 2.toUUID())

        val student1 = dataHelper.getStudent(
            id = 1.toUUID(),
            firstName = "John",
            lastName = "Doe",
            phone = "123456789",
            entityModifier = {
                it.changeProfilePicture(
                    dataHelper.getImage(
                        type = FileType.STUDENT_PROFILE_PICTURE,
                        originalFileUrl = "url",
                        compressedFileUrl = "url-comp",
                        blurHash = "123",
                    ).id,
                )
            },
        ).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.STUDENT, email = "<EMAIL>")
        }

        val student2 = dataHelper.getStudent(
            id = 2.toUUID(),
            firstName = "Jane",
            lastName = "Dede",
            phone = "00000000",
            entityModifier = {
                it.changeProfilePicture(
                    dataHelper.getImage(
                        type = FileType.STUDENT_PROFILE_PICTURE,
                        originalFileUrl = "url2",
                        compressedFileUrl = "url2-comp",
                        blurHash = "456",
                    ).id,
                )
            },
        ).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.STUDENT, email = "<EMAIL>")
        }

        val student3 = dataHelper.getStudent(
            id = 3.toUUID(),
            firstName = "Jozef",
            lastName = "Henryk",
            phone = "1111111111",
        ).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.STUDENT, email = "<EMAIL>")
        }

        val product1 = dataHelper.getProduct(id = 1.toUUID(), traderId = trader1.id)
        val product2 = dataHelper.getProduct(id = 2.toUUID(), traderId = trader2.id)

        // fully used mentoring for product 1
        dataHelper.getMentoring(
            id = 1.toUUID(),
            studentId = student1.id,
            productId = product1.id,
            productName = "1",
            productAltDescription = "alt1",
            sessionCount = 5,
            useSessions = 5,
            expiresAt = "2025-02-01T23:59:59.000Z".toInstant(), // 01.02. 23:59:59
            createdTimestamp = "2025-01-01T10:00:00.000Z".toInstant(), // 01.01. 10:00
        )

        // partially used mentoring for product 1
        dataHelper.getMentoring(
            id = 2.toUUID(),
            studentId = student1.id,
            productId = product1.id,
            productName = "1",
            productAltDescription = "alt1",
            sessionCount = 5,
            useSessions = 3,
            expiresAt = "2025-04-01T23:59:59.000Z".toInstant(), // 01.04. 23:59:59
            createdTimestamp = "2025-03-01T10:00:00.000Z".toInstant(), // 01.03. 10:00
        )

        dataHelper.getMentoring(
            id = 3.toUUID(),
            studentId = student2.id,
            productId = product1.id,
            productName = "1",
            productAltDescription = "alt1",
            sessionCount = 10,
            useSessions = 0,
            expiresAt = "2025-03-01T23:59:59.000Z".toInstant(), // 01.03. 23:59:59
            createdTimestamp = "2025-02-01T10:00:00.000Z".toInstant(), // 01.02. 10:00
        )

        dataHelper.getMentoring(
            id = 4.toUUID(),
            studentId = student1.id,
            productId = product2.id, // product of different trader
        )

        dataHelper.getMentoring(
            id = 5.toUUID(),
            studentId = student3.id,
            productId = product1.id,
            productName = "1",
            productAltDescription = "alt1",
            sessionCount = 3,
            useSessions = 1,
            expiresAt = null,
            createdTimestamp = "2025-01-01T08:00:00.000Z".toInstant(), // 01.01. 08:00
        )

        // when
        val result = underTest.handle(
            TraderGetsTheirMentoringsQuery(traderId = trader1.id),
        )

        // then
        result.data shouldHaveSize 4

        result.data.run {
            this[0].run {
                mentoringId shouldBe 2.toUUID()
                boughtAt shouldBe "2025-03-01T10:00:00.000Z".toInstant()
                productId shouldBe 1.toUUID()
                productName shouldBe "1"
                productAltDescription shouldBe "alt1"
                expiresAt shouldBe "2025-04-01T23:59:59.000Z".toInstant()
                sessionCount shouldBe 5
                usedSessions shouldBe 3
                student.run {
                    studentId shouldBe 1.toUUID()
                    firstName shouldBe "John"
                    lastName shouldBe "Doe"
                    profilePicture!!.run {
                        imageOriginalUrl shouldBe "url"
                        imageCompressedUrl shouldBe "url-comp"
                        imageBlurHash shouldBe "123"
                    }
                    phone shouldBe "123456789"
                    email shouldBe "<EMAIL>"
                }
            }
            this[1].run {
                mentoringId shouldBe 3.toUUID()
                boughtAt shouldBe "2025-02-01T10:00:00.000Z".toInstant()
                productId shouldBe 1.toUUID()
                productName shouldBe "1"
                productAltDescription shouldBe "alt1"
                expiresAt shouldBe "2025-03-01T23:59:59.000Z".toInstant()
                sessionCount shouldBe 10
                usedSessions shouldBe 0
                student.run {
                    studentId shouldBe 2.toUUID()
                    firstName shouldBe "Jane"
                    lastName shouldBe "Dede"
                    profilePicture!!.run {
                        imageOriginalUrl shouldBe "url2"
                        imageCompressedUrl shouldBe "url2-comp"
                        imageBlurHash shouldBe "456"
                    }
                    phone shouldBe "00000000"
                    email shouldBe "<EMAIL>"
                }
            }
            this[2].run {
                mentoringId shouldBe 1.toUUID()
                boughtAt shouldBe "2025-01-01T10:00:00.000Z".toInstant()
                productId shouldBe 1.toUUID()
                productName shouldBe "1"
                productAltDescription shouldBe "alt1"
                expiresAt shouldBe "2025-02-01T23:59:59.000Z".toInstant()
                sessionCount shouldBe 5
                usedSessions shouldBe 5
                student.run {
                    studentId shouldBe 1.toUUID()
                    firstName shouldBe "John"
                    lastName shouldBe "Doe"
                    profilePicture!!.run {
                        imageOriginalUrl shouldBe "url"
                        imageCompressedUrl shouldBe "url-comp"
                        imageBlurHash shouldBe "123"
                    }
                    phone shouldBe "123456789"
                    email shouldBe "<EMAIL>"
                }
            }
            this[3].run {
                mentoringId shouldBe 5.toUUID()
                boughtAt shouldBe "2025-01-01T08:00:00.000Z".toInstant()
                productId shouldBe 1.toUUID()
                productName shouldBe "1"
                productAltDescription shouldBe "alt1"
                expiresAt shouldBe null
                sessionCount shouldBe 3
                usedSessions shouldBe 1
                student.run {
                    studentId shouldBe 3.toUUID()
                    firstName shouldBe "Jozef"
                    lastName shouldBe "Henryk"
                    profilePicture shouldBe null
                    phone shouldBe "1111111111"
                    email shouldBe "<EMAIL>"
                }
            }
        }
    }

    @Test
    fun `trader gets their mentorings but has no mentorings should return empty list`() {
        // given
        val trader1 = dataHelper.getTrader(id = 1.toUUID())
        val trader2 = dataHelper.getTrader(id = 2.toUUID())

        dataHelper.getMentoring(
            id = 1.toUUID(),
            studentId = dataHelper.getStudent(id = 1.toUUID()).id,
            productId = dataHelper.getProduct(traderId = trader2.id).id, // product of different trader
        )

        // when
        val result = underTest.handle(
            TraderGetsTheirMentoringsQuery(traderId = trader1.id),
        )

        // then
        result.data.size shouldBe 0
    }
}
