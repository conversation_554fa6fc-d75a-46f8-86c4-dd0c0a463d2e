package com.cleevio.fundedmind.application.module.gamelevelprogress

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamelevelprogress.query.StudentGetsGameLevelProgressQuery
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.shouldBeAbout
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class StudentGetsGameLevelProgressQueryHandlerTest @Autowired constructor(
    private val underTest: StudentGetsGameLevelProgressQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should return student game level progress list ordered by achieved at desc`() {
        // Given
        val student = dataHelper.getStudent(id = 1.toUUID())
        val progress1 = dataHelper.getGameLevelProgress(
            id = 1.toUUID(),
            studentId = student.id,
            gameLevel = GameLevel.FIVE,
            achievedAt = "2024-01-01T10:00:00Z".toInstant(),
            entityModifier = { it.markAsShown() },
        )
        val progress2 = dataHelper.getGameLevelProgress(
            id = 2.toUUID(),
            studentId = student.id,
            gameLevel = GameLevel.SIX,
            achievedAt = "2024-01-02T10:00:00Z".toInstant(),
            entityModifier = { it.markAsShown() },
        )
        val progress3 = dataHelper.getGameLevelProgress(
            id = 3.toUUID(),
            studentId = student.id,
            gameLevel = GameLevel.SEVEN,
            achievedAt = "2024-01-03T10:00:00Z".toInstant(),
        )

        // When
        val result = underTest.handle(
            StudentGetsGameLevelProgressQuery(studentId = student.id),
        )

        // Then
        result.data shouldHaveSize 3
        result.data[0].run {
            id shouldBe progress3.id
            level shouldBe GameLevel.SEVEN
            achievedAt shouldBe "2024-01-03T10:00:00Z".toInstant()
            shown shouldBe false
        }
        result.data[1].run {
            id shouldBe progress2.id
            level shouldBe GameLevel.SIX
            achievedAt shouldBeAbout "2024-01-02T10:00:00Z".toInstant()
            shown shouldBe true
        }
        result.data[2].run {
            id shouldBe progress1.id
            level shouldBe GameLevel.FIVE
            achievedAt shouldBe "2024-01-01T10:00:00Z".toInstant()
            shown shouldBe true
        }
    }

    @Test
    fun `should return empty list when student has no game level progress`() {
        // Given
        val student = dataHelper.getStudent(id = 1.toUUID())

        // When
        val result = underTest.handle(
            StudentGetsGameLevelProgressQuery(studentId = student.id),
        )

        // Then
        result.data shouldHaveSize 0
    }

    @Test
    fun `should return only progress for specified student`() {
        // Given
        val student1 = dataHelper.getStudent(id = 1.toUUID())
        val student2 = dataHelper.getStudent(id = 2.toUUID())

        dataHelper.getGameLevelProgress(
            id = 1.toUUID(),
            studentId = student1.id,
            gameLevel = GameLevel.FIVE,
        )

        dataHelper.getGameLevelProgress(
            id = 2.toUUID(),
            studentId = student2.id, // different student
            gameLevel = GameLevel.SIX,
        )

        // When
        val result = underTest.handle(
            StudentGetsGameLevelProgressQuery(studentId = student1.id),
        )

        // Then
        result.data shouldHaveSize 1
        result.data.single().id shouldBe 1.toUUID()
    }
}
