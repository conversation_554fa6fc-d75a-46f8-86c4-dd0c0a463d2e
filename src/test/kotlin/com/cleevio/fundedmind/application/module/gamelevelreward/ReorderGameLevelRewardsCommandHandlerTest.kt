package com.cleevio.fundedmind.application.module.gamelevelreward

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamelevelreward.command.ReorderGameLevelRewardsCommand
import com.cleevio.fundedmind.application.module.gamelevelreward.command.ReorderGameLevelRewardsCommand.GameLevelRewardOrderingInput
import com.cleevio.fundedmind.domain.gamelevelreward.GameLevelRewardRepository
import com.cleevio.fundedmind.domain.gamelevelreward.exception.ActiveGameLevelRewardsMismatchException
import com.cleevio.fundedmind.domain.gamelevelreward.exception.GameLevelRewardOrderCannotBeNegativeException
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class ReorderGameLevelRewardsCommandHandlerTest @Autowired constructor(
    private val underTest: ReorderGameLevelRewardsCommandHandler,
    private val gameLevelRewardRepository: GameLevelRewardRepository,
) : IntegrationTest() {

    @Test
    fun `should reorder game level rewards`() {
        // given
        dataHelper.getGameLevelReward(id = 1.toUUID(), listingOrder = 1, name = "Reward 1")
        dataHelper.getGameLevelReward(id = 2.toUUID(), listingOrder = 2, name = "Reward 2")
        dataHelper.getGameLevelReward(id = 3.toUUID(), listingOrder = 3, name = "Reward 3")

        // when
        underTest.handle(
            ReorderGameLevelRewardsCommand(
                gameLevelRewardOrderings = listOf(
                    GameLevelRewardOrderingInput(gameLevelRewardId = 1.toUUID(), newListingOrder = 2),
                    GameLevelRewardOrderingInput(gameLevelRewardId = 2.toUUID(), newListingOrder = 3),
                    GameLevelRewardOrderingInput(gameLevelRewardId = 3.toUUID(), newListingOrder = 1),
                ),
            ),
        )

        // then
        val gameLevelRewards = gameLevelRewardRepository.findAll()
        gameLevelRewards shouldHaveSize 3
        gameLevelRewards.first { it.id == 1.toUUID() }.listingOrder shouldBe 2
        gameLevelRewards.first { it.id == 2.toUUID() }.listingOrder shouldBe 3
        gameLevelRewards.first { it.id == 3.toUUID() }.listingOrder shouldBe 1
    }

    @Test
    fun `should reorder game level rewards even if display order is not unique`() {
        // given
        dataHelper.getGameLevelReward(id = 1.toUUID(), listingOrder = 1, name = "Reward 1")
        dataHelper.getGameLevelReward(id = 2.toUUID(), listingOrder = 2, name = "Reward 2")
        dataHelper.getGameLevelReward(id = 3.toUUID(), listingOrder = 3, name = "Reward 3")

        // when
        underTest.handle(
            ReorderGameLevelRewardsCommand(
                gameLevelRewardOrderings = listOf(
                    GameLevelRewardOrderingInput(gameLevelRewardId = 1.toUUID(), newListingOrder = 1),
                    GameLevelRewardOrderingInput(gameLevelRewardId = 2.toUUID(), newListingOrder = 1),
                    GameLevelRewardOrderingInput(gameLevelRewardId = 3.toUUID(), newListingOrder = 1),
                ),
            ),
        )

        // then
        val gameLevelRewards = gameLevelRewardRepository.findAll()
        gameLevelRewards shouldHaveSize 3
        gameLevelRewards.first { it.id == 1.toUUID() }.listingOrder shouldBe 1
        gameLevelRewards.first { it.id == 2.toUUID() }.listingOrder shouldBe 1
        gameLevelRewards.first { it.id == 3.toUUID() }.listingOrder shouldBe 1
    }

    @Test
    fun `should throw if there is a mismatch of game level rewards - reward is missing`() {
        // given
        dataHelper.getGameLevelReward(id = 1.toUUID(), listingOrder = 1, name = "Reward 1")
        dataHelper.getGameLevelReward(id = 2.toUUID(), listingOrder = 2, name = "Reward 2")
        dataHelper.getGameLevelReward(id = 3.toUUID(), listingOrder = 3, name = "Reward 3")

        // when & then
        shouldThrow<ActiveGameLevelRewardsMismatchException> {
            underTest.handle(
                ReorderGameLevelRewardsCommand(
                    gameLevelRewardOrderings = listOf(
                        GameLevelRewardOrderingInput(gameLevelRewardId = 1.toUUID(), newListingOrder = 2),
                        GameLevelRewardOrderingInput(gameLevelRewardId = 3.toUUID(), newListingOrder = 1),
                    ),
                ),
            )
        }
    }

    @Test
    fun `should throw if display order is negative`() {
        // given
        dataHelper.getGameLevelReward(id = 1.toUUID(), listingOrder = 1, name = "Reward 1")

        // when & then
        shouldThrow<GameLevelRewardOrderCannotBeNegativeException> {
            underTest.handle(
                ReorderGameLevelRewardsCommand(
                    gameLevelRewardOrderings = listOf(
                        GameLevelRewardOrderingInput(gameLevelRewardId = 1.toUUID(), newListingOrder = -1),
                    ),
                ),
            )
        }
    }
}
