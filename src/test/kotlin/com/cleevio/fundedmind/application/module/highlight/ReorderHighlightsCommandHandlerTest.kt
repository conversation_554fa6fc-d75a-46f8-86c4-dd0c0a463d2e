package com.cleevio.fundedmind.application.module.highlight

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.highlight.command.ReorderHighlightsCommand
import com.cleevio.fundedmind.application.module.highlight.command.ReorderHighlightsCommand.HighlightOrderingInput
import com.cleevio.fundedmind.domain.highlight.HighlightRepository
import com.cleevio.fundedmind.domain.highlight.exception.ActiveHighlightsMismatchException
import com.cleevio.fundedmind.domain.highlight.exception.HighlightOrderCannotBeNegativeException
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class ReorderHighlightsCommandHandlerTest @Autowired constructor(
    private val underTest: ReorderHighlightsCommandHandler,
    private val highlightRepository: HighlightRepository,
) : IntegrationTest() {

    @Test
    fun `should reorder highlights`() {
        // given
        dataHelper.getHighlight(id = 1.toUUID(), listingOrder = 1)
        dataHelper.getHighlight(id = 2.toUUID(), listingOrder = 2)
        dataHelper.getHighlight(id = 3.toUUID(), listingOrder = 3)

        // when
        underTest.handle(
            ReorderHighlightsCommand(
                highlightOrderings = listOf(
                    HighlightOrderingInput(highlightId = 1.toUUID(), newListingOrder = 2),
                    HighlightOrderingInput(highlightId = 2.toUUID(), newListingOrder = 3),
                    HighlightOrderingInput(highlightId = 3.toUUID(), newListingOrder = 1),
                ),
            ),
        )

        // then
        val highlights = highlightRepository.findAll()
        highlights shouldHaveSize 3
        highlights.first { it.id == 1.toUUID() }.listingOrder shouldBe 2
        highlights.first { it.id == 2.toUUID() }.listingOrder shouldBe 3
        highlights.first { it.id == 3.toUUID() }.listingOrder shouldBe 1
    }

    @Test
    fun `should reorder highlights even if display order is not unique`() {
        // given
        dataHelper.getHighlight(id = 1.toUUID(), listingOrder = 1)
        dataHelper.getHighlight(id = 2.toUUID(), listingOrder = 2)
        dataHelper.getHighlight(id = 3.toUUID(), listingOrder = 3)

        // when
        underTest.handle(
            ReorderHighlightsCommand(
                highlightOrderings = listOf(
                    HighlightOrderingInput(highlightId = 1.toUUID(), newListingOrder = 1),
                    HighlightOrderingInput(highlightId = 2.toUUID(), newListingOrder = 1),
                    HighlightOrderingInput(highlightId = 3.toUUID(), newListingOrder = 1),
                ),
            ),
        )

        // then
        val highlights = highlightRepository.findAll()
        highlights shouldHaveSize 3
        highlights.first { it.id == 1.toUUID() }.listingOrder shouldBe 1
        highlights.first { it.id == 2.toUUID() }.listingOrder shouldBe 1
        highlights.first { it.id == 3.toUUID() }.listingOrder shouldBe 1
    }

    @Test
    fun `should throw if there is a mismatch of highlights - highlight is missing`() {
        dataHelper.getHighlight(id = 1.toUUID(), listingOrder = 1)
        dataHelper.getHighlight(id = 2.toUUID(), listingOrder = 2)

        shouldThrow<ActiveHighlightsMismatchException> {
            underTest.handle(
                ReorderHighlightsCommand(
                    highlightOrderings = listOf(
                        HighlightOrderingInput(highlightId = 1.toUUID(), newListingOrder = 1),
                    ),
                ),
            )
        }
    }

    @Test
    fun `should throw if display order is not positive or zero`() {
        dataHelper.getHighlight(id = 1.toUUID(), listingOrder = 1)

        shouldThrow<HighlightOrderCannotBeNegativeException> {
            underTest.handle(
                ReorderHighlightsCommand(
                    highlightOrderings = listOf(
                        HighlightOrderingInput(highlightId = 1.toUUID(), newListingOrder = -1),
                    ),
                ),
            )
        }
    }
}
