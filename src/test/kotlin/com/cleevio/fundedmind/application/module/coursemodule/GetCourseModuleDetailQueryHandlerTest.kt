package com.cleevio.fundedmind.application.module.coursemodule

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.coursemodule.query.GetCourseModuleDetailQuery
import com.cleevio.fundedmind.domain.common.AppButtonWithLink
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetCourseModuleDetailQueryHandlerTest @Autowired constructor(
    private val underTest: GetCourseModuleDetailQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should get course module - with pictures - verify mappings`() {
        // given
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)
        dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = 1.toUUID(),
            title = "Course Module",
            description = "Description",
            shortDescription = "Short Description",
            rewardDescription = "Reward Description",
            rewardCouponCode = null,
            rewardButton = null,
            comingSoon = false,
            entityModifier = {
                it.changeThumbnailPictureDesktop(
                    fileId = dataHelper.getImage(
                        type = FileType.COURSE_MODULE_DESKTOP_THUMBNAIL,
                        originalFileUrl = "desktop-url",
                        compressedFileUrl = "desktop-url-comp",
                        blurHash = "123",
                    ).id,
                )
                it.changeThumbnailPictureMobile(
                    fileId = dataHelper.getImage(
                        type = FileType.COURSE_MODULE_MOBILE_THUMBNAIL,
                        originalFileUrl = "mobile-url",
                        compressedFileUrl = "mobile-url-comp",
                        blurHash = "456",
                    ).id,
                )
                it.changeRewardPicture(
                    fileId = dataHelper.getImage(
                        type = FileType.COURSE_MODULE_REWARD_PICTURE,
                        originalFileUrl = "reward-url",
                        compressedFileUrl = "reward-url-comp",
                        blurHash = "789",
                    ).id,
                )
            },
        )

        // when
        val result = underTest.handle(
            GetCourseModuleDetailQuery(
                courseId = 1.toUUID(),
                courseModuleId = 1.toUUID(),
            ),
        )

        result.run {
            courseModuleId shouldBe 1.toUUID()
            courseId shouldBe 1.toUUID()
            title shouldBe "Course Module"
            description shouldBe "Description"
            shortDescription shouldBe "Short Description"
            thumbnailDesktop shouldNotBe null
            thumbnailDesktop!!.run {
                imageOriginalUrl shouldBe "desktop-url"
                imageCompressedUrl shouldBe "desktop-url-comp"
                imageBlurHash shouldBe "123"
            }
            thumbnailMobile shouldNotBe null
            thumbnailMobile!!.run {
                imageOriginalUrl shouldBe "mobile-url"
                imageCompressedUrl shouldBe "mobile-url-comp"
                imageBlurHash shouldBe "456"
            }
            rewardPicture shouldNotBe null
            rewardPicture!!.run {
                imageOriginalUrl shouldBe "reward-url"
                imageCompressedUrl shouldBe "reward-url-comp"
                imageBlurHash shouldBe "789"
            }
            rewardDescription shouldBe "Reward Description"
            rewardCouponCode shouldBe null
            rewardButton shouldBe null
            comingSoon shouldBe false
        }
    }

    @Test
    fun `should get course module - with button`() {
        // given
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)
        dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = 1.toUUID(),
            rewardButton = AppButtonWithLink(
                text = "Button",
                color = Color.BLUE,
                linkUrl = "url",
            ),
        )

        // when
        val result = underTest.handle(
            GetCourseModuleDetailQuery(
                courseId = 1.toUUID(),
                courseModuleId = 1.toUUID(),
            ),
        )

        result.rewardButton!!.run {
            text shouldBe "Button"
            color shouldBe Color.BLUE
            linkUrl shouldBe "url"
        }
    }

    @Test
    fun `should get course module - with coupon code`() {
        // given
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)
        dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = 1.toUUID(),
            rewardCouponCode = "1234",
        )

        // when
        val result = underTest.handle(
            GetCourseModuleDetailQuery(
                courseId = 1.toUUID(),
                courseModuleId = 1.toUUID(),
            ),
        )

        result.rewardCouponCode shouldBe "1234"
    }
}
