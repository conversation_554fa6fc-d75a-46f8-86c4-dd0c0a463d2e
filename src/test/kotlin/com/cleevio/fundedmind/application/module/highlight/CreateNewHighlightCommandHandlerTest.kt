package com.cleevio.fundedmind.application.module.highlight

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.command.AppButtonInput
import com.cleevio.fundedmind.application.module.highlight.command.CreateNewHighlightCommand
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.highlight.HighlightRepository
import com.cleevio.fundedmind.domain.highlight.exception.HighlightButtonWithoutLinkException
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class CreateNewHighlightCommandHandlerTest @Autowired constructor(
    private val underTest: CreateNewHighlightCommandHandler,
    private val highlightRepository: HighlightRepository,
) : IntegrationTest() {

    @Test
    fun `should create new highlight without link and button`() {
        val result = underTest.handle(
            defaultCommand(linkUrl = null, button = null),
        )

        val meeting = highlightRepository.findByIdOrNull(result.id)!!

        meeting.run {
            title shouldBe "Test"
            description shouldBe "Description"
            visibleToTiers shouldBe listOf(StudentTier.BASECAMP)
            visibleToDiscordUsers shouldBe true
            linkUrl shouldBe null
            button shouldBe null
        }
    }

    @Test
    fun `should create new highlight with link and without button`() {
        val result = underTest.handle(
            defaultCommand(linkUrl = "linkUrl", button = null),
        )

        val meeting = highlightRepository.findByIdOrNull(result.id)!!

        meeting.run {
            linkUrl shouldBe "linkUrl"
            button shouldBe null
        }
    }

    @Test
    fun `should create new highlight with link and with button`() {
        val result = underTest.handle(
            defaultCommand(
                linkUrl = "linkUrl",
                button = AppButtonInput(
                    text = "Button",
                    color = Color.BLUE,
                ),
            ),
        )

        val meeting = highlightRepository.findByIdOrNull(result.id)!!

        meeting.run {
            linkUrl shouldBe "linkUrl"
            button!!.run {
                text shouldBe "Button"
                color shouldBe Color.BLUE
            }
        }
    }

    @Test
    fun `should create highlight without title and description`() {
        val result = underTest.handle(
            defaultCommand(
                title = null,
                description = null,
                linkUrl = null,
                button = null,
            ),
        )

        val meeting = highlightRepository.findByIdOrNull(result.id)!!

        meeting.run {
            title shouldBe null
            description shouldBe null
            linkUrl shouldBe null
            button shouldBe null
        }
    }

    @Test
    fun `should throw when creating new highlight with button and without link`() {
        shouldThrow<HighlightButtonWithoutLinkException> {
            underTest.handle(
                defaultCommand(
                    linkUrl = null,
                    button = AppButtonInput(
                        text = "Button",
                        color = Color.BLUE,
                    ),
                ),
            )
        }
    }

    private fun defaultCommand(
        title: String? = "Test",
        description: String? = "Description",
        visibleToTiers: List<StudentTier> = listOf(StudentTier.BASECAMP),
        visibleToDiscordUsers: Boolean = true,
        linkUrl: String?,
        button: AppButtonInput?,
    ) = CreateNewHighlightCommand(
        title = title,
        description = description,
        visibleToTiers = visibleToTiers,
        visibleToDiscordUsers = visibleToDiscordUsers,
        linkUrl = linkUrl,
        button = button,
    )
}
