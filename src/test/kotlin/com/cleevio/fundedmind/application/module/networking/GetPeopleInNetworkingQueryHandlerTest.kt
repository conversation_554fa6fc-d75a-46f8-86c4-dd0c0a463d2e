package com.cleevio.fundedmind.application.module.networking

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.networking.query.GetPeopleInNetworkingQuery
import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetPeopleInNetworkingQueryHandlerTest @Autowired constructor(
    private val underTest: GetPeopleInNetworkingQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should count people in networking with location`() {
        // given
        val userId = 1.toUUID()
        dataHelper.getAppUser(id = userId)

        // Onboarding 1 with location
        dataHelper.getStudentForOnboarding(
            id = 1.toUUID(),
            entityModifier = {
                it.saveUserSurvey(
                    firstName = "Jane",
                    lastName = "Smith",
                    phone = "+987654321",
                    biography = "Bio",
                    country = Country.CZ,
                    firstNameVocative = "Jane",
                    lastNameVocative = "Smith",
                    locationId = dataHelper.getUserLocation().id,
                )
            },
        )
        // Onboarding without location
        dataHelper.getStudentForOnboarding(id = 2.toUUID())

        // Student 1 (with values from Onboarding 1) - MASTERCLASS tier
        dataHelper.getStudent(
            id = 1.toUUID(),
            locationId = dataHelper.getUserLocation().id,
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.MASTERCLASS,
        )

        // Student without location - MASTERCLASS tier
        dataHelper.getStudent(
            id = 2.toUUID(),
            locationId = null,
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.MASTERCLASS,
        )

        // Student 3 with location and disabled networking visibility - MASTERCLASS tier
        dataHelper.getStudent(
            id = 3.toUUID(),
            locationId = dataHelper.getUserLocation().id,
            networkingVisibility = NetworkingVisibility.DISABLED,
            studentTier = StudentTier.MASTERCLASS,
        )

        // Student 4 without location and disabled networking visibility - MASTERCLASS tier
        dataHelper.getStudent(
            id = 4.toUUID(),
            locationId = null,
            networkingVisibility = NetworkingVisibility.DISABLED,
            studentTier = StudentTier.MASTERCLASS,
        )

        // Student 5 with location and enabled networking visibility but BASECAMP tier (should be excluded)
        dataHelper.getStudent(
            id = 5.toUUID(),
            locationId = dataHelper.getUserLocation().id,
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.BASECAMP,
        )

        // Trader with location
        dataHelper.getTrader(
            id = 1.toUUID(),
            locationId = dataHelper.getUserLocation().id,
            networkingVisibility = NetworkingVisibility.ENABLED,
        )

        // Trader with location and disabled networking visibility
        dataHelper.getTrader(
            id = 2.toUUID(),
            locationId = dataHelper.getUserLocation().id,
            networkingVisibility = NetworkingVisibility.DISABLED,
        )

        // Trader without location
        dataHelper.getTrader(
            id = 3.toUUID(),
            locationId = null,
            networkingVisibility = NetworkingVisibility.ENABLED,
        )

        // Trader without location and disabled networking visibility
        dataHelper.getTrader(
            id = 4.toUUID(),
            locationId = null,
            networkingVisibility = NetworkingVisibility.DISABLED,
        )

        // when
        val result = underTest.handle(GetPeopleInNetworkingQuery(userId = userId))

        // then
        // count only students (excluding BASECAMP tier) and traders with location and enabled networking visibility
        result.count shouldBe 2
    }
}
