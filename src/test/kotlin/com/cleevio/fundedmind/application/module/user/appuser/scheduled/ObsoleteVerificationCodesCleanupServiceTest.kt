package com.cleevio.fundedmind.application.module.user.appuser.scheduled

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.user.appuser.service.ObsoleteVerificationCodesCleanupService
import com.cleevio.fundedmind.domain.user.appuser.VerificationCodeRepository
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit

class ObsoleteVerificationCodesCleanupServiceTest @Autowired constructor(
    private val underTest: ObsoleteVerificationCodesCleanupService,
    private val verificationCodeRepository: VerificationCodeRepository,
) : IntegrationTest() {

    @Test
    fun `should delete expired verification codes`() {
        dataHelper.getAppUser(id = 1.toUUID()).also {
            dataHelper.getVerificationCode(
                id = 1.toUUID(),
                appUserId = it.id,
                code = "1234",
                expiresAt = Instant.now().minus(1, ChronoUnit.MINUTES),
            )
            dataHelper.getVerificationCode(
                id = 2.toUUID(),
                appUserId = it.id,
                code = "2345",
                expiresAt = Instant.now().minus(10, ChronoUnit.MINUTES),
            )
        }
        dataHelper.getAppUser(id = 2.toUUID()).also {
            dataHelper.getVerificationCode(
                id = 3.toUUID(),
                appUserId = it.id,
                code = "3456",
                expiresAt = Instant.now().minus(1, ChronoUnit.DAYS),
            )

            dataHelper.getVerificationCode(
                id = 4.toUUID(),
                appUserId = it.id,
                code = "4567",
                expiresAt = Instant.now().plus(1, ChronoUnit.DAYS),
            )
        }

        underTest.cleanup()

        verificationCodeRepository.findAll().run {
            this shouldHaveSize 1
            single().run {
                this.id shouldBe 4.toUUID()
                this.code shouldBe "4567"
            }
        }
    }

    @Test
    fun `should delete used and old verification codes`() {
        // Create a user
        val user = dataHelper.getAppUser(id = 1.toUUID())

        // Create a used verification code that is older than the default threshold (30 days)
        val oldUsedCode = dataHelper.getVerificationCode(
            id = 1.toUUID(),
            code = "1111",
            appUserId = user.id,
            entityModifier = {
                it.markAsUsed(now = "2025-01-01T00:00:00Z".toInstant()) // USED 1.1.2025
            },
        )

        // Create a used verification code that is newer than the threshold
        val recentUsedCode = dataHelper.getVerificationCode(
            id = 2.toUUID(),
            appUserId = user.id,
            code = "2222",
            entityModifier = {
                it.markAsUsed(now = "2025-01-14T00:00:00Z".toInstant()) // USED 14.1.2025
            },
        )

        // Create a valid (not used, not expired) verification code
        val validCode = dataHelper.getVerificationCode(
            id = 3.toUUID(),
            appUserId = user.id,
            code = "3333",
            expiresAt = Instant.now().plus(1, ChronoUnit.DAYS),
        )

        // create used and expired code
        dataHelper.getVerificationCode(
            id = 4.toUUID(),
            appUserId = user.id,
            code = "4444",
            expiresAt = Instant.now().minus(1, ChronoUnit.DAYS),
            entityModifier = {
                it.markAsUsed(now = "2025-01-01T00:00:00Z".toInstant()) // USED 01.1.2025
            },
        )

        // Run the cleanup on 15.1.2025
        underTest.cleanup(
            usedCodesOlderThan = Duration.of(7, ChronoUnit.DAYS),
            now = "2025-01-15T00:00:00Z".toInstant(), // NOW 15.1.2025
        )

        // Verify oldUsedCode was deleted and others remain
        verificationCodeRepository
            .findAll()
            .map { it.id } shouldContainExactlyInAnyOrder setOf(2.toUUID(), 3.toUUID())
    }
}
