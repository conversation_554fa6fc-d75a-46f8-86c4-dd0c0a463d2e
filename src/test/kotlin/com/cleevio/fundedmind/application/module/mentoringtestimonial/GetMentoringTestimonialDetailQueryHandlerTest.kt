package com.cleevio.fundedmind.application.module.mentoringtestimonial

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.mentoringtestimonial.query.GetMentoringTestimonialDetailQuery
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.mentoringtestimonial.exception.MentoringTestimonialNotFoundException
import com.cleevio.fundedmind.domain.mentoringtestimonial.exception.MentoringTestimonialNotRelatedToTraderException
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetMentoringTestimonialDetailQueryHandlerTest @Autowired constructor(
    private val underTest: GetMentoringTestimonialDetailQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should get testimonial detail`() {
        // given
        val trader = dataHelper.getTrader(id = 1.toUUID()).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.TRADER)
        }

        val testimonial = dataHelper.getMentoringTestimonial(
            traderId = trader.id,
            firstName = "John",
            lastName = "Doe",
            rating = 5,
            description = "Great mentoring experience!",
        )

        // when
        val result = underTest.handle(
            GetMentoringTestimonialDetailQuery(
                testimonialId = testimonial.id,
                traderId = trader.id,
            ),
        )

        // then
        result.run {
            id shouldBe testimonial.id
            traderId shouldBe testimonial.traderId
            firstName shouldBe "John"
            lastName shouldBe "Doe"
            rating shouldBe 5
            description shouldBe "Great mentoring experience!"
            picture shouldBe null
        }
    }

    @Test
    fun `should get testimonial detail with picture`() {
        // given
        val trader = dataHelper.getTrader(id = 1.toUUID()).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.TRADER)
        }

        val testimonial = dataHelper.getMentoringTestimonial(
            traderId = trader.id,
            profilePictureFileId = dataHelper.getImage(
                type = FileType.MENTORING_TESTIMONIAL_PICTURE,
                originalFileUrl = "original-url",
                compressedFileUrl = "compressed-url",
                blurHash = "blur-hash",
            ).id,
            firstName = "John",
            lastName = "Doe",
            rating = 5,
            description = "Great mentoring experience!",
        )

        // when
        val result = underTest.handle(
            GetMentoringTestimonialDetailQuery(
                testimonialId = testimonial.id,
                traderId = trader.id,
            ),
        )

        // then
        result.run {
            id shouldBe testimonial.id
            traderId shouldBe testimonial.traderId
            firstName shouldBe "John"
            lastName shouldBe "Doe"
            rating shouldBe 5
            description shouldBe "Great mentoring experience!"
            picture shouldNotBe null
            picture!!.run {
                imageOriginalUrl shouldBe "original-url"
                imageCompressedUrl shouldBe "compressed-url"
                imageBlurHash shouldBe "blur-hash"
            }
        }
    }

    @Test
    fun `should throw exception when testimonial not found`() {
        // given
        val trader = dataHelper.getTrader(id = 999.toUUID())

        // when/then
        shouldThrow<MentoringTestimonialNotFoundException> {
            underTest.handle(
                GetMentoringTestimonialDetailQuery(
                    testimonialId = 1.toUUID(),
                    traderId = trader.id,
                ),
            )
        }
    }

    @Test
    fun `should throw exception when testimonial not related to trader`() {
        // given
        val trader = dataHelper.getTrader(id = 1.toUUID()).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.TRADER)
        }
        val otherTrader = dataHelper.getTrader(id = 2.toUUID()).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.TRADER)
        }

        val testimonial = dataHelper.getMentoringTestimonial(
            traderId = trader.id,
            firstName = "John",
            lastName = "Doe",
            rating = 5,
            description = "Great mentoring experience!",
        )

        // when/then
        shouldThrow<MentoringTestimonialNotRelatedToTraderException> {
            underTest.handle(
                GetMentoringTestimonialDetailQuery(
                    testimonialId = testimonial.id,
                    traderId = otherTrader.id,
                ),
            )
        }
    }
}
