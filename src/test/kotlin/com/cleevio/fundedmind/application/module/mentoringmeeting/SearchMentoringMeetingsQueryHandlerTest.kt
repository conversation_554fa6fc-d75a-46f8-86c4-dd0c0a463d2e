package com.cleevio.fundedmind.application.module.mentoringmeeting

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.mentoringmeeting.query.SearchMentoringMeetingsQuery
import com.cleevio.fundedmind.domain.mentoringmeeting.exception.MentoringMeetingNotAccessibleException
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class SearchMentoringMeetingsQueryHandlerTest @Autowired constructor(
    private val underTest: SearchMentoringMeetingsQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should search mentoring meetings by calendly event uri`() {
        // Given
        val admin = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.ADMIN)

        val mentoringMeeting = dataHelper.getMentoringMeeting(
            id = 1.toUUID(),
            mentoringId = dataHelper.getMentoring(
                id = 11.toUUID(),
                productId = dataHelper.getProduct(
                    traderId = dataHelper.getTrader(id = 3.toUUID()).id,
                ).id,
                studentId = dataHelper.getStudent(id = 4.toUUID()).id,
            ).id,
            calendlyEventUri = "calendly-event-uri",
        )

        // When
        val result = underTest.handle(
            SearchMentoringMeetingsQuery(
                userId = admin.id,
                filter = SearchMentoringMeetingsQuery.Filter(
                    calendlyEventUri = "calendly-event-uri",
                ),
            ),
        )

        // Then
        result.data.size shouldBe 1
        result.data.first().run {
            mentoringMeetingId shouldBe mentoringMeeting.id
            calendlyEventUri shouldBe "calendly-event-uri"
            mentoringId shouldBe 11.toUUID()
            traderId shouldBe 3.toUUID()
            studentId shouldBe 4.toUUID()
        }
    }

    @Test
    fun `search mentoring meetings should throw when trader tries to access not their mentoring meeting`() {
        // Given
        val trader = dataHelper.getTrader(id = 1.toUUID()).also { trader ->
            dataHelper.getAppUser(id = trader.id, userRole = UserRole.TRADER)
        }

        val otherTrader = dataHelper.getTrader(id = 2.toUUID()).also { otherTrader ->
            dataHelper.getAppUser(id = otherTrader.id, userRole = UserRole.TRADER)
        }

        val mentoringMeeting = dataHelper.getMentoringMeeting(
            id = 1.toUUID(),
            mentoringId = dataHelper.getMentoring(
                productId = dataHelper.getProduct(traderId = trader.id).id,
                studentId = dataHelper.getStudent().id,
            ).id,
            calendlyEventUri = "calendly-event-uri",
        )

        // When/Then
        shouldThrow<MentoringMeetingNotAccessibleException> {
            underTest.handle(
                SearchMentoringMeetingsQuery(
                    userId = otherTrader.id,
                    filter = SearchMentoringMeetingsQuery.Filter(
                        calendlyEventUri = "calendly-event-uri",
                    ),
                ),
            )
        }
    }

    @Test
    fun `search mentoring meetings should throw when student tries to access not their mentoring meeting`() {
        // Given
        val student = dataHelper.getStudent(id = 1.toUUID()).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        val otherStudent = dataHelper.getStudent(id = 2.toUUID()).also { otherStudent ->
            dataHelper.getAppUser(id = otherStudent.id, userRole = UserRole.STUDENT)
        }

        val mentoringMeeting = dataHelper.getMentoringMeeting(
            id = 1.toUUID(),
            mentoringId = dataHelper.getMentoring(
                productId = dataHelper.getProduct(traderId = dataHelper.getTrader().id).id,
                studentId = student.id,
            ).id,
            calendlyEventUri = "calendly-event-uri",
        )

        // When/Then
        shouldThrow<MentoringMeetingNotAccessibleException> {
            underTest.handle(
                SearchMentoringMeetingsQuery(
                    userId = otherStudent.id,
                    filter = SearchMentoringMeetingsQuery.Filter(
                        calendlyEventUri = "calendly-event-uri",
                    ),
                ),
            )
        }
    }
}
