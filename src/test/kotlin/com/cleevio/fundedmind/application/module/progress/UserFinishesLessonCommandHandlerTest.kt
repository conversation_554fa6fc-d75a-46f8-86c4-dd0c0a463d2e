package com.cleevio.fundedmind.application.module.progress

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.progress.command.UserFinishesLessonCommand
import com.cleevio.fundedmind.domain.lesson.exception.LessonNotFoundException
import com.cleevio.fundedmind.domain.progress.CourseModuleProgressRepository
import com.cleevio.fundedmind.domain.progress.CourseProgressRepository
import com.cleevio.fundedmind.domain.progress.LessonProgressRepository
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant

class UserFinishesLessonCommandHandlerTest @Autowired constructor(
    private val underTest: UserFinishes<PERSON><PERSON>on<PERSON>ommandHand<PERSON>,
    private val lessonProgressRepository: LessonProgressRepository,
    private val courseModuleProgressRepository: CourseModuleProgressRepository,
    private val courseProgressRepository: CourseProgressRepository,
) : IntegrationTest() {

    @Test
    fun `should finish last lesson progress and trigger finish of module and course`() {
        val user = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)

        dataHelper.getCourse(
            id = 0.toUUID(),
            traderId = dataHelper.getTrader(id = 0.toUUID()).id,
        ).also { course ->
            // module with 1 lesson, 1 deleted lesson
            dataHelper.getCourseModule(
                id = 1.toUUID(),
                courseId = course.id,
            ).also { module ->
                // finished lesson
                dataHelper.getLesson(
                    id = 10.toUUID(),
                    courseModuleId = module.id,
                ).also { lesson ->
                    dataHelper.getLessonProgress(
                        id = 100.toUUID(),
                        userId = user.id,
                        lessonId = lesson.id,
                        seconds = 20,
                        entityModifier = { it.finish(now = "2025-01-01T06:00:00Z".toInstant()) }, // 01.01. 06:00
                    )
                }

                // last lesson to finish
                dataHelper.getLesson(
                    id = 11.toUUID(),
                    courseModuleId = module.id,
                ).also { lesson ->
                    dataHelper.getLessonProgress(
                        id = 111.toUUID(),
                        userId = user.id,
                        lessonId = lesson.id,
                        seconds = 20,
                    )
                }

                // deleted finished lesson
                dataHelper.getLesson(
                    id = 12.toUUID(),
                    courseModuleId = module.id,
                    entityModifier = { it.softDelete() },
                ).also { lesson ->
                    dataHelper.getLessonProgress(
                        id = 120.toUUID(),
                        userId = user.id,
                        lessonId = lesson.id,
                        seconds = 20,
                        entityModifier = { it.finish(now = "2025-01-02T08:00:00Z".toInstant()) }, // 02.01. 08:00
                    )
                }
            }

            // deleted module
            dataHelper.getCourseModule(
                id = 2.toUUID(),
                courseId = course.id,
                entityModifier = { it.softDelete() },
            )

            // coming soon module with lessons
            dataHelper.getCourseModule(
                id = 3.toUUID(),
                courseId = course.id,
                comingSoon = true,
            ).also { module ->
                // lesson in coming soon module
                dataHelper.getLesson(id = 30.toUUID(), courseModuleId = module.id)
                dataHelper.getLesson(id = 31.toUUID(), courseModuleId = module.id)
            }

            // coming soon module without lessons
            dataHelper.getCourseModule(
                id = 4.toUUID(),
                courseId = course.id,
                comingSoon = true,
            )
        }

        mockkStatic(Instant::class)
        every { Instant.now() } returns "2025-01-01T10:00:00Z".toInstant() // 01.01. 10:00

        underTest.handle(
            UserFinishesLessonCommand(
                userId = user.id,
                lessonId = 11.toUUID(),
            ),
        )

        lessonProgressRepository.findAll().run {
            size shouldBe 3

            first { it.id == 120.toUUID() }.run {
                finished shouldBe true
                finishedAt shouldBe "2025-01-02T08:00:00Z".toInstant() // 02.01. 08:00
            }

            first { it.id == 100.toUUID() }.run {
                finished shouldBe true
                finishedAt shouldBe "2025-01-01T06:00:00Z".toInstant() // 01.01. 06:00
            }

            first { it.id == 111.toUUID() }.run {
                userId shouldBe user.id
                lessonId shouldBe 11.toUUID()
                seconds shouldBe 20
                finished shouldBe true
                finishedAt shouldBe "2025-01-01T10:00:00Z".toInstant() // 01.01. 10:00
            }
        }

        courseModuleProgressRepository.findAll().run {
            size shouldBe 1
            single().run {
                userId shouldBe user.id
                courseModuleId shouldBe 1.toUUID()
                finished shouldBe true
                // 01.01. 10:00 - same as last finished (non-deleted) lesson
                finishedAt shouldBe "2025-01-01T10:00:00Z".toInstant()
            }
        }

        courseProgressRepository.findAll().run {
            size shouldBe 1
            single().run {
                userId shouldBe user.id
                courseId shouldBe 0.toUUID()
                finished shouldBe true
                // 01.01. 10:00 - same as last finished (non-deleted) module
                finishedAt shouldBe "2025-01-01T10:00:00Z".toInstant()
            }
        }

        verify { Instant.now() }
    }

    @Test
    fun `should create new lesson progress and finish it if it did not exist before`() {
        val user = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)

        val course = dataHelper.getCourse(id = 0.toUUID(), traderId = dataHelper.getTrader(id = 0.toUUID()).id)
        val module = dataHelper.getCourseModule(id = 0.toUUID(), courseId = course.id)
        val lesson = dataHelper.getLesson(id = 0.toUUID(), courseModuleId = module.id)

        mockkStatic(Instant::class)
        every { Instant.now() } returns "2025-01-01T10:00:00Z".toInstant() // 01.01. 10:00

        underTest.handle(
            UserFinishesLessonCommand(
                lessonId = lesson.id,
                userId = user.id,
            ),
        )

        lessonProgressRepository.findAll().run {
            size shouldBe 1
            single().run {
                userId shouldBe user.id
                lessonId shouldBe lesson.id
                seconds shouldBe 0
                finished shouldBe true
                finishedAt shouldBe "2025-01-01T10:00:00Z".toInstant()
            }
        }

        verify { Instant.now() }
    }

    @Test
    fun `should do nothing if lesson already finished`() {
        val user = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)

        val course = dataHelper.getCourse(id = 0.toUUID(), traderId = dataHelper.getTrader(id = 0.toUUID()).id)
        val module = dataHelper.getCourseModule(id = 0.toUUID(), courseId = course.id)
        val lesson = dataHelper.getLesson(id = 0.toUUID(), courseModuleId = module.id)

        dataHelper.getLessonProgress(
            id = 1.toUUID(),
            userId = user.id,
            lessonId = lesson.id,
            seconds = 20,
            entityModifier = { it.finish(now = "2025-01-01T08:00:00Z".toInstant()) }, // 01.01. 08:00
        )

        underTest.handle(
            UserFinishesLessonCommand(
                lessonId = lesson.id,
                userId = user.id,
            ),
        )

        lessonProgressRepository.findAll().run {
            size shouldBe 1
            single().run {
                userId shouldBe user.id
                lessonId shouldBe lesson.id
                seconds shouldBe 20
                finished shouldBe true
                finishedAt shouldBe "2025-01-01T08:00:00Z".toInstant() // 01.01. 08:00 - remains the same
            }
        }
    }

    @Test
    fun `should throw if lesson does not exist`() {
        val user = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT)

        shouldThrow<LessonNotFoundException> {
            underTest.handle(
                UserFinishesLessonCommand(
                    lessonId = 0.toUUID(),
                    userId = user.id,
                ),
            )
        }
    }
}
