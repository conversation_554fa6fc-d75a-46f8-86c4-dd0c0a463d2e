package com.cleevio.fundedmind.application.module.coursemodule

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.coursemodule.query.ListCourseModulesInCourseQuery
import com.cleevio.fundedmind.domain.common.AppButtonWithLink
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class ListCourseModulesInCourseQueryHandlerTest @Autowired constructor(
    private val underTest: ListCourseModulesInCourseQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should list course modules in course - verify mappings`() {
        // given
        val course = dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)

        // module with 3 lessons and 1 deleted lesson
        dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = course.id,
            rewardDescription = "Description",
            comingSoon = false,
            entityModifier = {
                it.changeThumbnailPictureDesktop(
                    fileId = dataHelper.getImage(
                        type = FileType.COURSE_MODULE_DESKTOP_THUMBNAIL,
                        originalFileUrl = "desktop-url",
                        compressedFileUrl = "desktop-url-comp",
                        blurHash = "123",
                    ).id,
                )
                it.changeThumbnailPictureMobile(
                    fileId = dataHelper.getImage(
                        type = FileType.COURSE_MODULE_MOBILE_THUMBNAIL,
                        originalFileUrl = "mobile-url",
                        compressedFileUrl = "mobile-url-comp",
                        blurHash = "456",
                    ).id,
                )
            },
        ).also { module ->
            dataHelper.getLesson(id = 11.toUUID(), courseModuleId = module.id, durationInSeconds = 10)
            dataHelper.getLesson(id = 12.toUUID(), courseModuleId = module.id, durationInSeconds = 10)
            dataHelper.getLesson(id = 13.toUUID(), courseModuleId = module.id, durationInSeconds = 10)
            dataHelper.getLesson(
                id = 14.toUUID(),
                courseModuleId = module.id,
                durationInSeconds = 10,
                entityModifier = { it.softDelete() },
            )
        }
        // module with 1 lesson
        dataHelper.getCourseModule(
            id = 2.toUUID(),
            courseId = course.id,
            listingOrder = 2,
            title = "With Coupon",
            rewardDescription = "Description",
            rewardCouponCode = "1234",
            comingSoon = false,
            entityModifier = {
                it.changeThumbnailPictureDesktop(
                    fileId = dataHelper.getImage(
                        type = FileType.COURSE_MODULE_DESKTOP_THUMBNAIL,
                        originalFileUrl = "desktop-url",
                        compressedFileUrl = "desktop-url-comp",
                        blurHash = "123",
                    ).id,
                )
            },
        ).also { module ->
            dataHelper.getLesson(id = 21.toUUID(), courseModuleId = module.id, durationInSeconds = 100)
        }
        // module with 1 deleted lesson
        dataHelper.getCourseModule(
            id = 3.toUUID(),
            courseId = course.id,
            listingOrder = 3,
            title = "With Button",
            rewardDescription = "Description",
            rewardButton = AppButtonWithLink(
                text = "Button",
                color = Color.BLUE,
                linkUrl = "url",
            ),
            comingSoon = true,
        ).also { module ->
            dataHelper.getLesson(
                id = 31.toUUID(),
                courseModuleId = module.id,
                durationInSeconds = 10,
                entityModifier = { it.softDelete() },
            )
        }

        // module from different course
        dataHelper.getCourseModule(
            id = 999.toUUID(),
            courseId = dataHelper.getCourse(id = 999.toUUID(), traderId = 1.toUUID()).id,
            listingOrder = 1,
        ).also { module ->
            dataHelper.getLesson(id = 9991.toUUID(), courseModuleId = module.id, durationInSeconds = 10)
        }

        // when
        val result = underTest.handle(
            ListCourseModulesInCourseQuery(
                filter = ListCourseModulesInCourseQuery.Filter(
                    searchString = null,
                    courseId = course.id,
                ),
            ),
        )

        result.data shouldHaveSize 3
        result.data.first { it.courseModuleId == 1.toUUID() }.run {
            courseModuleId shouldBe 1.toUUID()
            listingOrder shouldBe 1
            title shouldBe "Course Module"
            totalDurationInSeconds shouldBe 30
            reward shouldBe false
            comingSoon shouldBe false
            lessonCount shouldBe 3
            thumbnailDesktop shouldNotBe null
            thumbnailDesktop!!.run {
                imageOriginalUrl shouldBe "desktop-url"
                imageCompressedUrl shouldBe "desktop-url-comp"
                imageBlurHash shouldBe "123"
            }
            thumbnailMobile shouldNotBe null
            thumbnailMobile!!.run {
                imageOriginalUrl shouldBe "mobile-url"
                imageCompressedUrl shouldBe "mobile-url-comp"
                imageBlurHash shouldBe "456"
            }
        }
        result.data.first { it.courseModuleId == 2.toUUID() }.run {
            courseModuleId shouldBe 2.toUUID()
            listingOrder shouldBe 2
            title shouldBe "With Coupon"
            totalDurationInSeconds shouldBe 100
            reward shouldBe true
            comingSoon shouldBe false
            lessonCount shouldBe 1
            thumbnailDesktop shouldNotBe null
            thumbnailDesktop!!.run {
                imageOriginalUrl shouldBe "desktop-url"
                imageCompressedUrl shouldBe "desktop-url-comp"
                imageBlurHash shouldBe "123"
            }
            thumbnailMobile shouldBe null
        }
        result.data.first { it.courseModuleId == 3.toUUID() }.run {
            courseModuleId shouldBe 3.toUUID()
            listingOrder shouldBe 3
            title shouldBe "With Button"
            totalDurationInSeconds shouldBe 0
            reward shouldBe true
            comingSoon shouldBe true
            lessonCount shouldBe 0
            thumbnailDesktop shouldBe null
            thumbnailMobile shouldBe null
        }
    }

    @Test
    fun `should list course modules only from given course`() {
        // given
        dataHelper.getTrader(id = 1.toUUID())
        dataHelper.getCourse(id = 1.toUUID(), traderId = 1.toUUID())
        dataHelper.getCourse(id = 2.toUUID(), traderId = 1.toUUID())

        dataHelper.getCourseModule(id = 1.toUUID(), courseId = 1.toUUID())
        dataHelper.getCourseModule(id = 2.toUUID(), courseId = 1.toUUID())
        dataHelper.getCourseModule(id = 3.toUUID(), courseId = 2.toUUID())

        // when
        val result = underTest.handle(
            ListCourseModulesInCourseQuery(
                filter = ListCourseModulesInCourseQuery.Filter(
                    searchString = null,
                    courseId = 1.toUUID(),
                ),
            ),
        )

        // then
        result.data.map { it.courseModuleId } shouldBe listOf(1.toUUID(), 2.toUUID())
    }

    @Test
    fun `should list course modules ordered by listing order`() {
        // given
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(id = 1.toUUID()).id)
        dataHelper.getCourseModule(id = 1.toUUID(), courseId = 1.toUUID(), listingOrder = 1)
        dataHelper.getCourseModule(id = 2.toUUID(), courseId = 1.toUUID(), listingOrder = 3)
        dataHelper.getCourseModule(id = 3.toUUID(), courseId = 1.toUUID(), listingOrder = 2)

        // when
        val result = underTest.handle(
            ListCourseModulesInCourseQuery(
                filter = ListCourseModulesInCourseQuery.Filter(
                    searchString = null,
                    courseId = 1.toUUID(),
                ),
            ),
        )

        // then
        result.data.map { it.courseModuleId } shouldBe listOf(1.toUUID(), 3.toUUID(), 2.toUUID())
    }

    @Test
    fun `should list course modules by course without deleted courses`() {
        // given
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(id = 1.toUUID()).id)
        dataHelper.getCourseModule(id = 1.toUUID(), courseId = 1.toUUID())
        dataHelper.getCourseModule(id = 2.toUUID(), courseId = 1.toUUID(), entityModifier = { it.softDelete() })

        // when
        val result = underTest.handle(
            ListCourseModulesInCourseQuery(
                filter = ListCourseModulesInCourseQuery.Filter(
                    searchString = null,
                    courseId = 1.toUUID(),
                ),
            ),
        )

        // then
        result.data.map { it.courseModuleId } shouldBe listOf(1.toUUID())
    }
}
