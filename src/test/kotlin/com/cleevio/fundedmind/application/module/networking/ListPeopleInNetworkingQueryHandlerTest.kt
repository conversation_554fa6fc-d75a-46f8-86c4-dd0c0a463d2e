package com.cleevio.fundedmind.application.module.networking

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.common.command.GeoLocationInput
import com.cleevio.fundedmind.application.module.networking.query.ListPeopleInNetworkingQuery
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.LevelVisibility
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant
import java.time.temporal.ChronoUnit

class ListPeopleInNetworkingQueryHandlerTest @Autowired constructor(
    private val underTest: ListPeopleInNetworkingQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should return empty list when no locations in bounding box`() {
        // given
        val userId = 1.toUUID()
        dataHelper.getAppUser(id = userId, userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = userId, networkingVisibility = NetworkingVisibility.ENABLED)

        // when
        val result = underTest.handle(
            ListPeopleInNetworkingQuery(
                userId = userId,
                filter = ListPeopleInNetworkingQuery.Filter(
                    searchString = null,
                    northEast = GeoLocationInput(latitude = 50.0, longitude = 15.0),
                    southWest = GeoLocationInput(latitude = 49.0, longitude = 14.0),
                ),
            ),
        )

        // then
        result.data.shouldBeEmpty()
    }

    @Test
    fun `should return empty list when no people with matching location ids`() {
        // given
        val userId = 1.toUUID()
        dataHelper.getAppUser(id = userId, userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = userId, networkingVisibility = NetworkingVisibility.ENABLED)

        // Create a location within the bounding box
        val location = dataHelper.getUserLocation(
            obfuscatedLatitude = 49.5,
            obfuscatedLongitude = 14.5,
        )

        // when
        val result = underTest.handle(
            ListPeopleInNetworkingQuery(
                userId = userId,
                filter = ListPeopleInNetworkingQuery.Filter(
                    searchString = null,
                    northEast = GeoLocationInput(latitude = 50.0, longitude = 15.0),
                    southWest = GeoLocationInput(latitude = 49.0, longitude = 14.0),
                ),
            ),
        )

        // then
        result.data.shouldBeEmpty()
    }

    @Test
    fun `should return people with networking enabled and matching location ids`() {
        // given
        val requestingUserId = 1.toUUID()
        dataHelper.getAppUser(id = requestingUserId, userRole = UserRole.STUDENT)
        dataHelper.getStudent(
            id = requestingUserId,
            networkingVisibility = NetworkingVisibility.ENABLED,
            entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            },
        )

        val location1 = dataHelper.getUserLocation(
            city = "Prague",
            obfuscatedLatitude = 49.5,
            obfuscatedLongitude = 14.5,
        )
        val location2 = dataHelper.getUserLocation(
            city = "Brno",
            obfuscatedLatitude = 49.2,
            obfuscatedLongitude = 14.2,
        )
        val location3 = dataHelper.getUserLocation(
            city = "Outside",
            obfuscatedLatitude = 51.0, // Outside the bounding box
            obfuscatedLongitude = 16.0,
        )

        // Student with location1 and networking enabled - MASTERCLASS tier
        val student1 = dataHelper.getStudent(
            id = 2.toUUID(),
            firstName = "John",
            lastName = "Doe",
            biography = "Test bio",
            locationId = location1.id,
            gameLevel = GameLevel.NINE,
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.MASTERCLASS,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
            dataHelper.getStudentDiscord(
                studentId = student.id,
                discordId = "123456789",
            )
        }

        // Student with location2 and networking enabled - MASTERCLASS tier
        val student2 = dataHelper.getStudent(
            id = 3.toUUID(),
            firstName = "Jane",
            lastName = "Smith",
            biography = "Another bio",
            locationId = location2.id,
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.MASTERCLASS,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        // Student with location1 and networking enabled - BASECAMP tier (should be excluded)
        val studentBasecamp = dataHelper.getStudent(
            id = 10.toUUID(),
            firstName = "Basecamp",
            lastName = "Student",
            biography = "Basecamp student bio",
            locationId = location1.id,
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.BASECAMP,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        // Student with location1 but networking disabled
        dataHelper.getStudent(
            id = 4.toUUID(),
            firstName = "Bob",
            lastName = "Brown",
            locationId = location1.id,
            networkingVisibility = NetworkingVisibility.DISABLED,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        // Student with location3 (outside bounding box) and networking enabled
        dataHelper.getStudent(
            id = 5.toUUID(),
            firstName = "Alice",
            lastName = "Green",
            locationId = location3.id,
            networkingVisibility = NetworkingVisibility.ENABLED,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        // Trader with location1 and networking enabled
        val trader1 = dataHelper.getTrader(
            id = 6.toUUID(),
            firstName = "Mark",
            lastName = "Johnson",
            biography = "Trader bio",
            position = "Senior Trader",
            badgeColor = BadgeColor.GREEN_GRADIENT,
            locationId = location1.id,
            networkingVisibility = NetworkingVisibility.ENABLED,
        ).also { trader ->
            dataHelper.getAppUser(id = trader.id, userRole = UserRole.TRADER)
        }

        // Trader with location2 and networking enabled
        val trader2 = dataHelper.getTrader(
            id = 7.toUUID(),
            firstName = "Sarah",
            lastName = "Williams",
            biography = "Another trader bio",
            position = "Junior Trader",
            badgeColor = BadgeColor.BLUE_GRADIENT,
            locationId = location2.id,
            networkingVisibility = NetworkingVisibility.ENABLED,
        ).also { trader ->
            dataHelper.getAppUser(id = trader.id, userRole = UserRole.TRADER)
        }

        // when - query for bounding box containing location1 and location2
        val result = underTest.handle(
            ListPeopleInNetworkingQuery(
                userId = requestingUserId,
                filter = ListPeopleInNetworkingQuery.Filter(
                    searchString = null,
                    northEast = GeoLocationInput(latitude = 50.0, longitude = 15.0),
                    southWest = GeoLocationInput(latitude = 49.0, longitude = 14.0),
                ),
            ),
        )

        // then
        // Should have 4 people (2 students with MASTERCLASS tier + 2 traders)
        // BASECAMP student should be excluded
        result.data shouldHaveSize 4

        // Verify all expected people are in the result
        val studentJohn = result.data.find { it.id == student1.id }
        studentJohn shouldNotBe null

        // Verify BASECAMP student is not in the result
        result.data.find { it.id == studentBasecamp.id } shouldBe null
        studentJohn!!.run {
            id shouldBe student1.id
            role shouldBe UserRole.STUDENT
            firstName shouldBe "John"
            lastName shouldBe "Doe" // Requesting user has discord subscription
            biography shouldBe "Test bio"
            city shouldBe "Prague"
            gameLevel shouldBe GameLevel.NINE
            studentDiscordId shouldBe "123456789"
            obfuscatedLocation?.latitude shouldBe 49.5
            obfuscatedLocation?.longitude shouldBe 14.5
            profilePicture shouldBe null
            traderPosition shouldBe null
            traderBadgeColor shouldBe null
        }

        val studentJane = result.data.find { it.id == student2.id }
        studentJane shouldNotBe null

        val traderMark = result.data.find { it.id == trader1.id }
        traderMark shouldNotBe null
        traderMark!!.run {
            id shouldBe trader1.id
            role shouldBe UserRole.TRADER
            firstName shouldBe "Mark"
            lastName shouldBe "Johnson"
            biography shouldBe "Trader bio"
            city shouldBe "Prague"
            obfuscatedLocation?.latitude shouldBe 49.5
            obfuscatedLocation?.longitude shouldBe 14.5
            traderPosition shouldBe "Senior Trader"
            traderBadgeColor shouldBe BadgeColor.GREEN_GRADIENT
            profilePicture shouldBe null
            studentDiscordId shouldBe null
            gameLevel shouldBe null
        }

        val traderSarah = result.data.find { it.id == trader2.id }
        traderSarah shouldNotBe null
    }

    @Test
    fun `should filter people by search string`() {
        // given
        val requestingUserId = 1.toUUID()
        dataHelper.getAppUser(id = requestingUserId, userRole = UserRole.STUDENT)
        dataHelper.getStudent(
            id = requestingUserId,
            networkingVisibility = NetworkingVisibility.ENABLED,
            entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            },
        )

        val location = dataHelper.getUserLocation(
            city = "Prague",
            obfuscatedLatitude = 49.5,
            obfuscatedLongitude = 14.5,
        )

        // People with names matching "Jo"
        dataHelper.getStudent(
            id = 2.toUUID(),
            firstName = "John",
            lastName = "Doe",
            locationId = location.id,
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.MASTERCLASS,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        // BASECAMP student with name matching "Jo" (should be excluded)
        dataHelper.getStudent(
            id = 11.toUUID(),
            firstName = "Joseph",
            lastName = "Brown",
            locationId = location.id,
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.BASECAMP,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        dataHelper.getTrader(
            id = 3.toUUID(),
            firstName = "Mark",
            lastName = "Johnson",
            locationId = location.id,
            networkingVisibility = NetworkingVisibility.ENABLED,
        ).also { trader ->
            dataHelper.getAppUser(id = trader.id, userRole = UserRole.TRADER)
        }

        // People with names not matching "Jo"
        dataHelper.getStudent(
            id = 4.toUUID(),
            firstName = "Alice",
            lastName = "Smith",
            locationId = location.id,
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.MASTERCLASS,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        dataHelper.getTrader(
            id = 5.toUUID(),
            firstName = "Sarah",
            lastName = "Williams",
            locationId = location.id,
            networkingVisibility = NetworkingVisibility.ENABLED,
        ).also { trader ->
            dataHelper.getAppUser(id = trader.id, userRole = UserRole.TRADER)
        }

        // when - search for "Jo"
        val result = underTest.handle(
            ListPeopleInNetworkingQuery(
                userId = requestingUserId,
                filter = ListPeopleInNetworkingQuery.Filter(
                    searchString = "Jo",
                    northEast = GeoLocationInput(latitude = 50.0, longitude = 15.0),
                    southWest = GeoLocationInput(latitude = 49.0, longitude = 14.0),
                ),
            ),
        )

        // then
        // Should have 2 people matching "Jo" (1 student with MASTERCLASS tier + 1 trader)
        // BASECAMP student "Joseph" should be excluded
        result.data shouldHaveSize 2
        result.data.any { it.firstName == "John" } shouldBe true
        result.data.any { it.lastName == "Johnson" } shouldBe true
        result.data.any { it.firstName == "Joseph" } shouldBe false
    }

    @Test
    fun `should hide last names for students without discord subscription`() {
        // given
        val requestingUserId = 1.toUUID()
        dataHelper.getAppUser(id = requestingUserId, userRole = UserRole.STUDENT)
        dataHelper.getStudent(
            id = requestingUserId,
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.MASTERCLASS,
            // No discord subscription
        )

        val location = dataHelper.getUserLocation(
            obfuscatedLatitude = 49.5,
            obfuscatedLongitude = 14.5,
        )

        // Student with MASTERCLASS tier
        dataHelper.getStudent(
            id = 2.toUUID(),
            firstName = "John",
            lastName = "Doe",
            locationId = location.id,
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.MASTERCLASS,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        // Student with BASECAMP tier (should be excluded)
        dataHelper.getStudent(
            id = 12.toUUID(),
            firstName = "Basecamp",
            lastName = "User",
            locationId = location.id,
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.BASECAMP,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        // when
        val result = underTest.handle(
            ListPeopleInNetworkingQuery(
                userId = requestingUserId,
                filter = ListPeopleInNetworkingQuery.Filter(
                    searchString = null,
                    northEast = GeoLocationInput(latitude = 50.0, longitude = 15.0),
                    southWest = GeoLocationInput(latitude = 49.0, longitude = 14.0),
                ),
            ),
        )

        // then
        // Should have 1 person (1 student with MASTERCLASS tier)
        // BASECAMP student should be excluded
        result.data shouldHaveSize 1
        result.data.single().run {
            firstName shouldBe "John"
            lastName shouldBe null // Last name should be null for students without discord subscription
        }

        // Verify BASECAMP student is not in the result
        result.data.find { it.firstName == "Basecamp" } shouldBe null
    }

    @Test
    fun `should search by full name (first name + last name)`() {
        // given
        val requestingUserId = 1.toUUID()
        dataHelper.getAppUser(id = requestingUserId, userRole = UserRole.STUDENT)
        dataHelper.getStudent(
            id = requestingUserId,
            firstName = "Requesting",
            lastName = "User",
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.MASTERCLASS,
            entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            },
        )

        val location = dataHelper.getUserLocation(
            city = "Prague",
            obfuscatedLatitude = 49.5,
            obfuscatedLongitude = 14.5,
        )

        // Create users with different names
        val joeSmith = dataHelper.getStudent(
            id = 2.toUUID(),
            firstName = "Joe",
            lastName = "Smith",
            locationId = location.id,
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.MASTERCLASS,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        val joeDoe = dataHelper.getStudent(
            id = 3.toUUID(),
            firstName = "Joe",
            lastName = "Doe",
            locationId = location.id,
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.MASTERCLASS,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        // BASECAMP student with name matching search criteria (should be excluded)
        val joeBasecamp = dataHelper.getStudent(
            id = 13.toUUID(),
            firstName = "Joe",
            lastName = "Doe",
            locationId = location.id,
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.BASECAMP,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        val janeDoe = dataHelper.getTrader(
            id = 4.toUUID(),
            firstName = "Jane",
            lastName = "Doe",
            locationId = location.id,
            networkingVisibility = NetworkingVisibility.ENABLED,
        ).also { trader ->
            dataHelper.getAppUser(id = trader.id, userRole = UserRole.TRADER)
        }

        // Test searching by full name "Joe Doe"
        val fullNameResult = underTest.handle(
            ListPeopleInNetworkingQuery(
                userId = requestingUserId,
                filter = ListPeopleInNetworkingQuery.Filter(
                    searchString = "Joe Doe",
                    northEast = GeoLocationInput(latitude = 50.0, longitude = 15.0),
                    southWest = GeoLocationInput(latitude = 49.0, longitude = 14.0),
                ),
            ),
        )

        // Then - should match only Joe Doe with MASTERCLASS tier
        // BASECAMP student with same name should be excluded
        fullNameResult.data shouldHaveSize 1
        fullNameResult.data.single().run {
            id shouldBe joeDoe.id
            firstName shouldBe "Joe"
            lastName shouldBe "Doe"
        }

        // Verify BASECAMP student is not in the result
        fullNameResult.data.find { it.id == joeBasecamp.id } shouldBe null

        // Test searching by partial first name
        val firstNameResult = underTest.handle(
            ListPeopleInNetworkingQuery(
                userId = requestingUserId,
                filter = ListPeopleInNetworkingQuery.Filter(
                    searchString = "Joe",
                    northEast = GeoLocationInput(latitude = 50.0, longitude = 15.0),
                    southWest = GeoLocationInput(latitude = 49.0, longitude = 14.0),
                ),
            ),
        )

        // Then - should match both Joe Smith and Joe Doe with MASTERCLASS tier
        // BASECAMP student with same first name should be excluded
        firstNameResult.data shouldHaveSize 2
        firstNameResult.data.any { it.id == joeSmith.id } shouldBe true
        firstNameResult.data.any { it.id == joeDoe.id } shouldBe true
        firstNameResult.data.any { it.id == joeBasecamp.id } shouldBe false

        // Test searching by partial last name
        val lastNameResult = underTest.handle(
            ListPeopleInNetworkingQuery(
                userId = requestingUserId,
                filter = ListPeopleInNetworkingQuery.Filter(
                    searchString = "Doe",
                    northEast = GeoLocationInput(latitude = 50.0, longitude = 15.0),
                    southWest = GeoLocationInput(latitude = 49.0, longitude = 14.0),
                ),
            ),
        )

        // Then - should match both Joe Doe with MASTERCLASS tier and Jane Doe (trader)
        // BASECAMP student with same last name should be excluded
        lastNameResult.data shouldHaveSize 2
        lastNameResult.data.any { it.id == joeDoe.id } shouldBe true
        lastNameResult.data.any { it.id == janeDoe.id } shouldBe true
        lastNameResult.data.any { it.id == joeBasecamp.id } shouldBe false
    }

    @Test
    fun `should respect level visibility settings when showing game level`() {
        // given
        val location = dataHelper.getUserLocation(
            city = "Prague",
            obfuscatedLatitude = 49.5,
            obfuscatedLongitude = 14.5,
        )

        // Create students with different LevelVisibility settings
        val studentWithEnabledLevel = dataHelper.getStudent(
            id = 1.toUUID(),
            firstName = "Enabled",
            lastName = "Level",
            locationId = location.id,
            gameLevel = GameLevel.FIVE,
            networkingVisibility = NetworkingVisibility.ENABLED,
            levelVisibility = LevelVisibility.ENABLED,
            studentTier = StudentTier.MASTERCLASS,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        val studentWithDisabledLevel = dataHelper.getStudent(
            id = 2.toUUID(),
            firstName = "Disabled",
            lastName = "Level",
            locationId = location.id,
            gameLevel = GameLevel.NINE,
            networkingVisibility = NetworkingVisibility.ENABLED,
            levelVisibility = LevelVisibility.DISABLED,
            studentTier = StudentTier.MASTERCLASS,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        // Test with different requesting user roles

        // 1. Student requesting user (cannot always see game level)
        val studentUser = dataHelper.getStudent(
            id = 3.toUUID(),
            firstName = "Student",
            lastName = "User",
            networkingVisibility = NetworkingVisibility.ENABLED,
            studentTier = StudentTier.MASTERCLASS,
        ).also { student ->
            dataHelper.getAppUser(id = student.id, userRole = UserRole.STUDENT)
        }

        val studentResult = underTest.handle(
            ListPeopleInNetworkingQuery(
                userId = studentUser.id,
                filter = ListPeopleInNetworkingQuery.Filter(
                    searchString = null,
                    northEast = GeoLocationInput(latitude = 50.0, longitude = 15.0),
                    southWest = GeoLocationInput(latitude = 49.0, longitude = 14.0),
                ),
            ),
        )

        // Student should see game level only for students with LevelVisibility.ENABLED
        studentResult.data shouldHaveSize 2
        studentResult.data.find { it.id == studentWithEnabledLevel.id }!!.run {
            firstName shouldBe "Enabled"
            gameLevel shouldBe GameLevel.FIVE // Level is visible because LevelVisibility is ENABLED
        }
        studentResult.data.find { it.id == studentWithDisabledLevel.id }!!.run {
            firstName shouldBe "Disabled"
            gameLevel shouldBe null // Level is hidden because LevelVisibility is DISABLED
        }

        // 2. Trader requesting user (can always see game level)
        val traderUser = dataHelper.getTrader(
            id = 4.toUUID(),
            firstName = "Trader",
            lastName = "User",
            networkingVisibility = NetworkingVisibility.ENABLED,
        ).also { trader ->
            dataHelper.getAppUser(id = trader.id, userRole = UserRole.TRADER)
        }

        val traderResult = underTest.handle(
            ListPeopleInNetworkingQuery(
                userId = traderUser.id,
                filter = ListPeopleInNetworkingQuery.Filter(
                    searchString = null,
                    northEast = GeoLocationInput(latitude = 50.0, longitude = 15.0),
                    southWest = GeoLocationInput(latitude = 49.0, longitude = 14.0),
                ),
            ),
        )

        // Trader should see game level for all students, regardless of LevelVisibility
        traderResult.data shouldHaveSize 2
        traderResult.data.find { it.id == studentWithEnabledLevel.id }!!.run {
            firstName shouldBe "Enabled"
            gameLevel shouldBe GameLevel.FIVE // Level is visible because trader can always see game level
        }
        traderResult.data.find { it.id == studentWithDisabledLevel.id }!!.run {
            firstName shouldBe "Disabled"
            gameLevel shouldBe GameLevel.NINE // Level is visible because trader can always see game level
        }

        // 3. Admin requesting user (can always see game level)
        val adminUser = dataHelper.getAppUser(id = 5.toUUID(), userRole = UserRole.ADMIN)

        val adminResult = underTest.handle(
            ListPeopleInNetworkingQuery(
                userId = adminUser.id,
                filter = ListPeopleInNetworkingQuery.Filter(
                    searchString = null,
                    northEast = GeoLocationInput(latitude = 50.0, longitude = 15.0),
                    southWest = GeoLocationInput(latitude = 49.0, longitude = 14.0),
                ),
            ),
        )

        // Admin should see game level for all students, regardless of LevelVisibility
        adminResult.data shouldHaveSize 2
        adminResult.data.find { it.id == studentWithEnabledLevel.id }!!.run {
            firstName shouldBe "Enabled"
            gameLevel shouldBe GameLevel.FIVE // Level is visible because admin can always see game level
        }
        adminResult.data.find { it.id == studentWithDisabledLevel.id }!!.run {
            firstName shouldBe "Disabled"
            gameLevel shouldBe GameLevel.NINE // Level is visible because admin can always see game level
        }
    }
}
