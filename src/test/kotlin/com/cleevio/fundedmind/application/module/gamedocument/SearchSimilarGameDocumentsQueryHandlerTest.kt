package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamedocument.query.SearchSimilarGameDocumentsQuery
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.gamedocument.constant.SimilarityType
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentHasWrongTypeException
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.LocalDate

class SearchSimilarGameDocumentsQueryHandlerTest @Autowired constructor(
    private val underTest: SearchSimilarGameDocumentsQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should find similar documents for same student same payout date, same student same payout amount`() {
        // given
        val student = dataHelper.getAppUser(id = 3.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "Alice", lastName = "Johnson")
        }

        val targetDocument = dataHelper.getGameDocument(
            id = 1.toUUID(),
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("2000.00"),
            payoutDate = LocalDate.of(2025, 1, 1),
        )

        val sameAmountDoc = dataHelper.getGameDocument(
            id = 2.toUUID(),
            studentId = student.id, // same student
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("2000.00"), // same amount
            payoutDate = LocalDate.of(2025, 1, 2),
            entityModifier = { it.denyAwaiting("denied") },
        )

        val sameDateDoc = dataHelper.getGameDocument(
            id = 3.toUUID(),
            studentId = student.id, // same student
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("3000.00"),
            payoutDate = LocalDate.of(2025, 1, 1), // same date
            entityModifier = { it.approveAwaiting() },
        )

        // Different date, different amount - should not be included
        val differentDoc = dataHelper.getGameDocument(
            id = 4.toUUID(),
            studentId = student.id, // same student
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("1000.00"),
            payoutDate = LocalDate.of(2025, 2, 11),
        )

        // when
        val result = underTest.handle(
            SearchSimilarGameDocumentsQuery(gameDocumentId = targetDocument.id),
        )

        // then
        result.data shouldHaveSize 2
        result.data.first { it.gameDocumentId == sameAmountDoc.id }.run {
            payoutDate shouldBe LocalDate.of(2025, 1, 2)
            payoutAmount!! shouldBeEqualComparingTo BigDecimal("2000.00")
            state shouldBe GameDocumentApprovalState.DENIED
            similarityTypes shouldBe setOf(SimilarityType.SAME_STUDENT_SAME_AMOUNT)
            this.student.run {
                studentId shouldBe student.id
                firstName shouldBe "Alice"
                lastName shouldBe "Johnson"
            }
        }
        result.data.first { it.gameDocumentId == sameDateDoc.id }.run {
            payoutDate shouldBe LocalDate.of(2025, 1, 1)
            payoutAmount!! shouldBeEqualComparingTo BigDecimal("3000.00")
            state shouldBe GameDocumentApprovalState.APPROVED
            similarityTypes shouldBe setOf(SimilarityType.SAME_STUDENT_SAME_DATE)
            this.student.run {
                studentId shouldBe student.id
                firstName shouldBe "Alice"
                lastName shouldBe "Johnson"
            }
        }
    }

    @Test
    fun `should find similar for different student same payout amount and date`() {
        // given
        val student1 = dataHelper.getAppUser(id = 11.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "Charlie", lastName = "Brown")
        }

        val student2 = dataHelper.getAppUser(id = 12.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "Diana", lastName = "Prince")
        }

        val targetDocument = dataHelper.getGameDocument(
            id = 1.toUUID(),
            studentId = student1.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("2500.00"),
            payoutDate = LocalDate.of(2025, 4, 1),
        )

        val similarDocument = dataHelper.getGameDocument(
            id = 2.toUUID(),
            studentId = student2.id, // different student
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("2500.00"), // same amount
            payoutDate = LocalDate.of(2025, 4, 1), // same date
        )

        // when
        val result = underTest.handle(
            SearchSimilarGameDocumentsQuery(gameDocumentId = targetDocument.id),
        )

        // then
        result.data shouldHaveSize 1
        result.data.single().run {
            gameDocumentId shouldBe 2.toUUID()
            student.run {
                studentId shouldBe student2.id
                firstName shouldBe "Diana"
                lastName shouldBe "Prince"
            }
            payoutDate shouldBe LocalDate.of(2025, 4, 1)
            payoutAmount!! shouldBeEqualComparingTo BigDecimal("2500.00")
            state shouldBe GameDocumentApprovalState.WAITING
            similarityTypes shouldBe setOf(SimilarityType.DIFFERENT_STUDENT_SAME_DATE_AND_AMOUNT)
        }
    }

    @Test
    fun `should return empty list when no similar documents found`() {
        // given
        val student = dataHelper.getAppUser(id = 17.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "Grace", lastName = "Kelly")
        }

        val targetDocument = dataHelper.getGameDocument(
            id = 1.toUUID(),
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("9999.99"),
            payoutDate = LocalDate.of(2025, 12, 31),
        )

        // when
        val result = underTest.handle(
            SearchSimilarGameDocumentsQuery(gameDocumentId = targetDocument.id),
        )

        // then
        result.data shouldHaveSize 0
    }

    @Test
    fun `should throw exception when game document is not PAYOUT type`() {
        // given
        val student = dataHelper.getAppUser(id = 18.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "Henry", lastName = "Ford")
        }

        val backtesting = dataHelper.getGameDocument(
            id = 1.toUUID(),
            studentId = student.id,
            type = GameDocumentType.BACKTESTING,
        )
        val certificate = dataHelper.getGameDocument(
            id = 2.toUUID(),
            studentId = student.id,
            type = GameDocumentType.CERTIFICATE,
        )

        // when & then
        shouldThrow<GameDocumentHasWrongTypeException> {
            underTest.handle(
                SearchSimilarGameDocumentsQuery(gameDocumentId = backtesting.id),
            )
        }
        shouldThrow<GameDocumentHasWrongTypeException> {
            underTest.handle(
                SearchSimilarGameDocumentsQuery(gameDocumentId = certificate.id),
            )
        }
    }
}
