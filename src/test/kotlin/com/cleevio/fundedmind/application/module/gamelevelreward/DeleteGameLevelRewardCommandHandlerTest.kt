package com.cleevio.fundedmind.application.module.gamelevelreward

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamelevelreward.command.DeleteGameLevelRewardCommand
import com.cleevio.fundedmind.domain.gamelevelreward.GameLevelRewardRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class DeleteGameLevelRewardCommandHandlerTest @Autowired constructor(
    private val underTest: DeleteGameLevelRewardCommandHandler,
    private val gameLevelRewardRepository: GameLevelRewardRepository,
) : IntegrationTest() {

    @Test
    fun `should soft delete level reward`() {
        // given
        dataHelper.getGameLevelReward(id = 1.toUUID())

        // when
        underTest.handle(
            DeleteGameLevelRewardCommand(
                gameLevelRewardId = 1.toUUID(),
            ),
        )

        // then
        gameLevelRewardRepository.findByIdOrNull(1.toUUID())!!.isDeleted shouldBe true
    }
}
