package com.cleevio.fundedmind.application.module.coursemodule

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.coursemodule.command.DeleteCourseModuleCommand
import com.cleevio.fundedmind.domain.coursemodule.CourseModuleRepository
import com.cleevio.fundedmind.domain.file.AppFileRepository
import com.cleevio.fundedmind.domain.lesson.LessonRepository
import com.cleevio.fundedmind.domain.lesson.attachment.LessonAttachmentRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class DeleteCourseModuleCommandHandlerTest @Autowired constructor(
    private val underTest: DeleteCourseModuleCommandHandler,
    private val courseModuleRepository: CourseModuleRepository,
    private val lessonRepository: LessonRepository,
    private val lessonAttachmentRepository: LessonAttachmentRepository,
    private val appFileRepository: AppFileRepository,
) : IntegrationTest() {

    @Test
    fun `should soft delete course module`() {
        dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)
        dataHelper.getCourseModule(id = 1.toUUID(), courseId = 1.toUUID())

        underTest.handle(DeleteCourseModuleCommand(courseId = 1.toUUID(), courseModuleId = 1.toUUID()))

        courseModuleRepository.findByIdOrNull(1.toUUID())!!.isDeleted shouldBe true
    }

    @Test
    fun `should soft delete course module with its lessons`() {
        // given
        val course = dataHelper.getCourse(id = 1.toUUID(), traderId = dataHelper.getTrader(1.toUUID()).id)

        // create modules for the course
        val module = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id)

        // create lessons for modules
        dataHelper.getLesson(id = 1.toUUID(), courseModuleId = module.id)
        dataHelper.getLesson(id = 2.toUUID(), courseModuleId = module.id, entityModifier = { it.softDelete() })
        dataHelper.getLesson(
            id = 3.toUUID(),
            courseModuleId = module.id,
        ).also { lesson ->
            dataHelper.getLessonAttachment(
                lessonId = lesson.id,
                entityModifier = { it.changeAttachmentDocument(dataHelper.getDocument().id) },
            )
        }

        // when
        underTest.handle(
            DeleteCourseModuleCommand(courseModuleId = module.id, courseId = course.id),
        )

        // then
        courseModuleRepository.findByIdOrNull(module.id)!!.isDeleted shouldBe true

        lessonRepository.findAll().run {
            first { it.id == 1.toUUID() }.isDeleted shouldBe true
            first { it.id == 2.toUUID() }.isDeleted shouldBe true
            first { it.id == 3.toUUID() }.isDeleted shouldBe true
        }

        lessonAttachmentRepository.findAll().run {
            size shouldBe 1
            single().run {
                lessonId shouldBe 3.toUUID()
                // attachment file should be not deleted
                attachmentDocumentFileId shouldNotBe null
                appFileRepository.findByIdOrNull(attachmentDocumentFileId!!) shouldNotBe null
            }
        }
    }
}
