package com.cleevio.fundedmind.application.module.networking

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.networking.query.GetUserContactQuery
import com.cleevio.fundedmind.application.module.user.appuser.exception.UserHasDisabledNetworkingException
import com.cleevio.fundedmind.application.module.user.appuser.exception.UserIsAdminException
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.LevelVisibility
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant
import java.time.temporal.ChronoUnit

class GetUserContactQueryHandlerTest @Autowired constructor(
    private val underTest: GetUserContactQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should return gameLevel=null when queried student has disabled level visibility`() {
        // given
        val meUserId = 1.toUUID()
        val queriedUserId = 2.toUUID()

        // Create meUser as trader (to avoid tier/discord checks)
        dataHelper.getAppUser(id = meUserId, userRole = UserRole.TRADER)
        dataHelper.getTrader(id = meUserId)

        // Create queried user as student with enabled networking but disabled level visibility
        dataHelper.getAppUser(id = queriedUserId, userRole = UserRole.STUDENT)
        dataHelper.getStudent(
            id = queriedUserId,
            firstName = "Student",
            gameLevel = GameLevel.FIVE,
            networkingVisibility = NetworkingVisibility.ENABLED,
            levelVisibility = LevelVisibility.DISABLED,
            biography = "Student bio",
        )

        // when
        val result = underTest.handle(GetUserContactQuery(meUserId = meUserId, queriedUserId = queriedUserId))

        // then
        result.run {
            userRole shouldBe UserRole.STUDENT
            firstName shouldBe "Student"
            bio shouldBe "Student bio"
            networkingVisibility shouldBe NetworkingVisibility.ENABLED
            studentGameLevel shouldBe null // Level should be null because level visibility is disabled
        }
    }

    @Test
    fun `should return null discordId when queried student has discord subscription disabled`() {
        // given
        val meUserId = 1.toUUID()
        val queriedUserId = 2.toUUID()

        // Create meUser as trader (to avoid tier/discord checks)
        dataHelper.getAppUser(id = meUserId, userRole = UserRole.TRADER)
        dataHelper.getTrader(id = meUserId)

        // Create queried user as student with enabled networking but disabled discord subscription
        dataHelper.getAppUser(id = queriedUserId, userRole = UserRole.STUDENT)
        dataHelper.getStudent(
            id = queriedUserId,
            firstName = "Student",
            gameLevel = GameLevel.FIVE,
            networkingVisibility = NetworkingVisibility.ENABLED,
            biography = "Student bio",
            entityModifier = {
                it.activateDiscordSubscription("2025-02-01T23:59:00Z".toInstant())
                it.deactivateDiscordSubscription()
            },
        ).also {
            dataHelper.getStudentDiscord(
                studentId = it.id,
                discordId = "123456789",
            )
        }

        // when
        val result = underTest.handle(GetUserContactQuery(meUserId = meUserId, queriedUserId = queriedUserId))

        // then
        result.run {
            userRole shouldBe UserRole.STUDENT
            firstName shouldBe "Student"
            bio shouldBe "Student bio"
            networkingVisibility shouldBe NetworkingVisibility.ENABLED
            discordId shouldBe null // Discord ID should be null because the discord subscription is disabled
        }
    }

    @Test
    fun `should throw when queried user is student and has networking visibility disabled`() {
        // given
        val meUserId = 1.toUUID()
        val queriedUserId = 2.toUUID()

        // Create meUser as trader (to avoid tier/discord checks)
        dataHelper.getAppUser(id = meUserId, userRole = UserRole.TRADER)
        dataHelper.getTrader(id = meUserId)

        // Create queried user as student with disabled networking
        dataHelper.getAppUser(id = queriedUserId, userRole = UserRole.STUDENT)
        dataHelper.getStudent(id = queriedUserId, networkingVisibility = NetworkingVisibility.DISABLED)

        // when and then
        shouldThrow<UserHasDisabledNetworkingException> {
            underTest.handle(GetUserContactQuery(meUserId = meUserId, queriedUserId = queriedUserId))
        }
    }

    @ParameterizedTest
    @EnumSource(value = StudentTier::class, names = ["MASTERCLASS", "EXCLUSIVE"])
    fun `should pass when meUser is student with masterclass or exclusive tier and queried user is student or trader`(
        studentTier: StudentTier,
    ) {
        // given
        val meUserId = 1.toUUID()
        val queriedStudentId = 2.toUUID()
        val queriedTraderId = 3.toUUID()

        // Create meUser as student with masterclass/exclusive tier and discord enabled
        dataHelper.getAppUser(id = meUserId, userRole = UserRole.STUDENT)
        dataHelper.getStudent(
            id = meUserId,
            studentTier = studentTier,
            entityModifier = { it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS)) },
        )

        // Create queried user as student with enabled networking
        dataHelper.getAppUser(id = queriedStudentId, userRole = UserRole.STUDENT)
        dataHelper.getStudent(
            id = queriedStudentId,
            firstName = "Student",
            gameLevel = GameLevel.FIVE,
            networkingVisibility = NetworkingVisibility.ENABLED,
            biography = "Student bio",
            entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
                it.changeProfilePicture(
                    dataHelper.getImage(
                        type = FileType.STUDENT_PROFILE_PICTURE,
                        originalFileUrl = "url",
                        compressedFileUrl = "url-comp",
                        blurHash = "123",
                    ).id,
                )
            },
        ).also {
            dataHelper.getStudentDiscord(
                id = 1.toUUID(),
                studentId = it.id,
                discordId = "123456789",
            )
        }

        // Create queried user as trader with enabled networking
        dataHelper.getAppUser(id = queriedTraderId, userRole = UserRole.TRADER)
        dataHelper.getTrader(
            id = queriedTraderId,
            firstName = "Trader",
            lastName = "Doe",
            biography = "Trader bio",
            badgeColor = BadgeColor.GOLDEN_GRADIENT,
            position = "Mentor",
            entityModifier = {
                it.changeProfilePicture(
                    dataHelper.getImage(
                        type = FileType.TRADER_PROFILE_PICTURE,
                        originalFileUrl = "trader-url",
                        compressedFileUrl = "trader-url-comp",
                        blurHash = "456",
                    ).id,
                )
            },
            networkingVisibility = NetworkingVisibility.ENABLED,
        )

        // when & then - student query
        underTest.handle(GetUserContactQuery(meUserId = meUserId, queriedUserId = queriedStudentId)).run {
            userRole shouldBe UserRole.STUDENT
            firstName shouldBe "Student"
            bio shouldBe "Student bio"
            profilePicture shouldNotBe null
            profilePicture!!.run {
                imageOriginalUrl shouldBe "url"
                imageCompressedUrl shouldBe "url-comp"
                imageBlurHash shouldBe "123"
            }
            networkingVisibility shouldBe NetworkingVisibility.ENABLED
            discordId shouldBe "123456789"
            studentGameLevel shouldBe GameLevel.FIVE
            traderLastName shouldBe null
            traderBadgeColor shouldBe null
            traderPosition shouldBe null
        }

        // when & then - trader query
        underTest.handle(GetUserContactQuery(meUserId = meUserId, queriedUserId = queriedTraderId)).run {
            userRole shouldBe UserRole.TRADER
            firstName shouldBe "Trader"
            bio shouldBe "Trader bio"
            profilePicture shouldNotBe null
            profilePicture!!.run {
                imageOriginalUrl shouldBe "trader-url"
                imageCompressedUrl shouldBe "trader-url-comp"
                imageBlurHash shouldBe "456"
            }
            networkingVisibility shouldBe NetworkingVisibility.ENABLED
            discordId shouldBe null
            studentGameLevel shouldBe null
            traderLastName shouldBe "Doe"
            traderBadgeColor shouldBe BadgeColor.GOLDEN_GRADIENT
            traderPosition shouldBe "Mentor"
        }
    }

    @Test
    fun `should throw when meUser is trader and queried user is admin`() {
        // given
        val meUserId = 1.toUUID()
        val queriedAdminId = 2.toUUID()

        // Create meUser as trader
        dataHelper.getAppUser(id = meUserId, userRole = UserRole.TRADER)
        dataHelper.getTrader(id = meUserId)

        // Create queried user as admin
        dataHelper.getAppUser(id = queriedAdminId, userRole = UserRole.ADMIN)

        // when & then
        shouldThrow<UserIsAdminException> {
            underTest.handle(GetUserContactQuery(meUserId = meUserId, queriedUserId = queriedAdminId))
        }
    }

    @Test
    fun `should return discordId=null when meUser is student without discord subscription`() {
        // given
        val meUserId = 1.toUUID()
        val queriedUserId = 2.toUUID()

        // Create meUser as student with masterclass tier but without discord subscription
        dataHelper.getAppUser(id = meUserId, userRole = UserRole.STUDENT)
        dataHelper.getStudent(
            id = meUserId,
            studentTier = StudentTier.MASTERCLASS,
            // Not activating discord subscription
        )

        // Create queried user as student with enabled networking and discord
        dataHelper.getAppUser(id = queriedUserId, userRole = UserRole.STUDENT)
        dataHelper.getStudent(
            id = queriedUserId,
            firstName = "Student",
            gameLevel = GameLevel.FIVE,
            networkingVisibility = NetworkingVisibility.ENABLED,
            entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            },
        ).also {
            dataHelper.getStudentDiscord(
                studentId = it.id,
                discordId = "123456789",
            )
        }

        // when
        val result = underTest.handle(GetUserContactQuery(meUserId = meUserId, queriedUserId = queriedUserId))

        // then
        result.run {
            userRole shouldBe UserRole.STUDENT
            firstName shouldBe "Student"
            networkingVisibility shouldBe NetworkingVisibility.ENABLED
            discordId shouldBe null // Discord ID should be null because meUser doesn't have discord subscription
        }
    }
}
