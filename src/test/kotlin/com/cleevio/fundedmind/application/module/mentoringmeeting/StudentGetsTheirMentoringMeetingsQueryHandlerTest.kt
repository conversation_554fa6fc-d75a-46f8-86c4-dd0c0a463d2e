package com.cleevio.fundedmind.application.module.mentoringmeeting

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.mentoringmeeting.query.StudentGetsTheirMentoringMeetingsQuery
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.mentoringmeeting.constant.InitiatorType
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class StudentGetsTheirMentoringMeetingsQueryHandlerTest @Autowired constructor(
    private val underTest: StudentGetsTheirMentoringMeetingsQueryHandler,
) : IntegrationTest() {

    @Test
    fun `student gets their mentoring meetings - verify mappings`() {
        // given
        val student1 = dataHelper.getStudent(id = 1.toUUID())
        val student2 = dataHelper.getStudent(id = 2.toUUID())

        val trader1 = dataHelper.getTrader(
            id = 1.toUUID(),
            position = "Mentor",
            firstName = "John",
            lastName = "Doe",
            entityModifier = {
                it.changeMentoringPhoto(
                    dataHelper.getImage(
                        type = FileType.TRADER_MENTORING_PHOTO,
                        originalFileUrl = "photo-url",
                        compressedFileUrl = "photo-url-comp",
                        blurHash = "blur123",
                    ).id,
                )
            },
        )

        val trader2 = dataHelper.getTrader(
            id = 2.toUUID(),
            position = "Mentor",
            firstName = "Jane",
            lastName = "Smith",
            entityModifier = {
                it.changeMentoringPhoto(
                    dataHelper.getImage(
                        type = FileType.TRADER_MENTORING_PHOTO,
                        originalFileUrl = "photo-url2",
                        compressedFileUrl = "photo-url2-comp",
                        blurHash = "blur456",
                    ).id,
                )
            },
        )

        val product1 = dataHelper.getProduct(id = 1.toUUID(), traderId = trader1.id)
        val product2 = dataHelper.getProduct(id = 2.toUUID(), traderId = trader2.id)

        dataHelper.getMentoring(id = 1.toUUID(), studentId = student1.id, productId = product1.id).also { mentoring1 ->
            dataHelper.getMentoringMeeting(
                id = 1.toUUID(),
                mentoringId = mentoring1.id,
                startAt = "2025-04-20T10:00:00Z".toInstant(), // 20.04. 10:00
                finishAt = "2025-04-20T12:00:00Z".toInstant(), // 20.04. 12:00
                meetingUrl = "meeting-url-1",
                entityModifier = {
                    it.updateRecordingUrl("recording-url-1")
                },
            )

            dataHelper.getMentoringMeeting(
                id = 2.toUUID(),
                mentoringId = mentoring1.id,
                startAt = "2025-04-25T14:00:00Z".toInstant(), // 25.04. 14:00
                finishAt = "2025-04-25T16:00:00Z".toInstant(), // 25.04. 16:00
                meetingUrl = "meeting-url-2",
            )
        }

        dataHelper.getMentoring(id = 2.toUUID(), studentId = student1.id, productId = product2.id).also { mentoring2 ->
            dataHelper.getMentoringMeeting(
                id = 3.toUUID(),
                mentoringId = mentoring2.id,
                startAt = "2025-05-10T09:00:00Z".toInstant(), // 10.05. 09:00
                finishAt = "2025-05-10T10:30:00Z".toInstant(), // 10.05. 10:30
                meetingUrl = "meeting-url-3",
                entityModifier = {
                    it.updateRecordingUrl("recording-url-3")
                },
            )
        }

        // different student
        dataHelper.getMentoring(id = 3.toUUID(), studentId = student2.id, productId = product1.id).also { mentoring3 ->
            dataHelper.getMentoringMeeting(
                id = 4.toUUID(),
                mentoringId = mentoring3.id, // belongs to student2
                startAt = "2025-05-15T11:00:00Z".toInstant(), // 15.05. 11:00
                finishAt = "2025-05-15T12:30:00Z".toInstant(), // 15.05. 12:30
                meetingUrl = "meeting-url-4",
                entityModifier = {
                    it.updateRecordingUrl("recording-url-4")
                },
            )
        }

        // when
        val result = underTest.handle(
            StudentGetsTheirMentoringMeetingsQuery(studentId = student1.id),
        )

        // then
        result.data.size shouldBe 3
        result.data.run {
            this[0].run {
                mentoringMeetingId shouldBe 1.toUUID()
                startAt shouldBe "2025-04-20T10:00:00Z".toInstant()
                finishAt shouldBe "2025-04-20T12:00:00Z".toInstant()
                meetingUrl shouldBe "meeting-url-1"
                recordingUrl shouldBe "recording-url-1"
                coverPhoto!!.run {
                    imageOriginalUrl shouldBe "photo-url"
                    imageCompressedUrl shouldBe "photo-url-comp"
                    imageBlurHash shouldBe "blur123"
                }
                trader.run {
                    traderId shouldBe 1.toUUID()
                    position shouldBe "Mentor"
                    firstName shouldBe "John"
                    lastName shouldBe "Doe"
                }
            }
            this[1].run {
                mentoringMeetingId shouldBe 2.toUUID()
                startAt shouldBe "2025-04-25T14:00:00Z".toInstant()
                finishAt shouldBe "2025-04-25T16:00:00Z".toInstant()
                meetingUrl shouldBe "meeting-url-2"
                recordingUrl shouldBe null
                coverPhoto!!.run {
                    imageOriginalUrl shouldBe "photo-url"
                    imageCompressedUrl shouldBe "photo-url-comp"
                    imageBlurHash shouldBe "blur123"
                }
                trader.run {
                    traderId shouldBe 1.toUUID()
                    position shouldBe "Mentor"
                    firstName shouldBe "John"
                    lastName shouldBe "Doe"
                }
            }
            this[2].run {
                mentoringMeetingId shouldBe 3.toUUID()
                startAt shouldBe "2025-05-10T09:00:00Z".toInstant()
                finishAt shouldBe "2025-05-10T10:30:00Z".toInstant()
                meetingUrl shouldBe "meeting-url-3"
                recordingUrl shouldBe "recording-url-3"
                coverPhoto!!.run {
                    imageOriginalUrl shouldBe "photo-url2"
                    imageCompressedUrl shouldBe "photo-url2-comp"
                    imageBlurHash shouldBe "blur456"
                }
                trader.run {
                    traderId shouldBe 2.toUUID()
                    position shouldBe "Mentor"
                    firstName shouldBe "Jane"
                    lastName shouldBe "Smith"
                }
            }
        }
    }

    @Test
    fun `student gets their mentoring meetings but has no meetings should return empty list`() {
        // given
        val student1 = dataHelper.getStudent(id = 1.toUUID())
        val student2 = dataHelper.getStudent(id = 2.toUUID())

        val trader = dataHelper.getTrader()
        val product = dataHelper.getProduct(traderId = trader.id)

        dataHelper.getMentoring(
            studentId = student2.id, // different student
            productId = product.id,
        ).also { mentoring ->
            dataHelper.getMentoringMeeting(
                mentoringId = mentoring.id,
                startAt = "2025-05-15T11:00:00Z".toInstant(),
                finishAt = "2025-05-15T12:30:00Z".toInstant(),
            )
        }

        // when
        val result = underTest.handle(
            StudentGetsTheirMentoringMeetingsQuery(studentId = student1.id),
        )

        // then
        result.data.size shouldBe 0
    }

    @Test
    fun `student gets their mentoring meetings with trader without mentoring photo`() {
        // given
        val student = dataHelper.getStudent(id = 1.toUUID())
        val trader = dataHelper.getTrader(
            id = 1.toUUID(),
            firstName = "John",
            lastName = "Doe",
            // No mentoring photo set
        )

        val product = dataHelper.getProduct(traderId = trader.id)
        val mentoring = dataHelper.getMentoring(studentId = student.id, productId = product.id)

        dataHelper.getMentoringMeeting(
            id = 1.toUUID(),
            mentoringId = mentoring.id,
            startAt = "2025-04-20T10:00:00Z".toInstant(),
            finishAt = "2025-04-20T12:00:00Z".toInstant(),
            meetingUrl = "meeting-url-1",
        )

        // when
        val result = underTest.handle(
            StudentGetsTheirMentoringMeetingsQuery(studentId = student.id),
        )

        // then
        result.data.size shouldBe 1
        result.data.first().run {
            this.mentoringMeetingId shouldBe 1.toUUID()
            this.coverPhoto shouldBe null
            this.trader.run {
                traderId shouldBe 1.toUUID()
                firstName shouldBe "John"
                lastName shouldBe "Doe"
            }
        }
    }

    @Test
    fun `student gets their mentoring meetings but cancelled and rescheduled meetings are filtered out`() {
        // given
        val student = dataHelper.getStudent(id = 1.toUUID())
        val trader = dataHelper.getTrader(id = 1.toUUID())
        val product = dataHelper.getProduct(traderId = trader.id)
        val mentoring = dataHelper.getMentoring(studentId = student.id, productId = product.id)

        // Active meeting
        dataHelper.getMentoringMeeting(
            id = 1.toUUID(),
            mentoringId = mentoring.id,
            startAt = "2025-04-20T10:00:00Z".toInstant(),
            finishAt = "2025-04-20T12:00:00Z".toInstant(),
            meetingUrl = "meeting-url-1",
        )

        // Cancelled meeting - should be filtered out
        dataHelper.getMentoringMeeting(
            id = 2.toUUID(),
            mentoringId = mentoring.id,
            startAt = "2025-04-25T14:00:00Z".toInstant(),
            finishAt = "2025-04-25T16:00:00Z".toInstant(),
            meetingUrl = "meeting-url-2",
            entityModifier = {
                it.cancel(reason = "Test cancellation", initiatorType = InitiatorType.STUDENT)
            },
        )

        // Rescheduled meeting - should be filtered out
        dataHelper.getMentoringMeeting(
            id = 3.toUUID(),
            mentoringId = mentoring.id,
            startAt = "2025-05-10T09:00:00Z".toInstant(),
            finishAt = "2025-05-10T10:30:00Z".toInstant(),
            meetingUrl = "meeting-url-3",
            entityModifier = {
                it.reschedule(reason = "Test reschedule", initiatorType = InitiatorType.MENTOR)
            },
        )

        // when
        val result = underTest.handle(
            StudentGetsTheirMentoringMeetingsQuery(studentId = student.id),
        )

        // then
        result.data.size shouldBe 1
        result.data.first().run {
            mentoringMeetingId shouldBe 1.toUUID()
            startAt shouldBe "2025-04-20T10:00:00Z".toInstant()
            finishAt shouldBe "2025-04-20T12:00:00Z".toInstant()
            meetingUrl shouldBe "meeting-url-1"
        }
    }
}
