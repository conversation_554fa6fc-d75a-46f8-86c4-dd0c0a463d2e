package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamedocument.query.GetGameDocumentDetailQuery
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.gamedocument.constant.IssuingCompany
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.LocalDate

class GetGameDocumentDetailQueryHandlerTest @Autowired constructor(
    private val underTest: GetGameDocumentDetailQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should get game document detail - verify mappings`() {
        // given
        val user = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        val gameDocument = dataHelper.getGameDocument(
            id = 1.toUUID(),
            studentId = user.id,
            type = GameDocumentType.PAYOUT,
            issuingCompany = IssuingCompany.FTMO,
            payoutAmount = BigDecimal("1000.00"),
            reachedLevel = GameLevel.TWO,
            payoutDate = LocalDate.of(2023, 1, 15),
            truthScore = 95,
            scoreMessage = "Great achievement!",
            entityModifier = {
                it.changeGameDocumentFile(
                    fileId = dataHelper.getDocument(
                        id = 1.toUUID(),
                        type = FileType.GAME_DOCUMENT,
                    ).id,
                )
            },
        )

        // when
        val result = underTest.handle(
            GetGameDocumentDetailQuery(
                gameDocumentId = 1.toUUID(),
            ),
        )

        // then
        result.run {
            gameDocumentId shouldBe 1.toUUID()
            studentId shouldBe user.id
            type shouldBe GameDocumentType.PAYOUT
            issuingCompany shouldBe IssuingCompany.FTMO
            payoutAmount!! shouldBeEqualComparingTo BigDecimal("1000.00")
            reachedLevel shouldBe GameLevel.TWO
            payoutDate shouldBe LocalDate.of(2023, 1, 15)
            gameDocumentFile shouldNotBe null
            gameDocumentFile!!.run {
                documentId shouldBe 1.toUUID()
            }
            truthScore shouldBe 95
            state shouldBe GameDocumentApprovalState.WAITING
            denyMessage shouldBe null
            scoreMessage shouldBe "Great achievement!"
        }
    }

    @Test
    fun `should get game document detail with denied state`() {
        // given
        val user = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        val gameDocument = dataHelper.getGameDocument(
            id = 2.toUUID(),
            studentId = user.id,
            entityModifier = { it.denyAwaiting("Invalid certificate") },
        )

        // when
        val result = underTest.handle(
            GetGameDocumentDetailQuery(
                gameDocumentId = 2.toUUID(),
            ),
        )

        // then
        result.run {
            gameDocumentId shouldBe 2.toUUID()
            studentId shouldBe user.id
            state shouldBe GameDocumentApprovalState.DENIED
            denyMessage shouldBe "Invalid certificate"
        }
    }
}
