package com.cleevio.fundedmind.application.module.gamelevelreward

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamelevelreward.command.PublishGameLevelRewardCommand
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.gamelevelreward.GameLevelRewardRepository
import com.cleevio.fundedmind.domain.gamelevelreward.exception.GameLevelRewardMissingPictureException
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class PublishGameLevelRewardCommandHandlerTest @Autowired constructor(
    private val underTest: PublishGameLevelRewardCommandHandler,
    private val gameLevelRewardRepository: GameLevelRewardRepository,
) : IntegrationTest() {

    @Test
    fun `should publish level reward`() {
        // given
        dataHelper.getGameLevelReward(
            id = 1.toUUID(),
            entityModifier = {
                it.changeRewardPhoto(dataHelper.getImage(type = FileType.GAME_LEVEL_REWARD_PHOTO).id)
            },
        )

        // when
        underTest.handle(PublishGameLevelRewardCommand(gameLevelRewardId = 1.toUUID()))

        // then
        gameLevelRewardRepository.findByIdOrNull(1.toUUID())!!.run {
            published shouldBe true
        }
    }

    @Test
    fun `should throw if level reward is missing photo`() {
        // given
        dataHelper.getGameLevelReward(id = 1.toUUID())

        // when & then
        shouldThrow<GameLevelRewardMissingPictureException> {
            underTest.handle(PublishGameLevelRewardCommand(gameLevelRewardId = 1.toUUID()))
        }
    }
}
