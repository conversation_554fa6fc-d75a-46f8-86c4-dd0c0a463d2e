package com.cleevio.fundedmind.application.module.mentoring

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.mentoring.command.TraderCreatesManualMentoringCommand
import com.cleevio.fundedmind.application.module.product.exception.ProductNotRelatedToTraderException
import com.cleevio.fundedmind.domain.mentoring.MentoringRepository
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class TraderCreatesManualMentoringCommandHandlerTest @Autowired constructor(
    private val underTest: TraderCreatesManual<PERSON><PERSON><PERSON><PERSON><PERSON>mand<PERSON>and<PERSON>,
    private val mentoringRepository: MentoringRepository,
) : IntegrationTest() {

    @Test
    fun `should create mentoring successfully`() {
        // Given
        val trader = dataHelper.getTrader(id = 1.toUUID(), hubspotPropertyName = "trader_xxx")
        val student = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id)
        }

        val product = dataHelper.getProduct(
            id = 3.toUUID(),
            traderId = trader.id,
            name = "Test Product",
            altDescription = "Test Alt Description",
            sessionsCount = 5,
            validityInDays = 30,
        )

        every { hubspotService.updateCrmUserMentoring(any(), any(), any(), any()) } just Runs

        // When
        val result = underTest.handle(
            TraderCreatesManualMentoringCommand(
                traderId = trader.id,
                productId = product.id,
                studentId = student.id,
            ),
        )

        // Then
        mentoringRepository.findByIdOrNull(result.id)!!.run {
            sessionIdentifier shouldBe null
            studentId shouldBe student.id
            productId shouldBe product.id
            productName shouldBe "Test Product"
            productAltDescription shouldBe "Test Alt Description"
            sessionCount shouldBe 5
            usedSessions shouldBe 0
            expiresAt shouldNotBe null // Should be calculated based on validityInDays
        }

        verify {
            hubspotService.updateCrmUserMentoring(
                hubspotIdentifier = student.hubspotIdentifier,
                mentorPropertyName = trader.hubspotPropertyNameOrThrow,
                newSessionLeft = 5,
                allSessionsCount = 5,
            )
        }
    }

    @Test
    fun `should throw when product does not belong to trader`() {
        // Given
        val trader = dataHelper.getTrader(id = 1.toUUID(), hubspotPropertyName = "trader_xxx")
        val otherTrader = dataHelper.getTrader(id = 2.toUUID(), hubspotPropertyName = "other_trader")
        val student = dataHelper.getAppUser(id = 3.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id)
        }

        val product = dataHelper.getProduct(
            id = 4.toUUID(),
            traderId = otherTrader.id, // Product belongs to other trader
        )

        // When & Then
        shouldThrow<ProductNotRelatedToTraderException> {
            underTest.handle(
                TraderCreatesManualMentoringCommand(
                    traderId = trader.id,
                    productId = product.id,
                    studentId = student.id,
                ),
            )
        }
    }

    @Test
    fun `should create mentoring with null expiration when validityInDays is null`() {
        // Given
        val trader = dataHelper.getTrader(id = 1.toUUID(), hubspotPropertyName = "trader_xxx")
        val student = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id)
        }

        val product = dataHelper.getProduct(
            id = 3.toUUID(),
            traderId = trader.id,
            name = "Test Product",
            altDescription = "Test Alt Description",
            sessionsCount = 5,
            validityInDays = null, // No validity
        )

        every { hubspotService.updateCrmUserMentoring(any(), any(), any(), any()) } just Runs

        // When
        val result = underTest.handle(
            TraderCreatesManualMentoringCommand(
                traderId = trader.id,
                productId = product.id,
                studentId = student.id,
            ),
        )

        // Then
        mentoringRepository.findByIdOrNull(result.id)!!.expiresAt shouldBe null

        verify {
            hubspotService.updateCrmUserMentoring(
                hubspotIdentifier = student.hubspotIdentifier,
                mentorPropertyName = trader.hubspotPropertyNameOrThrow,
                newSessionLeft = 5,
                allSessionsCount = 5,
            )
        }
    }
}
