package com.cleevio.fundedmind.application.module.referral

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.referral.command.HideReferralCommand
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.referral.ReferralRepository
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class HideReferralCommandHandlerTest @Autowired constructor(
    private val underTest: HideReferralCommandHandler,
    private val referralRepository: ReferralRepository,
) : IntegrationTest() {

    @Test
    fun `should hide referral that was previously published`() {
        dataHelper.getReferral(
            id = 1.toUUID(),
            entityModifier = {
                it.changeImageDesktop(dataHelper.getImage(type = FileType.REFERRAL_DESKTOP_PHOTO).id)
                it.changeImageMobile(dataHelper.getImage(type = FileType.REFERRAL_MOBILE_PHOTO).id)
                it.publish()
            },
        )
        referralRepository.findByIdOrNull(1.toUUID())!!.published shouldBe true

        underTest.handle(HideReferralCommand(referralId = 1.toUUID()))

        referralRepository.findByIdOrNull(1.toUUID())!!.published shouldBe false
    }

    @Test
    fun `hiding referral that was not published should not throw`() {
        dataHelper.getReferral(id = 1.toUUID())

        referralRepository.findByIdOrNull(1.toUUID())!!.published shouldBe false

        underTest.handle(HideReferralCommand(referralId = 1.toUUID()))

        referralRepository.findByIdOrNull(1.toUUID())!!.published shouldBe false
    }
}
