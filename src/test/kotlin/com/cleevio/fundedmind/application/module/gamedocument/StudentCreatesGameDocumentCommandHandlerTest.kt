package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamedocument.command.StudentCreatesGameDocumentCommand
import com.cleevio.fundedmind.application.module.user.student.exception.StudentHasInsufficientGameLevelException
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.gamedocument.GameDocumentRepository
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.gamedocument.constant.IssuingCompany
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentTruthScoreCannotBeNegativeException
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

class StudentCreatesGameDocumentCommandHandlerTest @Autowired constructor(
    private val underTest: StudentCreatesGameDocumentCommandHandler,
    private val gameDocumentRepository: GameDocumentRepository,
) : IntegrationTest() {

    @Test
    fun `should throw exception when student has insufficient game level`() {
        // Create a student with ZERO game level
        val user = dataHelper.getAppUser(id = 110.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.BASECAMP, gameLevel = GameLevel.ZERO)
        }

        // Attempt to create a game document with a student that has insufficient game level
        shouldThrow<StudentHasInsufficientGameLevelException> {
            underTest.handle(
                defaultCommand(studentId = user.id),
            )
        }
    }

    @Test
    fun `should create backtesting document and upgrade to level TWO for student with STRATEGY completed`() {
        // Create a student
        val user = dataHelper.getAppUser(id = 111.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "John",
                lastName = "Doe",
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.ONE,
            )
        }

        // Create a strategy course and mark it as completed
        dataHelper.getCourse(
            courseCategory = CourseCategory.STRATEGY,
            traderId = dataHelper.getTrader().id,
        ).also { course ->
            dataHelper.getCourseProgress(
                userId = user.id,
                courseId = course.id,
                finishedAt = "2025-09-01T10:00:00Z".toInstant(),
            )
        }

        every {
            zapierService.newGameDocument(
                studentName = any(),
                gameDocumentId = any(),
                gameDocumentType = any(),
                payoutAmount = any(),
                payoutDate = any(),
                truthScore = any(),
                scoreMessage = any(),
            )
        } just Runs

        val result = underTest.handle(
            defaultCommand(
                studentId = user.id,
                type = GameDocumentType.BACKTESTING,
                payoutAmount = null,
                scoreMessage = "Backtesting looks fine!",
            ),
        )

        gameDocumentRepository.findByIdOrNull(result.id)!!.run {
            studentId shouldBe user.id
            type shouldBe GameDocumentType.BACKTESTING
            previousLevel shouldBe GameLevel.ONE
            reachedLevel shouldBe GameLevel.TWO
            state shouldBe GameDocumentApprovalState.WAITING
        }

        verify {
            zapierService.newGameDocument(
                studentName = "John Doe",
                gameDocumentId = result.id,
                gameDocumentType = GameDocumentType.BACKTESTING,
                payoutAmount = null,
                payoutDate = LocalDate.now(),
                truthScore = 95,
                scoreMessage = "Backtesting looks fine!",
            )
        }
    }

    @Test
    fun `should create certificate document`() {
        // Create a student
        val user = dataHelper.getAppUser(id = 113.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "John",
                lastName = "Doe",
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.TWO,
            )
        }

        every {
            zapierService.newGameDocument(
                studentName = any(),
                gameDocumentId = any(),
                gameDocumentType = any(),
                payoutAmount = any(),
                payoutDate = any(),
                truthScore = any(),
                scoreMessage = any(),
            )
        } just Runs

        val result = underTest.handle(
            defaultCommand(
                studentId = user.id,
                type = GameDocumentType.CERTIFICATE,
                payoutAmount = null,
                scoreMessage = "Certificate looks fine!",
            ),
        )

        gameDocumentRepository.findByIdOrNull(result.id)!!.run {
            studentId shouldBe user.id
            type shouldBe GameDocumentType.CERTIFICATE
            previousLevel shouldBe GameLevel.TWO
            reachedLevel shouldBe GameLevel.THREE
            state shouldBe GameDocumentApprovalState.WAITING
        }

        verify {
            zapierService.newGameDocument(
                studentName = "John Doe",
                gameDocumentId = result.id,
                gameDocumentType = GameDocumentType.CERTIFICATE,
                payoutAmount = null,
                payoutDate = LocalDate.now(),
                truthScore = 95,
                scoreMessage = "Certificate looks fine!",
            )
        }
    }

    @Test
    fun `should create payout document`() {
        // Create a student
        val student = dataHelper.getAppUser(id = 116.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "John",
                lastName = "Doe",
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.THREE,
            )
        }

        every {
            zapierService.newGameDocument(
                studentName = any(),
                gameDocumentId = any(),
                gameDocumentType = any(),
                payoutAmount = any(),
                payoutDate = any(),
                truthScore = any(),
                scoreMessage = any(),
            )
        } just Runs

        val result = underTest.handle(
            defaultCommand(
                studentId = student.id,
                type = GameDocumentType.PAYOUT,
                payoutAmount = 13_000.toBigDecimal(),
                scoreMessage = "Valid payout 95%",
            ),
        )

        gameDocumentRepository.findByIdOrNull(result.id)!!.run {
            studentId shouldBe student.id
            type shouldBe GameDocumentType.PAYOUT
            payoutAmount!! shouldBeEqualComparingTo 13_000.toBigDecimal()
            previousLevel shouldBe GameLevel.THREE
            reachedLevel shouldBe GameLevel.SIX // With new system, 13k is enough for level SIX (requires 10k)
            state shouldBe GameDocumentApprovalState.WAITING
        }

        verify {
            zapierService.newGameDocument(
                studentName = "John Doe",
                gameDocumentId = result.id,
                gameDocumentType = GameDocumentType.PAYOUT,
                payoutAmount = 13_000.toBigDecimal(),
                payoutDate = LocalDate.now(),
                truthScore = 95,
                scoreMessage = "Valid payout 95%",
            )
        }
    }

    @Test
    fun `should throw exception when truth score is negative`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 108.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Attempt to create with negative truth score
        shouldThrow<GameDocumentTruthScoreCannotBeNegativeException> {
            underTest.handle(
                defaultCommand(
                    studentId = user.id,
                    truthScore = -10,
                ),
            )
        }
    }

    private fun defaultCommand(
        studentId: UUID,
        type: GameDocumentType = GameDocumentType.PAYOUT,
        issuingCompany: IssuingCompany = IssuingCompany.FOR_TRADERS,
        payoutAmount: BigDecimal? = BigDecimal("1000.00"),
        payoutDate: LocalDate = LocalDate.now(),
        truthScore: Int = 95,
        scoreMessage: String? = "Great achievement!",
    ) = StudentCreatesGameDocumentCommand(
        studentId = studentId,
        type = type,
        issuingCompany = issuingCompany,
        payoutAmount = payoutAmount,
        payoutDate = payoutDate,
        truthScore = truthScore,
        scoreMessage = scoreMessage,
    )
}
