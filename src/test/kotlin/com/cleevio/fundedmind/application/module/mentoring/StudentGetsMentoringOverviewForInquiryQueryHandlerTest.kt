package com.cleevio.fundedmind.application.module.mentoring

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.mentoring.query.StudentGetsMentoringOverviewForInquiryQuery
import com.cleevio.fundedmind.application.module.user.student.exception.StudentHasNoAccessToMentoringException
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderHasNoSaleableProductException
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.domain.user.trader.MentoringKeypoint
import com.cleevio.fundedmind.domain.user.trader.constant.TraderTag
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class StudentGetsMentoringOverviewForInquiryQueryHandlerTest @Autowired constructor(
    private val underTest: StudentGetsMentoringOverviewForInquiryQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should throw when student has no mentoring access`() {
        // given
        // BASECAMP tier doesn't have mentoring access
        val student = dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.BASECAMP).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.STUDENT)
        }

        val trader = dataHelper.getTrader(id = 2.toUUID())
            .also { dataHelper.getAppUser(id = it.id, userRole = UserRole.TRADER) }
            .also { dataHelper.getProduct(traderId = it.id, name = "Mentoring") }

        // when/then
        shouldThrow<StudentHasNoAccessToMentoringException> {
            underTest.handle(
                StudentGetsMentoringOverviewForInquiryQuery(
                    studentId = student.id,
                    traderId = trader.id,
                ),
            )
        }
    }

    @Test
    fun `should throw when trader has no saleable products`() {
        // given
        val student = dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.MASTERCLASS).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.STUDENT)
        }

        val trader = dataHelper.getTrader(id = 2.toUUID()).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.TRADER)
        }

        // when/then
        shouldThrow<TraderHasNoSaleableProductException> {
            underTest.handle(
                StudentGetsMentoringOverviewForInquiryQuery(
                    studentId = student.id,
                    traderId = trader.id,
                ),
            )
        }
    }

    @Test
    fun `should return trader overview with testimonials and profile picture`() {
        // given
        val student = dataHelper.getStudent(id = 1.toUUID(), studentTier = StudentTier.MASTERCLASS).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.STUDENT)
        }

        val trader = dataHelper
            .getTrader(
                id = 2.toUUID(),
                profilePictureFileId = dataHelper.getImage(
                    type = FileType.TRADER_PROFILE_PICTURE,
                    originalFileUrl = "url",
                    compressedFileUrl = "url-comp",
                    blurHash = "1",
                ).id,
                position = "Senior Trader",
                firstName = "John",
                lastName = "Smith",
                badgeColor = BadgeColor.BLUE_GRADIENT,
                tags = listOf(TraderTag.CRYPTO, TraderTag.FOREX),
                checkoutVideoUrl = "https://example.com/video",
                mentoringDescription = "Expert mentoring services",
                biography = "Experienced trader with 10+ years in financial markets",
                mentoringKeypoints = listOf(
                    MentoringKeypoint("Title1", "Text1"),
                    MentoringKeypoint("Title2", "Text2"),
                ),
            )
            .also { dataHelper.getAppUser(id = it.id, userRole = UserRole.TRADER) }
            .also { dataHelper.getProduct(traderId = it.id, name = "Mentoring Product") }

        // Create testimonials
        val testimonialWithoutPicture = dataHelper.getMentoringTestimonial(
            id = 1.toUUID(),
            traderId = trader.id,
            firstName = "Jane",
            lastName = "Doe",
            rating = 4,
            description = "Very helpful sessions!",
        )

        val testimonialWithPicture = dataHelper.getMentoringTestimonial(
            id = 2.toUUID(),
            traderId = trader.id,
            profilePictureFileId = dataHelper.getImage(
                type = FileType.MENTORING_TESTIMONIAL_PICTURE,
                originalFileUrl = "https://example.com/testimonial-original.jpg",
                compressedFileUrl = "https://example.com/testimonial-compressed.jpg",
                blurHash = "testimonial-blur-hash",
            ).id,
            firstName = "Alex",
            lastName = "Johnson",
            rating = 5,
            description = "Excellent mentoring experience!",
        )

        // when
        val result = underTest.handle(
            StudentGetsMentoringOverviewForInquiryQuery(
                studentId = student.id,
                traderId = trader.id,
            ),
        )

        // then
        result.run {
            traderId shouldBe trader.id
            position shouldBe "Senior Trader"
            firstName shouldBe "John"
            lastName shouldBe "Smith"
            badgeColor shouldBe BadgeColor.BLUE_GRADIENT
            tags shouldBe listOf(TraderTag.CRYPTO, TraderTag.FOREX)
            checkoutVideoUrl shouldBe "https://example.com/video"
            mentoringDescription shouldBe "Expert mentoring services"
            biography shouldBe "Experienced trader with 10+ years in financial markets"
            profilePicture shouldNotBe null
            profilePicture!!.run {
                imageOriginalUrl shouldBe "url"
                imageCompressedUrl shouldBe "url-comp"
                imageBlurHash shouldBe "1"
            }

            testimonials shouldHaveSize 2
            testimonials.find { it.id == testimonialWithoutPicture.id }!!.run {
                id shouldBe 1.toUUID()
                name shouldBe "Jane D."
                rating shouldBe 4
                description shouldBe "Very helpful sessions!"
                picture shouldBe null
            }
            testimonials.find { it.id == testimonialWithPicture.id }!!.run {
                id shouldBe 2.toUUID()
                name shouldBe "Alex J."
                rating shouldBe 5
                description shouldBe "Excellent mentoring experience!"
                picture shouldNotBe null
            }

            mentoringKeypoints shouldHaveSize 2
            mentoringKeypoints[0].run {
                title shouldBe "Title1"
                text shouldBe "Text1"
            }
            mentoringKeypoints[1].run {
                title shouldBe "Title2"
                text shouldBe "Text2"
            }
        }
    }
}
