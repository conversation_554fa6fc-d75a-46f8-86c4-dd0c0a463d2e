package com.cleevio.fundedmind.application.module.mentoringtestimonial

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.mentoringtestimonial.command.UpdateMentoringTestimonialCommand
import com.cleevio.fundedmind.domain.mentoringtestimonial.MentoringTestimonialRepository
import com.cleevio.fundedmind.domain.mentoringtestimonial.exception.MentoringTestimonialNotFoundException
import com.cleevio.fundedmind.domain.mentoringtestimonial.exception.MentoringTestimonialNotRelatedToTraderException
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class UpdateMentoringTestimonialCommandHandlerTest @Autowired constructor(
    private val underTest: UpdateMentoringTestimonialCommandHandler,
    private val mentoringTestimonialRepository: MentoringTestimonialRepository,
) : IntegrationTest() {

    @Test
    fun `should update mentoring testimonial`() {
        // given
        val trader = dataHelper.getTrader(id = 1.toUUID())

        val testimonial = dataHelper.getMentoringTestimonial(
            traderId = trader.id,
            firstName = "Original",
            lastName = "Name",
            rating = 3,
            description = "Original description",
        )

        // when
        underTest.handle(
            UpdateMentoringTestimonialCommand(
                testimonialId = testimonial.id,
                traderId = trader.id,
                firstName = "Updated",
                lastName = "Person",
                rating = 8,
                description = "Updated description",
                pictureFileId = null,
            ),
        )

        // then
        mentoringTestimonialRepository.findByIdOrNull(testimonial.id)!!.run {
            traderId shouldBe traderId
            firstName shouldBe "Updated"
            lastName shouldBe "Person"
            rating shouldBe 8
            description shouldBe "Updated description"
            pictureFileId shouldBe null
        }
    }

    @Test
    fun `should throw if testimonial does not exist`() {
        // given
        val trader = dataHelper.getTrader(id = 1.toUUID())

        // when/then
        shouldThrow<MentoringTestimonialNotFoundException> {
            underTest.handle(
                UpdateMentoringTestimonialCommand(
                    testimonialId = 999.toUUID(),
                    traderId = trader.id,
                    firstName = "John",
                    lastName = "Doe",
                    rating = 5,
                    description = "Great mentoring experience!",
                    pictureFileId = null,
                ),
            )
        }
    }

    @Test
    fun `should throw if testimonial is not related to trader`() {
        // given
        val trader1 = dataHelper.getTrader(id = 1.toUUID())
        val trader2 = dataHelper.getTrader(id = 2.toUUID())

        val testimonial = dataHelper.getMentoringTestimonial(
            traderId = trader1.id,
            firstName = "Original",
            lastName = "Name",
            rating = 3,
            description = "Original description",
        )

        // when/then
        shouldThrow<MentoringTestimonialNotRelatedToTraderException> {
            underTest.handle(
                UpdateMentoringTestimonialCommand(
                    testimonialId = testimonial.id,
                    traderId = trader2.id, // different trader
                    firstName = "Updated",
                    lastName = "Person",
                    rating = 8,
                    description = "Updated description",
                    pictureFileId = null,
                ),
            )
        }
    }
}
