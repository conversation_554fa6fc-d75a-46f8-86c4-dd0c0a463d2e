package com.cleevio.fundedmind.application.module.summerbootcamp

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.summerbootcamp.port.out.SummerBootcampPort
import com.cleevio.fundedmind.application.module.summerbootcamp.query.PublicGetSummerBootcampRemainingSpotQuery
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class PublicGetSummerBootcampRemainingSpotQueryHandlerTest @Autowired constructor(
    private val underTest: PublicGetSummerBootcampRemainingSpotQueryHandler,
    private val summerBootcampPort: SummerBootcampPort,
) : IntegrationTest() {

    @Test
    fun `should get summer bootcamp remaining spots`() {
        // given
        val testValue = 75
        summerBootcampPort.updateRemainingSpots(testValue)

        // when
        val result = underTest.handle(PublicGetSummerBootcampRemainingSpotQuery())

        // then
        result.remainingSpots shouldBe testValue
    }
}
