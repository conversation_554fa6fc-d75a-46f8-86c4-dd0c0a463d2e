package com.cleevio.fundedmind.application.module.progress

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.progress.query.GetFinishedCourseModuleRewardQuery
import com.cleevio.fundedmind.domain.common.AppButtonWithLink
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.course.Course
import com.cleevio.fundedmind.domain.course.exception.CourseIsLockedForUserException
import com.cleevio.fundedmind.domain.coursemodule.exception.CourseModuleNotFinishedException
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant
import java.time.temporal.ChronoUnit

class GetFinishedCourseModuleRewardQueryHandlerTest @Autowired constructor(
    private val underTest: GetFinishedCourseModuleRewardQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should get reward for finished course module - coupon code - verify mappings`() {
        // given
        val user1 = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT).also { user ->
            dataHelper.getStudent(id = user.id, studentTier = StudentTier.MASTERCLASS, entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            })
        }

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            courseCategory = CourseCategory.STRATEGY,
            traderId = dataHelper.getTrader().id,
            visibleToTiers = listOf(StudentTier.MASTERCLASS),
            visibleToDiscordUsers = true,
            entityModifier = { it.createPicturesAndPublish() },
        )

        val finishedAt = "2025-01-01T10:00:00Z".toInstant() // 01.01. 10:00
        val module = dataHelper.getCourseModule(
            id = 1.toUUID(),
            title = "Module",
            courseId = course.id,
            rewardDescription = "Reward Description",
            rewardCouponCode = "coupon",
            rewardButton = null,
            entityModifier = {
                it.changeRewardPicture(
                    fileId = dataHelper.getImage(
                        type = FileType.COURSE_MODULE_REWARD_PICTURE,
                        originalFileUrl = "reward-url",
                        compressedFileUrl = "reward-url-comp",
                        blurHash = "123",
                    ).id,
                )
            },
        ).also {
            dataHelper.getCourseModuleProgress(userId = user1.id, courseModuleId = it.id, finishedAt = finishedAt)
        }

        // when
        val result = underTest.handle(
            GetFinishedCourseModuleRewardQuery(
                userId = user1.id,
                courseId = course.id,
                courseModuleId = module.id,
            ),
        )

        // then
        result.reward shouldNotBe null
        result.reward!!.run {
            courseCategory shouldBe CourseCategory.STRATEGY
            title shouldBe "Module"
            description shouldBe "Reward Description"
            couponCode shouldBe "coupon"
            button shouldBe null
            picture shouldNotBe null
            picture!!.run {
                imageOriginalUrl shouldBe "reward-url"
                imageCompressedUrl shouldBe "reward-url-comp"
                imageBlurHash shouldBe "123"
            }
        }
    }

    @Test
    fun `should get reward for finished course module - button - verify mappings`() {
        // given
        val user1 = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT).also { user ->
            dataHelper.getStudent(id = user.id, studentTier = StudentTier.MASTERCLASS, entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            })
        }

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            courseCategory = CourseCategory.BASECAMP,
            traderId = dataHelper.getTrader().id,
            visibleToTiers = listOf(StudentTier.MASTERCLASS),
            visibleToDiscordUsers = true,
            entityModifier = { it.createPicturesAndPublish() },
        )

        val finishedAt = "2025-01-01T10:00:00Z".toInstant() // 01.01. 10:00
        val module = dataHelper.getCourseModule(
            id = 1.toUUID(),
            title = "Module",
            courseId = course.id,
            rewardDescription = "Reward Description",
            rewardCouponCode = null,
            rewardButton = AppButtonWithLink(
                text = "Button",
                color = Color.PURPLE,
                linkUrl = "url",
            ),
            entityModifier = {
                it.changeRewardPicture(
                    fileId = dataHelper.getImage(
                        type = FileType.COURSE_MODULE_REWARD_PICTURE,
                        originalFileUrl = "reward-url",
                        compressedFileUrl = "reward-url-comp",
                        blurHash = "123",
                    ).id,
                )
            },
        ).also {
            dataHelper.getCourseModuleProgress(userId = user1.id, courseModuleId = it.id, finishedAt = finishedAt)
        }

        // when
        val result = underTest.handle(
            GetFinishedCourseModuleRewardQuery(
                userId = user1.id,
                courseId = course.id,
                courseModuleId = module.id,
            ),
        )

        // then
        result.reward shouldNotBe null
        result.reward!!.run {
            courseCategory shouldBe CourseCategory.BASECAMP
            title shouldBe "Module"
            description shouldBe "Reward Description"
            couponCode shouldBe null
            button shouldNotBe null
            button!!.run {
                text shouldBe "Button"
                color shouldBe Color.PURPLE
                linkUrl shouldBe "url"
            }
            picture shouldNotBe null
            picture!!.run {
                imageOriginalUrl shouldBe "reward-url"
                imageCompressedUrl shouldBe "reward-url-comp"
                imageBlurHash shouldBe "123"
            }
        }
    }

    @Test
    fun `should not get reward if course module has no reward`() {
        // given
        val user1 = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT).also { user ->
            dataHelper.getStudent(id = user.id, studentTier = StudentTier.MASTERCLASS, entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            })
        }

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader().id,
            visibleToTiers = listOf(StudentTier.MASTERCLASS),
            visibleToDiscordUsers = true,
            entityModifier = { it.createPicturesAndPublish() },
        )

        val finishedAt = "2025-01-01T10:00:00Z".toInstant() // 01.01. 10:00
        val module = dataHelper.getCourseModule(
            id = 1.toUUID(),
            courseId = course.id,
            rewardDescription = null,
            rewardCouponCode = null,
            rewardButton = null,
        ).also {
            dataHelper.getCourseModuleProgress(userId = user1.id, courseModuleId = it.id, finishedAt = finishedAt)
        }

        // when
        val result = underTest.handle(
            GetFinishedCourseModuleRewardQuery(
                userId = user1.id,
                courseId = course.id,
                courseModuleId = module.id,
            ),
        )

        // then
        result.reward shouldBe null
    }

    @Test
    fun `should throw if course module is not finished`() {
        // given
        val user1 = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT).also { user ->
            dataHelper.getStudent(id = user.id, studentTier = StudentTier.MASTERCLASS, entityModifier = {
                it.activateDiscordSubscription(Instant.now().plus(30, ChronoUnit.DAYS))
            })
        }

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader().id,
            visibleToTiers = listOf(StudentTier.MASTERCLASS),
            visibleToDiscordUsers = true,
            entityModifier = { it.createPicturesAndPublish() },
        )

        val unfinishedModule = dataHelper.getCourseModule(id = 1.toUUID(), courseId = course.id)

        // when/then
        shouldThrow<CourseModuleNotFinishedException> {
            underTest.handle(
                GetFinishedCourseModuleRewardQuery(
                    userId = user1.id,
                    courseId = course.id,
                    courseModuleId = unfinishedModule.id,
                ),
            )
        }
    }

    @Test
    fun `should throw if course module is not accessible to user`() {
        // given
        val user1 = dataHelper.getAppUser(id = 0.toUUID(), userRole = UserRole.STUDENT).also { user ->
            dataHelper.getStudent(id = user.id, studentTier = StudentTier.BASECAMP)
        }

        val course = dataHelper.getCourse(
            id = 1.toUUID(),
            traderId = dataHelper.getTrader().id,
            visibleToTiers = listOf(StudentTier.MASTERCLASS),
            visibleToDiscordUsers = true,
            entityModifier = { it.createPicturesAndPublish() },
        )
        val courseModule = dataHelper.getCourseModule(id = 2.toUUID(), courseId = course.id).also {
            dataHelper.getLesson(id = 3.toUUID(), courseModuleId = it.id)
        }

        // when/then
        shouldThrow<CourseIsLockedForUserException> {
            underTest.handle(
                GetFinishedCourseModuleRewardQuery(
                    userId = user1.id,
                    courseId = course.id,
                    courseModuleId = courseModule.id,
                ),
            )
        }
    }

    private fun Course.createPicturesAndPublish() = with(this) {
        changeIntroPictureDesktop(fileId = dataHelper.getImage(type = FileType.COURSE_DESKTOP_INTRO_PHOTO).id)
        changeIntroPictureMobile(fileId = dataHelper.getImage(type = FileType.COURSE_MOBILE_INTRO_PHOTO).id)
        publish()
    }
}
