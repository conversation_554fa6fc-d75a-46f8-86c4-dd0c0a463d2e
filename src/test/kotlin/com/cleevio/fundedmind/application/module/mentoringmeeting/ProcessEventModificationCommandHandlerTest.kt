package com.cleevio.fundedmind.application.module.mentoringmeeting

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.mentoringmeeting.command.ProcessEventModificationCommand
import com.cleevio.fundedmind.domain.mentoring.MentoringRepository
import com.cleevio.fundedmind.domain.mentoringmeeting.MentoringMeetingRepository
import com.cleevio.fundedmind.domain.mentoringmeeting.constant.InitiatorType
import com.cleevio.fundedmind.domain.mentoringmeeting.constant.ModificationType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class ProcessEventModificationCommandHandlerTest @Autowired constructor(
    private val underTest: ProcessEventModification<PERSON>ommandHandler,
    private val mentoringMeetingRepository: MentoringMeetingRepository,
    private val mentoringRepository: MentoringRepository,
) : IntegrationTest() {

    @Test
    fun `should cancel mentoring meeting when cancellation event received`() {
        // given
        val student = dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.STUDENT,
            email = "<EMAIL>",
        ).also {
            dataHelper.getStudent(id = it.id)
        }

        val trader = dataHelper.getTrader(
            id = 2.toUUID(),
            calendlyUrl = "calendly-url",
            calendlyUserUri = "calendly-user-uri",
        )

        val product = dataHelper.getProduct(
            id = 3.toUUID(),
            sessionsCount = 5,
            traderId = trader.id,
        )

        val mentoring = dataHelper.getMentoring(
            id = 4.toUUID(),
            studentId = student.id,
            productId = product.id,
            sessionCount = 5,
            useSessions = 1,
        )

        val mentoringMeeting = dataHelper.getMentoringMeeting(
            id = 5.toUUID(),
            mentoringId = mentoring.id,
            calendlyEventUri = "event-uri",
            startAt = "2025-08-15T14:00:00Z".toInstant(),
            finishAt = "2025-08-15T15:00:00Z".toInstant(),
        )

        // when
        underTest.handle(
            ProcessEventModificationCommand(
                traderUri = "calendly-user-uri",
                studentEmail = "<EMAIL>",
                scheduledEventUri = "event-uri",
                reason = "Need to cancel",
                initiator = InitiatorType.STUDENT,
                type = ModificationType.CANCELLATION,
            ),
        )

        // then
        mentoringRepository.findByIdOrNull(mentoring.id)!!.usedSessions shouldBe 0

        mentoringMeetingRepository.findByIdOrNull(mentoringMeeting.id)!!.run {
            isCancelled shouldBe true
            modification shouldNotBe null
            modification!!.run {
                reason shouldBe "Need to cancel"
                initiator shouldBe InitiatorType.STUDENT
                type shouldBe ModificationType.CANCELLATION
            }
        }
    }

    @Test
    fun `should reschedule mentoring meeting when reschedule event received`() {
        // given
        val student = dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.STUDENT,
            email = "<EMAIL>",
        ).also {
            dataHelper.getStudent(id = it.id)
        }

        val trader = dataHelper.getTrader(
            id = 2.toUUID(),
            calendlyUrl = "calendly-url",
            calendlyUserUri = "calendly-user-uri",
        )

        val product = dataHelper.getProduct(
            id = 3.toUUID(),
            sessionsCount = 5,
            traderId = trader.id,
        )

        val mentoring = dataHelper.getMentoring(
            id = 4.toUUID(),
            studentId = student.id,
            productId = product.id,
            sessionCount = 5,
            useSessions = 1,
        )

        val mentoringMeeting = dataHelper.getMentoringMeeting(
            id = 5.toUUID(),
            mentoringId = mentoring.id,
            calendlyEventUri = "event-uri",
            startAt = "2025-08-15T14:00:00Z".toInstant(),
            finishAt = "2025-08-15T15:00:00Z".toInstant(),
        )

        // when
        underTest.handle(
            ProcessEventModificationCommand(
                traderUri = "calendly-user-uri",
                studentEmail = "<EMAIL>",
                scheduledEventUri = "event-uri",
                reason = "Need to reschedule",
                initiator = InitiatorType.MENTOR,
                type = ModificationType.RESCHEDULE,
            ),
        )

        // then
        mentoringRepository.findByIdOrNull(mentoring.id)!!.usedSessions shouldBe 0

        mentoringMeetingRepository.findByIdOrNull(mentoringMeeting.id)!!.run {
            isRescheduled shouldBe true
            modification shouldNotBe null
            modification!!.run {
                reason shouldBe "Need to reschedule"
                initiator shouldBe InitiatorType.MENTOR
                type shouldBe ModificationType.RESCHEDULE
            }
        }
    }

    @Test
    fun `should handle when user not found`() {
        // given
        val trader = dataHelper.getTrader(
            id = 2.toUUID(),
            calendlyUrl = "calendly-url",
            calendlyUserUri = "calendly-user-uri",
        )

        // when - should not throw
        underTest.handle(
            ProcessEventModificationCommand(
                traderUri = "calendly-user-uri",
                studentEmail = "<EMAIL>",
                scheduledEventUri = "event-uri",
                reason = "Need to reschedule",
                initiator = InitiatorType.STUDENT,
                type = ModificationType.CANCELLATION,
            ),
        )

        // then - no exception should be thrown
    }

    @Test
    fun `should handle when user has wrong role`() {
        // given
        val adminUser = dataHelper.getAppUser(
            id = 1.toUUID(),
            userRole = UserRole.ADMIN, // non-student user
            email = "<EMAIL>",
        )

        val trader = dataHelper.getTrader(
            id = 2.toUUID(),
            calendlyUrl = "calendly-url",
            calendlyUserUri = "calendly-user-uri",
        )

        // when - should not throw
        underTest.handle(
            ProcessEventModificationCommand(
                traderUri = "calendly-user-uri",
                studentEmail = "<EMAIL>", // User with wrong role
                scheduledEventUri = "event-uri",
                reason = "Need to reschedule",
                initiator = InitiatorType.STUDENT,
                type = ModificationType.CANCELLATION,
            ),
        )

        // then - no exception should be thrown
    }
}
