package com.cleevio.fundedmind.application.module.mentoringmeeting

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.mentoringmeeting.command.TraderUpdatesMentoringMeetingRecordingCommand
import com.cleevio.fundedmind.domain.mentoringmeeting.MentoringMeetingRepository
import com.cleevio.fundedmind.domain.mentoringmeeting.exception.MentoringMeetingNotAccessibleException
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull

class TraderUpdatesMentoringMeetingRecordingCommandHandlerTest @Autowired constructor(
    private val underTest: TraderUpdatesMentoringMeetingRecordingCommandHandler,
    private val mentoringMeetingRepository: MentoringMeetingRepository,
) : IntegrationTest() {

    @Test
    fun `should update mentoring meeting recording URL`() {
        // Given
        val trader = dataHelper.getTrader(id = 1.toUUID()).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.TRADER)
        }

        val mentoringMeeting = dataHelper.getMentoringMeeting(
            id = 1.toUUID(),
            mentoringId = dataHelper.getMentoring(
                productId = dataHelper.getProduct(traderId = trader.id).id,
                studentId = dataHelper.getStudent().id,
            ).id,
        )

        // When
        underTest.handle(
            TraderUpdatesMentoringMeetingRecordingCommand(
                traderId = trader.id,
                mentoringMeetingId = mentoringMeeting.id,
                recordingUrl = "recording-url",
            ),
        )

        // Then
        mentoringMeetingRepository.findByIdOrNull(1.toUUID())!!.recordingUrl shouldBe "recording-url"
    }

    @Test
    fun `should remove recording url when null is provided`() {
        // Given
        val trader = dataHelper.getTrader(id = 1.toUUID()).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.TRADER)
        }

        val mentoringMeeting = dataHelper.getMentoringMeeting(
            id = 1.toUUID(),
            mentoringId = dataHelper.getMentoring(
                productId = dataHelper.getProduct(traderId = trader.id).id,
                studentId = dataHelper.getStudent().id,
            ).id,
            entityModifier = {
                it.updateRecordingUrl("recording-url")
            },
        )

        // When
        underTest.handle(
            TraderUpdatesMentoringMeetingRecordingCommand(
                traderId = trader.id,
                mentoringMeetingId = mentoringMeeting.id,
                recordingUrl = null,
            ),
        )

        // Then
        mentoringMeetingRepository.findByIdOrNull(1.toUUID())!!.recordingUrl shouldBe null
    }

    @Test
    fun `should throw when trader tries to update meeting that doesn't belong to them`() {
        // Given
        val trader = dataHelper.getTrader(id = 1.toUUID()).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.TRADER)
        }
        val otherTrader = dataHelper.getTrader(id = 2.toUUID()).also {
            dataHelper.getAppUser(id = it.id, userRole = UserRole.TRADER)
        }

        val mentoringMeeting = dataHelper.getMentoringMeeting(
            id = 1.toUUID(),
            mentoringId = dataHelper.getMentoring(
                productId = dataHelper.getProduct(traderId = trader.id).id,
                studentId = dataHelper.getStudent().id,
            ).id,
        )

        // When & Then
        shouldThrow<MentoringMeetingNotAccessibleException> {
            underTest.handle(
                TraderUpdatesMentoringMeetingRecordingCommand(
                    traderId = otherTrader.id,
                    mentoringMeetingId = mentoringMeeting.id,
                    recordingUrl = "recording-url",
                ),
            )
        }
    }
}
