package com.cleevio.fundedmind.application.module.gamelevelreward

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamelevelreward.query.ListGameLevelRewardsQuery
import com.cleevio.fundedmind.domain.common.AppButtonWithLink
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.gamelevelreward.constant.GameLevelRewardType
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class ListGameLevelRewardsQueryHandlerTest @Autowired constructor(
    private val underTest: ListGameLevelRewardsQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should list level rewards - verify mappings`() {
        // given
        dataHelper.getGameLevelReward(
            id = 1.toUUID(),
            name = "Level Reward 1",
            gameLevel = GameLevel.THREE,
            type = GameLevelRewardType.PHYSICAL,
            description = "Level reward description",
            rewardCouponCode = "REWARD123",
            rewardButton = AppButtonWithLink("Claim Reward", Color.BLUE, "https://example.com/claim"),
            entityModifier = {
                it.changeRewardPhoto(
                    fileId = dataHelper.getImage(
                        type = FileType.GAME_LEVEL_REWARD_PHOTO,
                        originalFileUrl = "reward-photo-url",
                        compressedFileUrl = "reward-photo-url-comp",
                        blurHash = "789",
                    ).id,
                )
            },
        )

        // when
        val result = underTest.handle(ListGameLevelRewardsQuery())

        // then
        result.data shouldHaveSize 1
        result.data.single().run {
            gameLevelRewardId shouldBe 1.toUUID()
            name shouldBe "Level Reward 1"
            gameLevel shouldBe GameLevel.THREE
            type shouldBe GameLevelRewardType.PHYSICAL
            published shouldBe false
            rewardPhoto shouldNotBe null
            rewardPhoto!!.run {
                imageOriginalUrl shouldBe "reward-photo-url"
                imageCompressedUrl shouldBe "reward-photo-url-comp"
                imageBlurHash shouldBe "789"
            }
            description shouldBe "Level reward description"
            rewardCouponCode shouldBe "REWARD123"
            rewardButton!!.run {
                text shouldBe "Claim Reward"
                color shouldBe Color.BLUE
                linkUrl shouldBe "https://example.com/claim"
            }
        }
    }

    @Test
    fun `should list level rewards - should sort by game level ASC`() {
        // given
        dataHelper.getGameLevelReward(id = 1.toUUID(), gameLevel = GameLevel.THREE)
        dataHelper.getGameLevelReward(id = 2.toUUID(), gameLevel = GameLevel.ONE)
        dataHelper.getGameLevelReward(id = 3.toUUID(), gameLevel = GameLevel.FIVE)

        // when
        val result = underTest.handle(ListGameLevelRewardsQuery())

        // then
        result.data shouldHaveSize 3
        result.data.map { it.gameLevelRewardId } shouldBe listOf(2.toUUID(), 1.toUUID(), 3.toUUID())
        result.data.map { it.gameLevel } shouldBe listOf(GameLevel.ONE, GameLevel.THREE, GameLevel.FIVE)
    }

    @Test
    fun `should list level rewards - should filter out deleted level rewards`() {
        // given
        dataHelper.getGameLevelReward(id = 1.toUUID())
        dataHelper.getGameLevelReward(id = 2.toUUID(), entityModifier = { it.softDelete() })

        // when
        val result = underTest.handle(ListGameLevelRewardsQuery())

        // then
        result.data shouldHaveSize 1
        result.data.map { it.gameLevelRewardId } shouldContainExactlyInAnyOrder setOf(1.toUUID())
    }
}
