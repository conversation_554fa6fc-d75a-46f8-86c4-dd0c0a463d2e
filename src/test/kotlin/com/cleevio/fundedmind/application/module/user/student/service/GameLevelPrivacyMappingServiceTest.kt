package com.cleevio.fundedmind.application.module.user.student.service

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.LevelVisibility
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GameLevelPrivacyMappingServiceTest @Autowired constructor(
    private val underTest: GameLevelPrivacyMappingService,
) : IntegrationTest() {

    @Test
    fun `admin should always see game level regardless of level visibility`() {
        // given
        val admin = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.ADMIN)

        // when - level visibility is ENABLED
        val resultWithEnabledVisibility = underTest.gameLevelOrNull(
            meUserId = admin.id,
            studentGameLevel = GameLevel.FIVE,
            studentGameLevelVisibility = LevelVisibility.ENABLED,
        )

        // then
        resultWithEnabledVisibility shouldBe GameLevel.FIVE

        // when - level visibility is DISABLED
        val resultWithDisabledVisibility = underTest.gameLevelOrNull(
            meUserId = admin.id,
            studentGameLevel = GameLevel.NINE,
            studentGameLevelVisibility = LevelVisibility.DISABLED,
        )

        // then
        resultWithDisabledVisibility shouldBe GameLevel.NINE
    }

    @Test
    fun `trader should always see game level regardless of level visibility`() {
        // given
        val trader = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.TRADER)

        // when - level visibility is ENABLED
        val resultWithEnabledVisibility = underTest.gameLevelOrNull(
            meUserId = trader.id,
            studentGameLevel = GameLevel.FIVE,
            studentGameLevelVisibility = LevelVisibility.ENABLED,
        )

        // then
        resultWithEnabledVisibility shouldBe GameLevel.FIVE

        // when - level visibility is DISABLED
        val resultWithDisabledVisibility = underTest.gameLevelOrNull(
            meUserId = trader.id,
            studentGameLevel = GameLevel.NINE,
            studentGameLevelVisibility = LevelVisibility.DISABLED,
        )

        // then
        resultWithDisabledVisibility shouldBe GameLevel.NINE
    }

    @Test
    fun `student should see game level only when level visibility is enabled`() {
        // given
        val student = dataHelper.getAppUser(id = 3.toUUID(), userRole = UserRole.STUDENT)

        // when - level visibility is ENABLED
        val resultWithEnabledVisibility = underTest.gameLevelOrNull(
            meUserId = student.id,
            studentGameLevel = GameLevel.FIVE,
            studentGameLevelVisibility = LevelVisibility.ENABLED,
        )

        // then
        resultWithEnabledVisibility shouldBe GameLevel.FIVE

        // when - level visibility is DISABLED
        val resultWithDisabledVisibility = underTest.gameLevelOrNull(
            meUserId = student.id,
            studentGameLevel = GameLevel.NINE,
            studentGameLevelVisibility = LevelVisibility.DISABLED,
        )

        // then
        resultWithDisabledVisibility shouldBe null
    }
}
