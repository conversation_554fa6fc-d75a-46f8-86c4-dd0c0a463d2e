package com.cleevio.fundedmind.application.module.product.command

import com.cleevio.fundedmind.application.common.command.Command
import com.cleevio.fundedmind.application.common.command.IdResult
import com.cleevio.fundedmind.application.common.type.StripeProductId
import com.cleevio.fundedmind.application.common.validation.NotBlankAndLimited
import jakarta.validation.constraints.Positive
import java.util.UUID

data class CreateNewProductCommand(
    @field:NotBlankAndLimited val name: String,
    @field:NotBlankAndLimited val stripeIdentifier: StripeProductId,
    @field:NotBlankAndLimited val description: String,
    @field:NotBlankAndLimited val altDescription: String,
    @field:Positive val sessionsCount: Int,
    val traderId: UUID,
    @field:Positive val validityInDays: Int?,
) : Command<IdResult>

data class UpdateProductCommand(
    val productId: UUID,
    @field:NotBlankAndLimited val name: String,
    @field:NotBlankAndLimited val stripeIdentifier: StripeProductId,
    @field:NotBlankAndLimited val description: String,
    @field:NotBlankAndLimited val altDescription: String,
    @field:Positive val sessionsCount: Int,
    val traderId: UUID,
    @field:Positive val validityInDays: Int?,
) : Command<Unit>

data class UpdateProductSaleabilityCommand(
    val productId: UUID,
    val saleable: Boolean,
) : Command<Unit>
