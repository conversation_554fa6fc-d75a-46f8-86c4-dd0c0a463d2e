package com.cleevio.fundedmind.application.module.progress.query

import com.cleevio.fundedmind.application.common.command.AppButtonWithLinkResult
import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.common.query.Query
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import io.swagger.v3.oas.annotations.media.Schema
import java.util.UUID

data class GetDashboardLessonToWatchQuery(
    val userId: UUID,
) : Query<GetDashboardLessonToWatchQuery.Result> {

    @Schema(name = "GetDashboardLessonToWatchResult")
    data class Result(
        val lessonId: UUID,
        val courseModuleId: UUID,
        val courseId: UUID,
        val title: String,
        val thumbnailUrl: String,
        val thumbnailAnimationUrl: String,
        val durationInSeconds: Int,
        val watchedSeconds: Int,
    )
}

data class GetProgressOverviewQuery(
    val userId: UUID,
) : Query<GetProgressOverviewQuery.Result> {

    @Schema(name = "GetProgressOverviewResult")
    data class Result(
        val overview: Map<CourseCategory, CourseCategoryProgress>,
    )

    @Schema(name = "GetProgressOverviewCategoryProgress")
    data class CourseCategoryProgress(
        val courseCategory: CourseCategory,
        val lessonsCount: Int,
        val lessonsFinishedCount: Int,
    ) {
        val isFinished: Boolean = lessonsFinishedCount == lessonsCount
    }
}

data class GetNextModuleToWatchQuery(
    val userId: UUID,
    val finishedModuleId: UUID,
) : Query<GetNextModuleToWatchQuery.Result> {

    @Schema(name = "GetNextModuleToWatchResult")
    data class Result(
        val nextModule: NextModule?,
    )

    @Schema(name = "GetNextModuleToWatchNextModule")
    data class NextModule(
        val moduleId: UUID,
        val moduleTitle: String,
        val firstLessonId: UUID,
    )
}

data class GetFinishedCourseModuleRewardQuery(
    val userId: UUID,
    val courseId: UUID,
    val courseModuleId: UUID,
) : Query<GetFinishedCourseModuleRewardQuery.Result> {

    @Schema(name = "GetFinishedCourseModuleRewardResult")
    data class Result(
        val reward: Reward?,
    )

    @Schema(name = "GetFinishedCourseModuleRewardReward")
    data class Reward(
        val courseCategory: CourseCategory,
        val title: String,
        val picture: ImageResult?,
        val description: String?,
        val couponCode: String?,
        val button: AppButtonWithLinkResult?,
    )
}
