package com.cleevio.fundedmind.application.module.user.student.command

import com.cleevio.fundedmind.application.common.command.Command
import jakarta.validation.constraints.NotBlank
import java.util.UUID

data class StudentConnectsDiscordCommand(
    val userId: UUID,
    @field:NotBlank val discordUserId: String,
    @field:NotBlank val discordUserName: String,
    @field:NotBlank val accessToken: String,
) : Command<Unit>
