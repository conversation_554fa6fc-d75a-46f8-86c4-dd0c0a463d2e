package com.cleevio.fundedmind.application.module.user.onboarding.command

import com.cleevio.fundedmind.adapter.`in`.rest.request.QuestionnaireInput
import com.cleevio.fundedmind.application.common.command.Command
import com.cleevio.fundedmind.application.common.command.UserLocationInput
import com.cleevio.fundedmind.application.common.validation.NotBlankAndLimited
import com.cleevio.fundedmind.application.common.validation.NullOrNotBlankAndLimited
import com.cleevio.fundedmind.application.common.validation.PhoneNumber
import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.user.student.constant.OnboardingState
import jakarta.validation.Valid
import java.util.UUID

data class ChooseBasecampTierCommand(
    val studentId: UUID,
) : Command<Unit>

data class ChangeOnboardingStateCommand(
    val studentId: UUID,
    val newState: OnboardingState,
) : Command<Unit>

data class SaveOnboardSurveyCommand(
    val studentId: UUID,
    @field:NotBlankAndLimited val firstName: String,
    @field:NotBlankAndLimited val lastName: String,
    @field:PhoneNumber val phone: String,
    @field:NullOrNotBlankAndLimited val biography: String?,
    val country: Country,
    @field:NotBlankAndLimited val firstNameVocative: String,
    @field:NotBlankAndLimited val lastNameVocative: String,

    @field:Valid val location: UserLocationInput?,
) : Command<Unit>

data class SaveOnboardQuestionnaireCommand(
    val studentId: UUID,
    val questionnaire: QuestionnaireInput,
) : Command<Unit>

data class FinishOnboardingCommand(
    val studentId: UUID,
) : Command<Unit>
