package com.cleevio.fundedmind.application.module.gamedocument.event.listener

import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserUpdatePort
import com.cleevio.fundedmind.application.module.crm.port.out.PatchCrmCustomerRequest
import com.cleevio.fundedmind.application.module.gamedocument.event.GameDocumentApprovedEvent
import com.cleevio.fundedmind.application.module.gamedocument.event.GameDocumentDeniedEvent
import com.cleevio.fundedmind.application.module.gamedocument.finder.GameDocumentFinderService
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class GameDocumentCrmEventListener(
    private val gameDocumentFinderService: GameDocumentFinderService,
    private val appUserFinderService: AppUserFinderService,
    private val crmUserUpdatePort: CrmUserUpdatePort,
) {

    @SentryTransaction(operation = "async.crm.game-document-approved")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleGameDocumentApprovedEvent(event: GameDocumentApprovedEvent) {
        val gameDocument = gameDocumentFinderService.getById(event.gameDocumentId)
        val appUser = appUserFinderService.getById(gameDocument.studentId)

        val totalPayout = gameDocumentFinderService.sumStudentApprovedPayouts(gameDocument.studentId)

        crmUserUpdatePort.patchCrmUser(
            PatchCrmCustomerRequest(
                hubspotIdentifier = appUser.hubspotIdentifier,
                totalPayout = totalPayout,
            ),
        )
    }

    @SentryTransaction(operation = "async.crm.game-document-denied")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleGameDocumentDeniedEvent(event: GameDocumentDeniedEvent) {
        val gameDocument = gameDocumentFinderService.getById(event.gameDocumentId)
        val appUser = appUserFinderService.getById(gameDocument.studentId)

        val totalPayout = gameDocumentFinderService.sumStudentApprovedPayouts(gameDocument.studentId)

        crmUserUpdatePort.patchCrmUser(
            PatchCrmCustomerRequest(
                hubspotIdentifier = appUser.hubspotIdentifier,
                totalPayout = totalPayout,
            ),
        )
    }
}
