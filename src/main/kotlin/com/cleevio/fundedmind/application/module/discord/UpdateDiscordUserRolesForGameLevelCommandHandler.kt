package com.cleevio.fundedmind.application.module.discord

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.port.out.DiscordUserRoleUpdatePort
import com.cleevio.fundedmind.application.module.discord.command.UpdateDiscordUserRolesForGameLevelCommand
import com.cleevio.fundedmind.application.module.user.student.finder.StudentDiscordFinderService
import org.springframework.stereotype.Service

@Service
class UpdateDiscordUserRolesForGameLevelCommandHandler(
    private val studentDiscordFinderService: StudentDiscordFinderService,
    private val discordUserRoleUpdatePort: DiscordUserRoleUpdatePort,
) : CommandHandler<Unit, UpdateDiscordUserRolesForGameLevelCommand> {

    override val command = UpdateDiscordUserRolesForGameLevelCommand::class

    override fun handle(command: UpdateDiscordUserRolesForGameLevelCommand) {
        val studentDiscord = studentDiscordFinderService.getByStudentIdNonDeleted(command.studentId)

        discordUserRoleUpdatePort.updateDiscordUserRolesForGameLevel(
            discordUserId = studentDiscord.discordId,
            gameLevel = command.gameLevel,
            hasDiscordPremiumAccess = true,
        )
    }
}
