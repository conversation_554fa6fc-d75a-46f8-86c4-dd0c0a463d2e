package com.cleevio.fundedmind.application.module.email.event.listener

import com.cleevio.fundedmind.application.module.email.SendEmailService
import com.cleevio.fundedmind.application.module.gamelevelprogress.event.GameLevelProgressCreatedEvent
import com.cleevio.fundedmind.application.module.gamelevelprogress.finder.GameLevelProgressFinderService
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class GameLevelProgressEmailEventListener(
    private val sendEmailService: SendEmailService,
    private val gameLevelProgressFinderService: GameLevelProgressFinderService,
) {

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleGameLevelProgressCreatedEvent(event: GameLevelProgressCreatedEvent) {
        val gameLevelProgress = gameLevelProgressFinderService.getById(event.gameLevelProgressId)
        if (gameLevelProgress.gameLevel == GameLevel.ZERO) {
            // game level zero does not have an email
            return
        }

        sendEmailService.sendEmailGameLevelGained(
            studentId = gameLevelProgress.studentId,
            gameLevel = gameLevelProgress.gameLevel,
        )
    }
}
