package com.cleevio.fundedmind.application.module.mentoring.event.listener

import com.cleevio.fundedmind.application.module.mentoring.service.UpdateUserMentoringInCrmService
import com.cleevio.fundedmind.application.module.mentoringmeeting.event.MentoringMeetingCreatedEvent
import com.cleevio.fundedmind.application.module.mentoringmeeting.finder.MentoringMeetingFinderService
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class MentoringMeetingMentoringEventListener(
    private val mentoringMeetingFinderService: MentoringMeetingFinderService,
    private val updateUserMentoringInCrmService: UpdateUserMentoringInCrmService,
) {

    @SentryTransaction(operation = "async.crm.mentoring-updated")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT) // called within TransactionTemplate
    fun handleMentoringMeetingCreatedEvent(event: MentoringMeetingCreatedEvent) {
        val mentoringId = mentoringMeetingFinderService.getById(event.mentoringMeetingId).mentoringId

        updateUserMentoringInCrmService.updateMentoringInCrm(mentoringId)
    }
}
