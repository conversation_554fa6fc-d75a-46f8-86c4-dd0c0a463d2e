package com.cleevio.fundedmind.application.module.gamelevelprogress.event.listener

import com.cleevio.fundedmind.application.module.gamelevelprogress.service.StudentGainsNewLevelService
import com.cleevio.fundedmind.application.module.user.student.event.StudentCreatedEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentTierUpdatedFromHubspotEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentTierUpdatedInAppEvent
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class StudentGameLevelProgressEventListener(
    private val studentFinderService: StudentFinderService,
    private val studentGainsNewLevelService: StudentGainsNewLevelService,
) {

    @SentryTransaction(operation = "async.progress.tier-updated-from-hubspot")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentTierUpdatedFromHubspotEvent(event: StudentTierUpdatedFromHubspotEvent) {
        val student = studentFinderService.getById(event.studentId)

        when (student.studentTier) {
            StudentTier.NO_TIER, StudentTier.BASECAMP -> return

            StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE -> studentGainsNewLevelService.studentGainsNewLevel(
                studentId = event.studentId,
                gameLevel = GameLevel.ONE,
            )
        }
    }

    @SentryTransaction(operation = "async.progress.student-created")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentCreatedEvent(event: StudentCreatedEvent) {
        // every student starts at level 0
        studentGainsNewLevelService.studentGainsNewLevel(
            studentId = event.studentId,
            gameLevel = GameLevel.ZERO,
        )
    }

    @SentryTransaction(operation = "async.progress.tier-updated-in-app")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentTierUpdatedInAppEvent(event: StudentTierUpdatedInAppEvent) {
        val student = studentFinderService.getById(event.studentId)

        when (student.studentTier) {
            StudentTier.NO_TIER -> return

            StudentTier.BASECAMP -> return

            StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE -> studentGainsNewLevelService.studentGainsNewLevel(
                studentId = event.studentId,
                gameLevel = GameLevel.ONE,
            )
        }
    }
}
