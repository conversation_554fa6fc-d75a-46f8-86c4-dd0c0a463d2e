package com.cleevio.fundedmind.application.module.gamelevelprogress.event.listener

import com.cleevio.fundedmind.application.module.gamelevelprogress.service.StudentGainsNewLevelService
import com.cleevio.fundedmind.application.module.user.student.event.StudentOnboardingFinishedEvent
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class OnboardingGameLevelProgressEventListener(
    private val studentFinderService: StudentFinderService,
    private val studentGainsNewLevelService: StudentGainsNewLevelService,
) {

    /**
     * Handles onboarding completion by ensuring students have appropriate game levels.
     *
     * This serves as a fallback mechanism to guarantee level assignment:
     * - Users should normally gain level 0 upon registration
     * - Users should normally gain level 1 upon purchasing masterclass
     * - This listener ensures minimum level requirements are met after onboarding completion
     */
    @SentryTransaction(operation = "async.progress.onboarding-finished")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleOnboardingFinishedEvent(event: StudentOnboardingFinishedEvent) {
        val student = studentFinderService.getById(event.studentId)

        when (student.studentTier) {
            StudentTier.NO_TIER, StudentTier.BASECAMP -> {
                studentGainsNewLevelService.studentGainsNewLevel(
                    studentId = event.studentId,
                    gameLevel = GameLevel.ZERO,
                )
            }

            StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE -> {
                studentGainsNewLevelService.studentGainsNewLevel(
                    studentId = event.studentId,
                    gameLevel = GameLevel.ZERO,
                )
                studentGainsNewLevelService.studentGainsNewLevel(
                    studentId = event.studentId,
                    gameLevel = GameLevel.ONE,
                )
            }
        }
    }
}
