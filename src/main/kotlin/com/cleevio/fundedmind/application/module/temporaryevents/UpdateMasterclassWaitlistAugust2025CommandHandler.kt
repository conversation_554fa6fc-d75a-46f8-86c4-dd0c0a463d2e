package com.cleevio.fundedmind.application.module.temporaryevents

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.module.temporaryevents.command.UpdateMasterclassWaitlistAugust2025Command
import com.cleevio.fundedmind.application.module.temporaryevents.port.out.TemporaryEventsPort
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class UpdateMasterclassWaitlistAugust2025CommandHandler(
    private val temporaryEventsPort: TemporaryEventsPort,
) : CommandHandler<Unit, UpdateMasterclassWaitlistAugust2025Command> {

    override val command = UpdateMasterclassWaitlistAugust2025Command::class

    @Transactional
    override fun handle(command: UpdateMasterclassWaitlistAugust2025Command) {
        temporaryEventsPort.updateMasterclassWaitlistAugust2025(newValue = command.newValue)
    }
}
