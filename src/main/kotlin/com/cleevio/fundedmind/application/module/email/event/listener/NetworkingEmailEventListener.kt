package com.cleevio.fundedmind.application.module.email.event.listener

import com.cleevio.fundedmind.application.module.email.SendEmailService
import com.cleevio.fundedmind.application.module.networking.event.NetworkingMessageCreatedEvent
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class NetworkingEmailEventListener(
    private val sendEmailService: SendEmailService,
) {

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleNetworkingMessageCreated(event: NetworkingMessageCreatedEvent) {
        sendEmailService.sendNetworkingMessage(
            networkingMessageId = event.messageId,
        )
    }
}
