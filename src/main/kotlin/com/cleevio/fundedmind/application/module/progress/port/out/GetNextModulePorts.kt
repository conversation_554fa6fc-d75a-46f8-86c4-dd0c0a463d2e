package com.cleevio.fundedmind.application.module.progress.port.out

import java.util.UUID

interface GetNextUnfinishedCourseModulePort {
    operator fun invoke(
        userId: UUID,
        currentCourseModuleId: UUID,
    ): NextUnfinishedCourseModule?

    data class NextUnfinishedCourseModule(
        val courseModuleId: UUID,
        val title: String,
    )
}

interface GetFirstUnfinishedLessonFromModulePort {
    operator fun invoke(
        courseModuleId: UUID,
        userId: UUID,
    ): FirstUnfinishedLessonFromModule?

    data class FirstUnfinishedLessonFromModule(
        val lessonId: UUID,
    )
}
