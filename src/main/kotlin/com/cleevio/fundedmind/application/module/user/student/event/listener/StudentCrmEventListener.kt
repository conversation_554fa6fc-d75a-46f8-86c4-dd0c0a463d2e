package com.cleevio.fundedmind.application.module.user.student.event.listener

import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserUpdatePort
import com.cleevio.fundedmind.application.module.crm.port.out.PatchCrmCustomerRequest
import com.cleevio.fundedmind.application.module.location.finder.UserLocationFinderService
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import com.cleevio.fundedmind.application.module.user.student.event.StudentConnectedDiscordEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentDisconnectedDiscordEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentDiscordSubscriptionActivatedEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentDiscordSubscriptionActivatedFromHubspotEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentDiscordSubscriptionEndedEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentDiscordSubscriptionEndedFromHubspotEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentDiscordSubscriptionUpdatedEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentGameLevelUpdatedEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentLocationUpdatedEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentOnboardingFinishedEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentPrivacySettingsUpdatedEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentProfileUpdatedEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentQuestionnaireUpdatedEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentTierUpdatedInAppEvent
import com.cleevio.fundedmind.application.module.user.student.finder.StudentDiscordFinderService
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.application.module.user.student.service.UpdateStudentPremiumDiscordRoleService
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class StudentCrmEventListener(
    private val appUserFinderService: AppUserFinderService,
    private val crmUserUpdatePort: CrmUserUpdatePort,
    private val studentFinderService: StudentFinderService,
    private val studentDiscordFinderService: StudentDiscordFinderService,
    private val updateStudentPremiumDiscordRoleService: UpdateStudentPremiumDiscordRoleService,
    private val userLocationFinderService: UserLocationFinderService,
) {

    @SentryTransaction(operation = "async.crm.onboarding-finished")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentOnboardingFinishedEvent(event: StudentOnboardingFinishedEvent) {
        val student = studentFinderService.getById(event.studentId)
        val appUser = appUserFinderService.getById(event.studentId)

        val realLocation = student.locationId?.let { locationId ->
            userLocationFinderService.getById(locationId).formattedAddress
        }

        crmUserUpdatePort.patchCrmUser(
            PatchCrmCustomerRequest(
                hubspotIdentifier = appUser.hubspotIdentifier,
                firstName = student.firstName,
                lastName = student.lastName,
                phone = student.phone,
                countryCode = student.country,
                biography = student.biography,
                questionnaire = student.questionnaire,
                studentTier = student.studentTier,
                realLocation = realLocation,
                networkingVisibility = student.networkingVisibility,
                levelVisibility = student.levelVisibility,
                gameLevel = student.gameLevel,
            ),
        )
    }

    @SentryTransaction(operation = "async.crm.student-profile-updated")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentProfileUpdatedEvent(event: StudentProfileUpdatedEvent) {
        val student = studentFinderService.getById(event.studentId)
        val appUser = appUserFinderService.getById(event.studentId)

        val realLocation = student.locationId?.let { locationId ->
            userLocationFinderService.getById(locationId).formattedAddress
        }

        crmUserUpdatePort.patchCrmUser(
            PatchCrmCustomerRequest(
                firstName = student.firstName,
                lastName = student.lastName,
                phone = student.phone,
                countryCode = student.country,
                biography = student.biography,
                questionnaire = student.questionnaire,
                studentTier = student.studentTier,
                hubspotIdentifier = appUser.hubspotIdentifier,
                realLocation = realLocation,
            ),
        )
    }

    @SentryTransaction(operation = "async.crm.student-tier-updated-in-app")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentTierUpdatedInAppEvent(event: StudentTierUpdatedInAppEvent) {
        val appUser = appUserFinderService.getById(event.studentId)
        val student = studentFinderService.getById(event.studentId)

        crmUserUpdatePort.patchCrmUser(
            PatchCrmCustomerRequest(
                hubspotIdentifier = appUser.hubspotIdentifier,
                traderReferral = appUser.traderReferral,
                studentTier = student.studentTier,
            ),
        )
    }

    @SentryTransaction(operation = "async.crm.student-discord-subscription-activated")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentDiscordSubscriptionActivatedEvent(event: StudentDiscordSubscriptionActivatedEvent) {
        val appUser = appUserFinderService.getById(event.studentId)
        val student = studentFinderService.getById(event.studentId)

        crmUserUpdatePort.updateCrmUserPremiumDiscordAccess(
            hubspotIdentifier = appUser.hubspotIdentifier,
            premiumDiscordAccessEnds = requireNotNull(student.discordSubscriptionExpiresAt),
        )

        updateStudentPremiumDiscordRoleService.grantStudentFundedMindProUserRole(student.id)
    }

    @SentryTransaction(operation = "async.crm.student-discord-subscription-activated-from-hubspot")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentDiscordSubscriptionActivatedFromHubspotEvent(
        event: StudentDiscordSubscriptionActivatedFromHubspotEvent,
    ) {
        updateStudentPremiumDiscordRoleService.grantStudentFundedMindProUserRole(event.studentId)
    }

    @SentryTransaction(operation = "async.crm.student-discord-subscription-updated")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentDiscordSubscriptionUpdatedEvent(event: StudentDiscordSubscriptionUpdatedEvent) {
        val appUser = appUserFinderService.getById(event.studentId)
        val student = studentFinderService.getById(event.studentId)

        crmUserUpdatePort.updateCrmUserPremiumDiscordAccess(
            hubspotIdentifier = appUser.hubspotIdentifier,
            premiumDiscordAccessEnds = requireNotNull(student.discordSubscriptionExpiresAt),
        )

        updateStudentPremiumDiscordRoleService.grantStudentFundedMindProUserRole(student.id)
    }

    @SentryTransaction(operation = "async.crm.student-discord-subscription-ended")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentDiscordSubscriptionEndedEvent(event: StudentDiscordSubscriptionEndedEvent) {
        updateStudentPremiumDiscordRoleService.demoteStudentToEntryRole(studentId = event.studentId)
    }

    @SentryTransaction(operation = "async.crm.student-discord-subscription-ended-from-hubspot")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentDiscordSubscriptionEndedFromHubspotEvent(event: StudentDiscordSubscriptionEndedFromHubspotEvent) {
        updateStudentPremiumDiscordRoleService.demoteStudentToEntryRole(studentId = event.studentId)
    }

    @SentryTransaction(operation = "async.crm.questionnaire-updated")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentQuestionnaireUpdatedEvent(event: StudentQuestionnaireUpdatedEvent) {
        val student = studentFinderService.getById(event.studentId)
        val appUser = appUserFinderService.getById(event.studentId)

        crmUserUpdatePort.patchCrmUser(
            PatchCrmCustomerRequest(
                hubspotIdentifier = appUser.hubspotIdentifier,
                questionnaire = student.questionnaire,
            ),
        )
    }

    @SentryTransaction(operation = "async.crm.student-connected-discord")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentConnectedDiscordEvent(event: StudentConnectedDiscordEvent) {
        val studentDiscord = studentDiscordFinderService.getById(event.studentDiscordId)

        val appUser = appUserFinderService.getById(studentDiscord.studentId)

        crmUserUpdatePort.patchCrmUser(
            PatchCrmCustomerRequest(
                hubspotIdentifier = appUser.hubspotIdentifier,
                discordUserName = studentDiscord.userName,
                discordGlobalName = studentDiscord.globalName,
            ),
        )
    }

    @SentryTransaction(operation = "async.crm.student-disconnected-discord")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentDisconnectedDiscordEvent(event: StudentDisconnectedDiscordEvent) {
        val studentDiscord = studentDiscordFinderService.getByIdDeleted(event.studentDiscordId)

        val appUser = appUserFinderService.getById(studentDiscord.studentId)

        crmUserUpdatePort.updateCrmUserDiscordNicknames(
            hubspotIdentifier = appUser.hubspotIdentifier,
            discordUserName = studentDiscord.userName,
            discordGlobalName = studentDiscord.globalName,
        )
    }

    @SentryTransaction(operation = "async.crm.student-privacy-settings-updated")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentPrivacySettingsUpdatedEvent(event: StudentPrivacySettingsUpdatedEvent) {
        val student = studentFinderService.getById(event.studentId)
        val appUser = appUserFinderService.getById(event.studentId)

        crmUserUpdatePort.patchCrmUser(
            PatchCrmCustomerRequest(
                hubspotIdentifier = appUser.hubspotIdentifier,
                networkingVisibility = student.networkingVisibility,
                levelVisibility = student.levelVisibility,
            ),
        )
    }

    @SentryTransaction(operation = "async.crm.student-location-updated")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentLocationUpdatedEvent(event: StudentLocationUpdatedEvent) {
        val student = studentFinderService.getById(event.studentId)
        val appUser = appUserFinderService.getById(event.studentId)

        val realLocation = student.locationId?.let { locationId ->
            userLocationFinderService.getById(locationId).formattedAddress
        }

        crmUserUpdatePort.updateCrmUserLocation(
            hubspotIdentifier = appUser.hubspotIdentifier,
            realLocation = realLocation,
        )
    }

    @SentryTransaction(operation = "async.crm.student-game-level-updated")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentGameLevelUpdatedEvent(event: StudentGameLevelUpdatedEvent) {
        val appUser = appUserFinderService.getById(event.studentId)

        crmUserUpdatePort.patchCrmUser(
            PatchCrmCustomerRequest(
                hubspotIdentifier = appUser.hubspotIdentifier,
                gameLevel = event.gameLevel,
            ),
        )
    }
}
