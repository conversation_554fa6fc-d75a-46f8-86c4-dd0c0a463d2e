package com.cleevio.fundedmind.application.module.payment

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.port.out.CouponPort
import com.cleevio.fundedmind.application.module.payment.command.GeneratePromotionCodesCommand
import org.springframework.stereotype.Service

@Service
class GeneratePromotionCodesCommandHandler(
    private val couponPort: CouponPort,
) : CommandHandler<GeneratePromotionCodesCommand.Result, GeneratePromotionCodesCommand> {

    override val command = GeneratePromotionCodesCommand::class

    override fun handle(command: GeneratePromotionCodesCommand): GeneratePromotionCodesCommand.Result = couponPort
        .generatePromotionCode(
            coupon = command.coupon,
            quantity = command.quantity,
            maxRedemptions = command.maxRedemptions,
        )
        .let { GeneratePromotionCodesCommand.Result(it) }
}
