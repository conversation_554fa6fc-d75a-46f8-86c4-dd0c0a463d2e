package com.cleevio.fundedmind.application.module.progress.service

import com.cleevio.fundedmind.application.module.coursemodule.finder.CourseModuleFinderService
import com.cleevio.fundedmind.application.module.progress.event.CourseFinishedEvent
import com.cleevio.fundedmind.application.module.progress.finder.CourseModuleProgressFinderService
import com.cleevio.fundedmind.application.module.progress.finder.CourseProgressFinderService
import com.cleevio.fundedmind.domain.progress.CreateCourseProgressService
import com.cleevio.fundedmind.infrastructure.config.logger
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

/**
 * @see FinishLessonService
 * @see FinishCourseModuleService
 */
@Service
class FinishCourseService(
    private val courseModuleProgressFinderService: CourseModuleProgressFinderService,
    private val courseModuleFinderService: CourseModuleFinderService,
    private val courseProgressFinderService: CourseProgressFinderService,
    private val createCourseProgressService: CreateCourseProgressService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {
    private val logger = logger()

    /**
     * Finish course if the given user finishes all of its non-coming-soon modules.
     * If course is already finished, nothing happens.
     */
    @Transactional
    fun finish(
        userId: UUID,
        courseId: UUID,
    ) {
        val modulesInCourse = courseModuleFinderService
            .findAllNonDeletedByCourseId(courseId)
            .filter { !it.comingSoon } // skip coming soon modules for the computation of the finished course

        val moduleIds = modulesInCourse.map { it.id }
        val userModuleProgresses = courseModuleProgressFinderService.findAllByUserIdAndCourseModuleIdIn(
            userId = userId,
            courseModuleIds = moduleIds,
        )

        if (userModuleProgresses.size != modulesInCourse.size) {
            // not all modules in course were started by user
            return
        }

        if (userModuleProgresses.any { !it.finished }) {
            // not all modules in course were finished by user
            return
        }

        // all modules in course were finished by user
        val courseProgress = courseProgressFinderService.findByCourseIdAndUserId(
            courseId = courseId,
            userId = userId,
        )

        if (courseProgress != null) {
            logger.info(
                "Course: '$courseId' was already finished for user: '$userId' " +
                    " at: ${courseProgress.finishedAt}. Silently continuing.",
            )
            return
        }

        val finishedAt = userModuleProgresses.maxOf { it.finishedAt }

        createCourseProgressService.create(
            courseId = courseId,
            userId = userId,
            finishedAt = finishedAt,
        )

        applicationEventPublisher.publishEvent(
            CourseFinishedEvent(
                userId = userId,
                courseId = courseId,
            ),
        )
    }
}
