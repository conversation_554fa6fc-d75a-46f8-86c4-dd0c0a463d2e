package com.cleevio.fundedmind.application.module.product.query

import com.cleevio.fundedmind.application.common.query.AutocompleteFilter
import com.cleevio.fundedmind.application.common.query.Query
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import jakarta.validation.constraints.Positive
import java.util.UUID

data class AutocompleteProductQuery(
    @field:Positive val limit: Int,
    @field:Valid val filter: Filter,
) : Query<AutocompleteProductQuery.Result> {

    data class Filter(
        val traderId: UUID,
        override val searchString: String?,
    ) : AutocompleteFilter

    @Schema(name = "AutocompleteProductResult")
    data class Result(
        val data: List<AutocompleteProduct>,
    )

    data class AutocompleteProduct(
        val productId: UUID,
        val name: String,
    )
}
