package com.cleevio.fundedmind.application.module.user.student.command

import com.cleevio.fundedmind.adapter.`in`.hubspot.request.HubspotChangeSource
import com.cleevio.fundedmind.application.common.command.Command
import com.cleevio.fundedmind.application.common.type.HubspotId
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import java.time.Instant

data class UpdateBanStudentFromHubspotCommand(
    val userHubspotIdentifier: HubspotId,
    val changeSource: HubspotChangeSource,
    val propertyValue: Boolean,
) : Command<Unit>

data class UpdateStudentTierFromHubspotCommand(
    val userHubspotIdentifier: HubspotId,
    val changeSource: HubspotChangeSource,
    val tier: StudentTier,
) : Command<Unit>

data class UpdatePremiumDiscordAccessFromHubspotCommand(
    val userHubspotIdentifier: HubspotId,
    val changeSource: HubspotChangeSource,
    val premiumDiscordAccessEnds: Instant?,
) : Command<Unit>
