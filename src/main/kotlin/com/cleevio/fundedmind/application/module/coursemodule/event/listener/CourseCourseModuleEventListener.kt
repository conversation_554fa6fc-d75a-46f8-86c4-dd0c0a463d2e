package com.cleevio.fundedmind.application.module.coursemodule.event.listener

import com.cleevio.fundedmind.application.module.course.event.CourseDeletedEvent
import com.cleevio.fundedmind.application.module.coursemodule.service.DeleteCourseModuleService
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class CourseCourseModuleEventListener(
    private val deleteCourseModuleService: DeleteCourseModuleService,
) {

    @EventListener
    fun handleCourseDeletedEvent(event: CourseDeletedEvent) {
        deleteCourseModuleService.deleteAllInCourse(event.courseId)
    }
}
