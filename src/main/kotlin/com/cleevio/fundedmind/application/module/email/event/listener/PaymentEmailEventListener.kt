package com.cleevio.fundedmind.application.module.email.event.listener

import com.cleevio.fundedmind.application.module.email.SendEmailService
import com.cleevio.fundedmind.application.module.user.student.event.StudentDiscordSubscriptionCancelIntentEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentDiscordSubscriptionEndedEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentDiscordSubscriptionPaymentFailedEvent
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class PaymentEmailEventListener(
    private val sendEmailService: SendEmailService,
) {

    @Async
    @EventListener // event is not fired in transaction
    fun handleStudentDiscordSubscriptionCancelledEvent(event: StudentDiscordSubscriptionCancelIntentEvent) {
        sendEmailService.sendEmailDiscordCancelled(
            studentId = event.studentId,
            endsAt = event.subscriptionEndsAt,
        )
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentDiscordSubscriptionEndedEvent(event: StudentDiscordSubscriptionEndedEvent) {
        sendEmailService.sendEmailDiscordEnded(studentId = event.studentId)
    }

    @Async
    @EventListener // event is not fired in transaction
    fun handleStudentDiscordSubscriptionPaymentFailedEvent(event: StudentDiscordSubscriptionPaymentFailedEvent) {
        sendEmailService.sendEmailDiscordPaymentFailed(
            studentId = event.studentId,
            endsAt = event.subscriptionEndsAt,
        )
    }
}
