package com.cleevio.fundedmind.application.module.highlight.command

import com.cleevio.fundedmind.application.common.command.AppButtonInput
import com.cleevio.fundedmind.application.common.command.Command
import com.cleevio.fundedmind.application.common.command.IdResult
import com.cleevio.fundedmind.application.common.validation.NullOrNotBlankAndLimited
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import jakarta.validation.Valid
import jakarta.validation.constraints.PositiveOrZero
import java.util.UUID

data class CreateNewHighlightCommand(
    @field:NullOrNotBlankAndLimited val title: String?,
    @field:NullOrNotBlankAndLimited val description: String?,
    val visibleToTiers: List<StudentTier>,
    val visibleToDiscordUsers: Boolean,
    @field:NullOrNotBlankAndLimited val linkUrl: String?,
    @field:Valid val button: AppButtonInput?,
) : Command<IdResult>

data class UpdateHighlightCommand(
    val highlightId: UUID,
    @field:NullOrNotBlankAndLimited val title: String?,
    @field:NullOrNotBlankAndLimited val description: String?,
    val visibleToTiers: List<StudentTier>,
    val visibleToDiscordUsers: Boolean,
    @field:NullOrNotBlankAndLimited val linkUrl: String?,
    @field:Valid val button: AppButtonInput?,
) : Command<Unit>

data class DeleteHighlightCommand(
    val highlightId: UUID,
) : Command<Unit>

data class PublishHighlightCommand(
    val highlightId: UUID,
) : Command<Unit>

data class HideHighlightCommand(
    val highlightId: UUID,
) : Command<Unit>

data class ReorderHighlightsCommand(
    val highlightOrderings: List<@Valid HighlightOrderingInput>,
) : Command<Unit> {
    data class HighlightOrderingInput(
        val highlightId: UUID,
        @field:PositiveOrZero val newListingOrder: Int,
    )
}
