package com.cleevio.fundedmind.application.module.referral.query

import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.common.query.Query
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import io.swagger.v3.oas.annotations.media.Schema
import java.util.UUID

data class GetReferralDetailQuery(
    val referralId: UUID,
) : Query<GetReferralDetailQuery.Result> {

    @Schema(name = "GetReferralDetailResult")
    data class Result(
        val referralId: UUID,
        val listingOrder: Int,
        val published: Boolean,
        val imageDesktop: ImageResult?,
        val imageMobile: ImageResult?,
        val title: String?,
        val description: String?,
        val visibleToTiers: List<StudentTier>,
        val visibleToDiscordUsers: Boolean,
        val linkUrl: String?,
        val rewardCouponCode: String?,
    )
}

data class ListReferralsQuery(
    val userId: UUID,
) : Query<ListReferralsQuery.Result> {

    @Schema(name = "ListReferralsResult")
    data class Result(
        val data: List<ReferralListing>,
    )

    @Schema(name = "ListReferralReferral")
    data class ReferralListing(
        val referralId: UUID,
        val listingOrder: Int,
        val published: Boolean,
        val blurred: Boolean,
        val imageDesktop: ImageResult?,
        val imageMobile: ImageResult?,
        val title: String?,
        val description: String?,
        val linkUrl: String?,
        val rewardCouponCode: String?,
        val visibleToTiers: List<StudentTier>,
        val visibleToDiscordUsers: Boolean,
    )
}
