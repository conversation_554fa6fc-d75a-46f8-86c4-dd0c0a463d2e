package com.cleevio.fundedmind.application.module.user.student.command

import com.cleevio.fundedmind.adapter.`in`.rest.request.QuestionnaireInput
import com.cleevio.fundedmind.application.common.command.Command
import com.cleevio.fundedmind.application.common.command.UserLocationInput
import com.cleevio.fundedmind.application.common.type.StripePriceId
import com.cleevio.fundedmind.application.common.type.StripeSessionId
import com.cleevio.fundedmind.application.common.type.StripeSubscriptionId
import com.cleevio.fundedmind.application.common.validation.NotBlankAndLimited
import com.cleevio.fundedmind.application.common.validation.NullOrNotBlankAndLimited
import com.cleevio.fundedmind.application.common.validation.PhoneNumber
import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.common.constant.LevelVisibility
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import java.util.UUID

data class StudentUpdatesProfileCommand(
    val studentId: UUID,
    @field:NotBlankAndLimited val firstName: String,
    @field:NotBlankAndLimited val lastName: String,
    @field:PhoneNumber val phone: String,
    @field:NullOrNotBlankAndLimited val biography: String?,
    val country: Country,
    @field:NotBlankAndLimited val firstNameVocative: String,
    @field:NotBlankAndLimited val lastNameVocative: String,
    @field:Valid val location: UserLocationInput?,
) : Command<Unit>

data class StudentUpdatesQuestionnaireCommand(
    val studentId: UUID,
    val questionnaire: QuestionnaireInput,
) : Command<Unit>

data class UpdateStudentBusinessPaymentDataCommand(
    val studentId: UUID,
    val checkoutSessionId: StripeSessionId,
    val businessPayment: Boolean,
    val ico: String?,
    val dic: String?,
) : Command<Unit>

data class InitiateCheckoutDiscordSubscriptionCommand(
    val studentId: UUID,
    val priceId: StripePriceId,
) : Command<InitiateCheckoutDiscordSubscriptionCommand.Result> {

    @Schema(name = "InitiateCheckoutDiscordSubscriptionResult")
    data class Result(
        val sessionId: StripeSessionId,
        val sessionSecret: String,
    )
}

data class StudentPatchesPrivacyCommand(
    val studentId: UUID,
    val networkingVisibility: NetworkingVisibility?,
    val levelVisibility: LevelVisibility?,
) : Command<Unit>

data class StudentPatchesLocationCommand(
    val studentId: UUID,
    val location: UserLocationInput?,
) : Command<Unit>

data class AdminActivatesStudentDiscordSubscriptionByIdCommand(
    val studentId: UUID,
    val subscriptionId: StripeSubscriptionId,
) : Command<Unit>
