package com.cleevio.fundedmind.application.module.email.event.listener

import com.cleevio.fundedmind.application.module.email.SendEmailService
import com.cleevio.fundedmind.application.module.user.appuser.event.VerificationCodeCreatedEvent
import com.cleevio.fundedmind.application.module.user.student.event.StudentOnboardingFinishedEvent
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class UserEmailEventListener(
    private val sendEmailService: SendEmailService,
    private val studentFinderService: StudentFinderService,
) {

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleOnboardingFinishedEvent(event: StudentOnboardingFinishedEvent) {
        val student = studentFinderService.getById(event.studentId)

        when (student.studentTier) {
            StudentTier.BASECAMP -> sendEmailService.sendEmailFinishOnboardingBasecamp(event.studentId)
            StudentTier.MASTERCLASS -> sendEmailService.sendEmailFinishOnboardingMasterclass(event.studentId)
            StudentTier.NO_TIER, StudentTier.EXCLUSIVE -> return // will not happen
        }
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleVerificationCodeCreatedEvent(event: VerificationCodeCreatedEvent) {
        sendEmailService.sendEmailOtpEmail(verificationCodeId = event.verificationCodeId)
    }
}
