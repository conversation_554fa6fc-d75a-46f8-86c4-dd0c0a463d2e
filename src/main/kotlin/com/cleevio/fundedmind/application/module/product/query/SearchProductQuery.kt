package com.cleevio.fundedmind.application.module.product.query

import com.cleevio.fundedmind.adapter.`in`.InfiniteScroll
import com.cleevio.fundedmind.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.common.command.MoneyResult
import com.cleevio.fundedmind.application.common.query.AutocompleteFilter
import com.cleevio.fundedmind.application.common.query.Query
import com.cleevio.fundedmind.application.common.type.StripeProductId
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import java.util.UUID

data class SearchProductQuery(
    @field:Valid val infiniteScroll: InfiniteScroll<UUID>,
    @field:Valid val filter: Filter,
) : Query<InfiniteScrollSlice<SearchProductQuery.Result, UUID>> {

    data class Filter(
        override val searchString: String?,
    ) : AutocompleteFilter

    @Schema(name = "SearchProductResult")
    data class Result(
        val productId: UUID,
        val name: String,
        val sessionCount: Int,
        val validityInDays: Int?,
        val traderBio: TraderBio,
        val description: String,
        val stripeIdentifier: StripeProductId,
        val price: MoneyResult,
        val saleable: Boolean,
    )

    @Schema(name = "SearchProductTraderBio")
    data class TraderBio(
        val traderId: UUID,
        val position: String,
        val firstName: String,
        val lastName: String,
        val profilePicture: ImageResult?,
        val badgeColor: BadgeColor,
        val active: Boolean,
    )
}
