package com.cleevio.fundedmind.application.module.user.student.service.trial

import com.cleevio.fundedmind.domain.user.student.Student
import org.springframework.stereotype.Service

@Service
class DiscordTrialCalculationService(
    private val discordTrialRules: List<DiscordTrialRule>,
) {

    /**
     * Calculates the trial period days based on enabled promotion rules.
     * Rules are evaluated in priority order (lower number = higher priority).
     * The first enabled rule that returns a non-null value wins.
     *
     * @param student The student to calculate trial days for
     * @return The number of trial period days, or null if no trial period should be applied
     */
    fun calculateTrialPeriodDays(student: Student): Long? = discordTrialRules
        .filter { it.isEnabled() }
        .sortedBy { it.getPriority() }
        .firstNotNullOfOrNull { it.calculateTrialDays(student) }
}
