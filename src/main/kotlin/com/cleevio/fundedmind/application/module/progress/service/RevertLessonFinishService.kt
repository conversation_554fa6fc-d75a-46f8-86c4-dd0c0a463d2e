package com.cleevio.fundedmind.application.module.progress.service

import com.cleevio.fundedmind.application.module.progress.event.LessonFinishRevertedEvent
import com.cleevio.fundedmind.application.module.progress.finder.LessonProgressFinderService
import com.cleevio.fundedmind.infrastructure.config.logger
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

/**
 * Service for reverting the finish status of a lesson.
 * @see FinishLessonService
 */
@Service
class RevertLessonFinishService(
    private val lessonProgressFinderService: LessonProgressFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {
    private val logger = logger()

    @Transactional
    fun revertFinish(
        userId: UUID,
        lessonId: UUID,
    ) {
        lessonProgressFinderService
            .getByLessonIdAndUserId(lessonId, userId)
            .apply {
                if (this.finished) {
                    revertFinish()

                    applicationEventPublisher.publishEvent(
                        LessonFinishRevertedEvent(
                            userId = userId,
                            lessonId = lessonId,
                        ),
                    )
                } else {
                    logger.info("Lesson: '$lessonId' is not finished for user: '$userId'. Silently continuing.")
                }
            }
    }
}
