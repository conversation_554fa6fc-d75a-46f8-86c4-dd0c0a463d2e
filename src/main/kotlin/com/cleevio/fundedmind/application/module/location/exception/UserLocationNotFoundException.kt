package com.cleevio.fundedmind.application.module.location.exception

import com.cleevio.fundedmind.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fundedmind.infrastructure.exception.FundedmindApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class UserLocationNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.USER_LOCATION_NOT_FOUND,
    message = message,
)
