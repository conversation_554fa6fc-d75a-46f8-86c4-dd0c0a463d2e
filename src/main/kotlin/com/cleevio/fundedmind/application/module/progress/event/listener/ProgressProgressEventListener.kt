package com.cleevio.fundedmind.application.module.progress.event.listener

import com.cleevio.fundedmind.application.module.coursemodule.finder.CourseModuleFinderService
import com.cleevio.fundedmind.application.module.lesson.finder.LessonFinderService
import com.cleevio.fundedmind.application.module.progress.event.CourseModuleFinishRevertedEvent
import com.cleevio.fundedmind.application.module.progress.event.CourseModuleFinishedEvent
import com.cleevio.fundedmind.application.module.progress.event.LessonFinishRevertedEvent
import com.cleevio.fundedmind.application.module.progress.event.LessonFinishedEvent
import com.cleevio.fundedmind.application.module.progress.service.FinishCourseModuleService
import com.cleevio.fundedmind.application.module.progress.service.FinishCourseService
import com.cleevio.fundedmind.application.module.progress.service.RevertCourseFinishService
import com.cleevio.fundedmind.application.module.progress.service.RevertCourseModuleFinishService
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class ProgressProgressEventListener(
    private val lessonFinderService: LessonFinderService,
    private val courseModuleFinderService: CourseModuleFinderService,
    private val finishCourseModuleService: FinishCourseModuleService,
    private val finishCourseService: FinishCourseService,
    private val revertCourseModuleFinishService: RevertCourseModuleFinishService,
    private val revertCourseFinishService: RevertCourseFinishService,
) {

    @EventListener
    fun handleLessonFinishedEvent(event: LessonFinishedEvent) {
        val courseModuleId = lessonFinderService.getById(event.lessonId).courseModuleId

        finishCourseModuleService.finish(
            userId = event.userId,
            courseModuleId = courseModuleId,
        )
    }

    @EventListener
    fun handleCourseModuleFinishedEvent(event: CourseModuleFinishedEvent) {
        val courseId = courseModuleFinderService.getById(event.courseModuleId).courseId

        finishCourseService.finish(
            userId = event.userId,
            courseId = courseId,
        )
    }

    @EventListener
    fun handleLessonFinishRevertedEvent(event: LessonFinishRevertedEvent) {
        val courseModuleId = lessonFinderService.getById(event.lessonId).courseModuleId

        revertCourseModuleFinishService.revertFinish(
            userId = event.userId,
            courseModuleId = courseModuleId,
        )
    }

    @EventListener
    fun handleCourseModuleFinishRevertedEvent(event: CourseModuleFinishRevertedEvent) {
        val courseId = courseModuleFinderService.getById(event.courseModuleId).courseId

        revertCourseFinishService.revertFinish(
            userId = event.userId,
            courseId = courseId,
        )
    }
}
