package com.cleevio.fundedmind.application.module.payment

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.payment.command.ProcessInvoiceFinalizedCommand
import com.cleevio.fundedmind.application.module.payment.port.out.InvoicingPort
import com.cleevio.fundedmind.application.module.payment.service.CreateInvoiceService
import com.cleevio.fundedmind.infrastructure.config.logger
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class ProcessInvoiceFinalizedCommandHandler(
    private val invoicingPort: InvoicingPort,
    private val createInvoiceService: CreateInvoiceService,
) : CommandHandler<Unit, ProcessInvoiceFinalizedCommand> {
    override val command = ProcessInvoiceFinalizedCommand::class

    private val logger = logger()

    /**
     * Process invoice paid event
     *
     * Lock is the same as in [ProcessInvoicePaidCommandHandler].
     * Locking is vital because Stripe events might not be delivered in order.
     *
     * Meaning 'invoice.finalized' might come after 'invoice.paid'.
     * In such case, in this handler we need first check if invoice does not exist.
     * If it exists then we assume that 'invoice.paid' was already processed, and it has created a Fakturoid invoice.
     * Therefore, processing of 'invoice.finalized' is unnecessary and will simply be skipped.
     */
    @SentrySpan
    @Transactional // ! locking is crucial - see above
    @Lock(module = Locks.Payment.MODULE, lockName = Locks.Payment.INVOICE_GENERATION)
    override fun handle(@LockFieldParameter("invoiceIdentifier") command: ProcessInvoiceFinalizedCommand) {
        logger.info("Processing invoice finalized for invoice: '${command.invoiceIdentifier}'...")

        invoicingPort
            .findInvoiceIdByCustomId(customId = command.invoiceIdentifier)
            ?.let { fakturoidInvoice ->
                logger.info(
                    "Finalized invoice: '${command.invoiceIdentifier}' already exists " +
                        "in Fakturoid (${fakturoidInvoice.id}), skipping processing.",
                )
                return
            }

        createInvoiceService.createInvoice(
            invoiceIdentifier = command.invoiceIdentifier,
            customerIdentifier = command.customerIdentifier,
            issuedOn = command.finalizedAt,
        )

        logger.info("Successfully processed invoice finalized for invoice: '${command.invoiceIdentifier}'.")
    }
}
