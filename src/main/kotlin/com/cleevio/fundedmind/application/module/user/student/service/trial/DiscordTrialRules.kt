package com.cleevio.fundedmind.application.module.user.student.service.trial

import com.cleevio.fundedmind.application.common.util.CZECH_ZONE_ID
import com.cleevio.fundedmind.application.module.summerbootcamp.port.out.SummerBootcampPort
import com.cleevio.fundedmind.domain.user.student.Student
import com.cleevio.fundedmind.infrastructure.properties.DiscordTrialRuleProperties
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit

interface DiscordTrialRule {
    fun getPriority(): Int
    fun isEnabled(): Boolean
    fun calculateTrialDays(student: Student): Long?

    fun calculateTrialDays(
        today: LocalDate,
        targetDate: LocalDate,
    ): Long? {
        val minimumTrialDays = 2L // Stripe's hard requirement
        val daysBetween = ChronoUnit.DAYS.between(today, targetDate)
        return when {
            daysBetween >= 3 -> daysBetween
            daysBetween in 1..2 -> minimumTrialDays
            else -> null
        }
    }
}

@Component
class SummerBootcampDiscordTrialRule(
    private val summerBootcampPort: SummerBootcampPort,
    private val discordTrialRuleProperties: DiscordTrialRuleProperties,
) : DiscordTrialRule {
    override fun getPriority(): Int = 1
    override fun isEnabled(): Boolean = discordTrialRuleProperties.summerBootcamp.enabled

    override fun calculateTrialDays(student: Student): Long? {
        if (!isEnabled()) return null

        with(summerBootcampPort.getTimeline()) {
            val studentRegistrationTime = student.tierUpgradedAt.atZone(CZECH_ZONE_ID)
            val isEligible = studentRegistrationTime in startDate..endDate

            if (isEligible) {
                return calculateTrialDays(
                    today = ZonedDateTime.now(CZECH_ZONE_ID).toLocalDate(),
                    targetDate = discordTrialEndDate.toLocalDate(),
                )
            }
        }
        return null
    }
}

@Component
class NewUserDiscordTrialRule(
    private val discordTrialRuleProperties: DiscordTrialRuleProperties,
) : DiscordTrialRule {
    override fun getPriority(): Int = 2
    override fun isEnabled(): Boolean = discordTrialRuleProperties.newUser.enabled

    override fun calculateTrialDays(student: Student): Long? {
        if (!isEnabled()) return null

        with(discordTrialRuleProperties.newUser) {
            val studentRegistrationTime = student.createdAt.atZone(CZECH_ZONE_ID)
            val newUserTrialStartDateTime = startDate.atStartOfDay(CZECH_ZONE_ID)

            if (studentRegistrationTime >= newUserTrialStartDateTime) {
                return trialDays
            }
        }
        return null
    }
}

@Component
class LaunchOfferDiscordTrialRule(
    private val discordTrialRuleProperties: DiscordTrialRuleProperties,
) : DiscordTrialRule {
    override fun getPriority(): Int = 3 // Lowest priority (fallback)
    override fun isEnabled(): Boolean = discordTrialRuleProperties.launchOffer.enabled

    override fun calculateTrialDays(student: Student): Long? {
        if (!isEnabled()) return null

        with(discordTrialRuleProperties.launchOffer) {
            return calculateTrialDays(
                today = ZonedDateTime.now(CZECH_ZONE_ID).toLocalDate(),
                targetDate = trialEndDate,
            )
        }
    }
}
