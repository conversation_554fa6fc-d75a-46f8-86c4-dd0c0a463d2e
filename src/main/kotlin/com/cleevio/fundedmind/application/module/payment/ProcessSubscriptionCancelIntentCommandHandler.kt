package com.cleevio.fundedmind.application.module.payment

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.port.out.GetSubscriptionDataPort
import com.cleevio.fundedmind.application.module.payment.command.ProcessSubscriptionCancelIntentCommand
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import com.cleevio.fundedmind.application.module.user.student.event.StudentDiscordSubscriptionCancelIntentEvent
import com.cleevio.fundedmind.infrastructure.config.logger
import com.cleevio.fundedmind.infrastructure.properties.StripeProperties
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service

@Service
class ProcessSubscriptionCancelIntentCommandHandler(
    private val appUserFinderService: AppUserFinderService,
    private val getSubscriptionDataPort: GetSubscriptionDataPort,
    private val applicationEventPublisher: ApplicationEventPublisher,
    private val stripeProperties: StripeProperties,
) : CommandHandler<Unit, ProcessSubscriptionCancelIntentCommand> {
    override val command = ProcessSubscriptionCancelIntentCommand::class

    private val logger = logger()

    @SentrySpan
    override fun handle(command: ProcessSubscriptionCancelIntentCommand) {
        logger.info("Processing cancel intent of subscription: '${command.subscriptionIdentifier}'...")

        when (command.productIdentifier) {
            stripeProperties.product.discord -> {
                logger.debug("Subscription: '${command.subscriptionIdentifier}' for DISCORD product.")
                processDiscordSubscriptionCancelIntent(command)
            }

            stripeProperties.product.masterclass -> {
                logger.debug("Subscription: '${command.subscriptionIdentifier}' for MASTERCLASS product.")
                // do nothing
            }

            stripeProperties.product.exclusive -> {
                logger.warn(
                    "Subscription: '${command.subscriptionIdentifier}' for EXCLUSIVE product. " +
                        "This case should not be viable as EXCLUSIVE shouldn't be paid via subscription model.",
                )
            }

            else -> {
                logger.warn(
                    "Subscription: '${command.subscriptionIdentifier}' is " +
                        "related to unknown product: '${command.productIdentifier}'.",
                )
                return
            }
        }

        logger.info("Cancel intent of Subscription: '${command.subscriptionIdentifier}' successfully processed.")
    }

    private fun processDiscordSubscriptionCancelIntent(command: ProcessSubscriptionCancelIntentCommand) {
        val studentUser = appUserFinderService.getByStripeIdentifier(command.customerIdentifier)
        val subscription = getSubscriptionDataPort.getSubscriptionBySubscriptionId(command.subscriptionIdentifier)

        applicationEventPublisher.publishEvent(
            StudentDiscordSubscriptionCancelIntentEvent(
                studentId = studentUser.id,
                subscriptionEndsAt = requireNotNull(subscription.subscriptionCancelAt),
            ),
        )
    }
}
