package com.cleevio.fundedmind.application.module.comment.event.listener

import com.cleevio.fundedmind.application.module.comment.event.CommentCreatedEvent
import com.cleevio.fundedmind.application.module.comment.service.NotifyThreadCommentParticipantsService
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class CommentCommentEventListener(
    private val notifyThreadCommentParticipantsService: NotifyThreadCommentParticipantsService,
) {

    @SentryTransaction(operation = "async.notification.thread-comment-created")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleThreadCommentCreatedEvent(event: CommentCreatedEvent) {
        notifyThreadCommentParticipantsService.notifyParticipants(event.commentId)
    }
}
