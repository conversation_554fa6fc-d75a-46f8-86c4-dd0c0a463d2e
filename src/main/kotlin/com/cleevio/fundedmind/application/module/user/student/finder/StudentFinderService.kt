package com.cleevio.fundedmind.application.module.user.student.finder

import com.cleevio.fundedmind.application.common.finder.BaseFinderService
import com.cleevio.fundedmind.application.module.user.student.exception.StudentNotFoundException
import com.cleevio.fundedmind.domain.user.student.Student
import com.cleevio.fundedmind.domain.user.student.StudentRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(readOnly = true)
class StudentFinderService(
    private val studentRepository: StudentRepository,
) : BaseFinderService<Student>(studentRepository) {

    override fun errorBlock(message: String) = throw StudentNotFoundException(message)

    override fun getEntityType() = Student::class

    fun findAllNonDeleted(): List<Student> = studentRepository.findAll().filter { !it.isDeleted }
}
