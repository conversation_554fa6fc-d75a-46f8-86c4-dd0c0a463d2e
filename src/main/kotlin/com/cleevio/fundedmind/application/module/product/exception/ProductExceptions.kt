package com.cleevio.fundedmind.application.module.product.exception

import com.cleevio.fundedmind.application.common.type.StripeProductId
import com.cleevio.fundedmind.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fundedmind.infrastructure.exception.FundedmindApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class ProductNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.PRODUCT_NOT_FOUND,
    message = message,
)

@ResponseStatus(HttpStatus.NOT_FOUND)
class ProductPriceNotFoundException(productIdentifier: StripeProductId) : FundedmindApiException(
    reason = ExtendedErrorReasonType.PRODUCT_PRICE_NOT_FOUND,
    message = "Price of Product: '$productIdentifier' not found.",
)

@ResponseStatus(HttpStatus.CONFLICT)
class ProductAlreadyExistsException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.PRODUCT_ALREADY_EXISTS,
    message = message,
    shouldBeLoggedToSentry = false,
)

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class ProductIsSaleableException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.PRODUCT_IS_SALEABLE,
    message = message,
    shouldBeLoggedToSentry = false,
)

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class ProductIsNotSaleableException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.PRODUCT_IS_NOT_SALEABLE,
    message = message,
    shouldBeLoggedToSentry = false,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class ProductNotRelatedToTraderException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.PRODUCT_NOT_RELATED_TO_TRADER,
    message = message,
)

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class ProductCannotBeUpdatedException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.PRODUCT_CANNOT_BE_UPDATED,
    message = message,
)

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class ProductRelatedToUnfinishedMentoringException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.PRODUCT_RELATED_TO_UNFINISHED_MENTORING,
    message = message,
)

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class ProductRelatedToMentoringMeetingInFutureException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.PRODUCT_RELATED_TO_MENTORING_MEETING_IN_FUTURE,
    message = message,
)
