package com.cleevio.fundedmind.application.module.user.appuser.event.listener

import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserUpdatePort
import com.cleevio.fundedmind.application.module.user.appuser.event.TraderReferralAppliedEvent
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class UserCrmEventListener(
    private val crmUserUpdatePort: CrmUserUpdatePort,
    private val appUserFinderService: AppUserFinderService,
) {

    @SentryTransaction(operation = "async.crm.trader-referral-applied")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleTraderReferralAppliedEvent(event: TraderReferralAppliedEvent) {
        val appUser = appUserFinderService.getById(event.userId)

        crmUserUpdatePort.updateCrmUserReferralApplied(
            hubspotIdentifier = appUser.hubspotIdentifier,
            referralApplied = appUser.referralApplied,
        )
    }
}
