package com.cleevio.fundedmind.application.module.payment

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.module.payment.command.ProcessSubscriptionUpdatedCommand
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import com.cleevio.fundedmind.application.module.user.student.service.UpdateStudentDiscordSubscriptionService
import com.cleevio.fundedmind.infrastructure.config.logger
import com.cleevio.fundedmind.infrastructure.properties.StripeProperties
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Service

@Service
class ProcessSubscriptionUpdatedCommandHandler(
    private val updateStudentDiscordSubscriptionService: UpdateStudentDiscordSubscriptionService,
    private val appUserFinderService: AppUserFinderService,
    private val stripeProperties: StripeProperties,
) : CommandHandler<Unit, ProcessSubscriptionUpdatedCommand> {
    override val command = ProcessSubscriptionUpdatedCommand::class

    private val logger = logger()

    @SentrySpan
    override fun handle(command: ProcessSubscriptionUpdatedCommand) {
        logger.info("Processing update of subscription: '${command.subscriptionIdentifier}'...")

        when (command.productIdentifier) {
            stripeProperties.product.discord -> {
                logger.debug("Subscription: '${command.subscriptionIdentifier}' for DISCORD product.")
                processDiscordSubscriptionUpdated(command)
            }

            stripeProperties.product.masterclass -> {
                logger.debug("Subscription: '${command.subscriptionIdentifier}' for MASTERCLASS product.")
                // do nothing - masterclass tier was changed during success of checkout
            }

            stripeProperties.product.exclusive -> {
                logger.warn(
                    "Subscription: '${command.subscriptionIdentifier}' for EXCLUSIVE product. " +
                        "This case should not be viable as EXCLUSIVE shouldn't be paid via subscription model.",
                )
            }

            else -> {
                logger.warn(
                    "Subscription: '${command.subscriptionIdentifier}' is " +
                        "related to unknown product: '${command.productIdentifier}'.",
                )
                return
            }
        }

        logger.info("Update of Subscription: '${command.subscriptionIdentifier}' successfully processed.")
    }

    private fun processDiscordSubscriptionUpdated(command: ProcessSubscriptionUpdatedCommand) {
        val studentUser = appUserFinderService.getByStripeIdentifier(command.customerIdentifier)

        updateStudentDiscordSubscriptionService.updateFromSubscription(
            userId = studentUser.id,
            subscriptionIdentifier = command.subscriptionIdentifier,
        )
    }
}
