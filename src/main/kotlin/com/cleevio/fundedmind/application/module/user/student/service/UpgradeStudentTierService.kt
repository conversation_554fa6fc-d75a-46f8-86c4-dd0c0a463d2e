package com.cleevio.fundedmind.application.module.user.student.service

import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.user.student.event.StudentTierUpdatedInAppEvent
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.infrastructure.config.logger
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockArgumentParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class UpgradeStudentTierService(
    private val studentFinderService: StudentFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {

    private val logger = logger()

    @Transactional
    @Lock(module = Locks.Student.MODULE, lockName = Locks.Student.UPDATE)
    fun upgradeStudentToMasterclassTier(@LockArgumentParameter studentId: UUID) {
        studentFinderService
            .getById(studentId)
            .apply {
                if (studentTier == StudentTier.MASTERCLASS) {
                    logger.warn("Student: '$studentId' is already '${StudentTier.MASTERCLASS}'. Skipping processing.")
                    return
                }
            }
            .apply { upgradeToMasterclass() }
            .also { applicationEventPublisher.publishEvent(StudentTierUpdatedInAppEvent(studentId)) }

        logger.info("Student: '$studentId' upgraded to '${StudentTier.MASTERCLASS}'.")
    }

    @Transactional
    @Lock(module = Locks.Student.MODULE, lockName = Locks.Student.UPDATE)
    fun upgradeStudentToExclusiveTier(@LockArgumentParameter userId: UUID) {
        val studentId = userId

        studentFinderService
            .getById(studentId)
            .apply {
                if (studentTier == StudentTier.EXCLUSIVE) {
                    logger.warn("Student: '$studentId' is already '${StudentTier.EXCLUSIVE}'. Skipping processing.")
                    return
                }

                upgradeToExclusive()
            }
            .also { applicationEventPublisher.publishEvent(StudentTierUpdatedInAppEvent(studentId)) }

        logger.info("Student: '$studentId' upgraded to '${StudentTier.EXCLUSIVE}'.")
    }
}
