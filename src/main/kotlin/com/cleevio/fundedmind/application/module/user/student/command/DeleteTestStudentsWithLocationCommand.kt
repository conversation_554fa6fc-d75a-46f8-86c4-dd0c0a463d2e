package com.cleevio.fundedmind.application.module.user.student.command

import com.cleevio.fundedmind.application.common.command.Command
import io.swagger.v3.oas.annotations.media.Schema

data class DeleteTestStudentsWithLocationCommand(
    val dummy: Boolean = true,
) : Command<DeleteTestStudentsWithLocationCommand.Result> {

    @Schema(name = "DeleteTestStudentsWithLocationResult")
    data class Result(
        val deletedStudentsCount: Int,
    )
}
