package com.cleevio.fundedmind.application.module.mentoring.finder

import com.cleevio.fundedmind.application.common.finder.BaseFinderService
import com.cleevio.fundedmind.domain.mentoring.Mentoring
import com.cleevio.fundedmind.domain.mentoring.MentoringRepository
import com.cleevio.fundedmind.domain.mentoring.exception.MentoringNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
@Transactional(readOnly = true)
class MentoringFinderService(
    private val mentoringRepository: MentoringRepository,
) : BaseFinderService<Mentoring>(mentoringRepository) {

    override fun errorBlock(message: String) = throw MentoringNotFoundException(message)

    override fun getEntityType() = Mentoring::class

    fun findAllByStudentIdAndTraderId(
        studentId: UUID,
        traderId: UUID,
    ): List<Mentoring> = mentoringRepository.findAllByStudentIdAndTraderId(studentId, traderId)

    fun existsByProductId(productId: UUID): Boolean = mentoringRepository.existsByProductId(productId)

    fun findAllByTraderId(traderId: UUID): List<Mentoring> = mentoringRepository.findAllByTraderId(traderId)

    fun findAllByProductId(productId: UUID): List<Mentoring> = mentoringRepository.findAllByProductId(productId)
}
