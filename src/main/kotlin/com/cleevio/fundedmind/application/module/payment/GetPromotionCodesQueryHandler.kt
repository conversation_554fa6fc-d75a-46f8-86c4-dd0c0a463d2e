package com.cleevio.fundedmind.application.module.payment

import com.cleevio.fundedmind.application.common.port.out.CouponPort
import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.module.payment.query.GetPromotionCodesQuery
import org.springframework.stereotype.Service

@Service
class GetPromotionCodesQueryHandler(
    private val couponPort: CouponPort,
) : QueryHandler<GetPromotionCodesQuery.Result, GetPromotionCodesQuery> {

    override val query = GetPromotionCodesQuery::class

    override fun handle(query: GetPromotionCodesQuery): GetPromotionCodesQuery.Result = couponPort
        .getPromotionCode(coupon = query.coupon)
        .let { GetPromotionCodesQuery.Result(it) }
}
