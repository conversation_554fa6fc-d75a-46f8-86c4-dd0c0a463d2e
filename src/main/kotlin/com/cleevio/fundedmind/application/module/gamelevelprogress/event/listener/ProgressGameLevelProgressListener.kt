package com.cleevio.fundedmind.application.module.gamelevelprogress.event.listener

import com.cleevio.fundedmind.application.module.course.finder.CourseFinderService
import com.cleevio.fundedmind.application.module.gamelevelprogress.service.PostStrategyCourseLevelTwoRewardService
import com.cleevio.fundedmind.application.module.progress.event.CourseFinishedEvent
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class ProgressGameLevelProgressListener(
    private val courseFinderService: CourseFinderService,
    private val postStrategyCourseLevelTwoRewardService: PostStrategyCourseLevelTwoRewardService,
) {

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleCourseFinishedEvent(event: CourseFinishedEvent) {
        val course = courseFinderService.getById(event.courseId)

        if (course.courseCategory == CourseCategory.STRATEGY) {
            postStrategyCourseLevelTwoRewardService.processAfterFinishingStrategyCourse(studentId = event.userId)
        }
    }
}
