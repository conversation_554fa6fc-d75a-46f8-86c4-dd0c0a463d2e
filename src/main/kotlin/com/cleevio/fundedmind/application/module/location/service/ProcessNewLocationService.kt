package com.cleevio.fundedmind.application.module.location.service

import com.cleevio.fundedmind.application.common.command.UserLocationInput
import com.cleevio.fundedmind.application.module.user.location.service.ObfuscateLocationService
import com.cleevio.fundedmind.domain.location.CreateUserLocationService
import com.cleevio.fundedmind.domain.location.DeleteUserLocationService
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class ProcessNewLocationService(
    private val obfuscateLocationService: ObfuscateLocationService,
    private val createUserLocationService: CreateUserLocationService,
    private val deleteUserLocationService: DeleteUserLocationService,
) {

    fun processNewLocation(
        currentLocationId: UUID?,
        newLocationInput: UserLocationInput?,
    ): UUID? {
        // delete previous location if exists
        currentLocationId?.let { deleteUserLocationService.deleteUserLocationById(it) }

        if (newLocationInput == null) return null

        with(newLocationInput) {
            // create a new location
            val obfuscatedLocation = obfuscateLocationService.obfusacte(
                latitude = geoLocation.latitude,
                longitude = geoLocation.longitude,
            )

            val newLocation = createUserLocationService.createUserLocation(
                street = street,
                city = city,
                postalCode = postalCode,
                state = state,
                latitude = geoLocation.latitude,
                longitude = geoLocation.longitude,
                obfuscatedLatitude = obfuscatedLocation.latitude,
                obfuscatedLongitude = obfuscatedLocation.longitude,
            )

            return newLocation.id
        }
    }
}
