package com.cleevio.fundedmind.application.module.product.finder

import com.cleevio.fundedmind.application.common.finder.BaseFinderService
import com.cleevio.fundedmind.application.common.type.StripeProductId
import com.cleevio.fundedmind.application.module.product.exception.ProductNotFoundException
import com.cleevio.fundedmind.domain.product.Product
import com.cleevio.fundedmind.domain.product.ProductRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
@Transactional(readOnly = true)
class ProductFinderService(
    private val productRepository: ProductRepository,
) : BaseFinderService<Product>(productRepository) {

    override fun errorBlock(message: String) = throw ProductNotFoundException(message)

    override fun getEntityType() = Product::class

    fun existsNonDeletedByStripeIdentifier(stripeIdentifier: StripeProductId): Boolean =
        productRepository.existsByStripeIdentifierAndDeletedAtIsNull(stripeIdentifier)

    fun existsSaleableNonDeletedByTraderId(traderId: UUID): Boolean =
        productRepository.existsByTraderIdAndDeletedAtIsNullAndSaleableIsTrue(traderId)

    fun getByStripeIdentifier(stripeIdentifier: StripeProductId): Product =
        productRepository.findByStripeIdentifierAndDeletedAtIsNull(stripeIdentifier)
            ?: throw ProductNotFoundException("Product with Stripe ID: '$stripeIdentifier' not found.")

    fun getByMentoringId(mentoringId: UUID): Product =
        productRepository.findByMentoringIdAndDeletedAtIsNull(mentoringId)
            ?: throw ProductNotFoundException("Product from mentoring: '$mentoringId' not found.")
}
