package com.cleevio.fundedmind.application.module.progress.finder

import com.cleevio.fundedmind.application.common.finder.BaseFinderService
import com.cleevio.fundedmind.domain.progress.CourseModuleProgress
import com.cleevio.fundedmind.domain.progress.CourseModuleProgressRepository
import com.cleevio.fundedmind.domain.progress.exception.CourseModuleProgressNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
@Transactional(readOnly = true)
class CourseModuleProgressFinderService(
    private val courseModuleProgressRepository: CourseModuleProgressRepository,
) : BaseFinderService<CourseModuleProgress>(courseModuleProgressRepository) {

    override fun errorBlock(message: String) = throw CourseModuleProgressNotFoundException(message)

    override fun getEntityType() = CourseModuleProgress::class

    fun findByCourseModuleIdAndUserId(
        courseModuleId: UUID,
        userId: UUID,
    ): CourseModuleProgress? = courseModuleProgressRepository.findByCourseModuleIdAndUserId(
        courseModuleId = courseModuleId,
        userId = userId,
    )

    fun findAllByUserIdAndCourseModuleIdIn(
        userId: UUID,
        courseModuleIds: List<UUID>,
    ): List<CourseModuleProgress> = courseModuleProgressRepository.findAllByUserIdAndCourseModuleIdIn(
        userId = userId,
        courseModuleIds = courseModuleIds,
    )
}
