package com.cleevio.fundedmind.application.module.user.student.finder

import com.cleevio.fundedmind.application.common.finder.BaseFinderService
import com.cleevio.fundedmind.application.module.user.student.exception.StudentDiscordNotFoundException
import com.cleevio.fundedmind.domain.user.student.StudentDiscord
import com.cleevio.fundedmind.domain.user.student.StudentDiscordRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
@Transactional(readOnly = true)
class StudentDiscordFinderService(
    private val studentDiscordRepository: StudentDiscordRepository,
) : BaseFinderService<StudentDiscord>(studentDiscordRepository) {

    override fun errorBlock(message: String) = throw StudentDiscordNotFoundException(message)

    override fun getEntityType() = StudentDiscord::class

    fun getByStudentIdNonDeleted(studentId: UUID): StudentDiscord =
        studentDiscordRepository.findByStudentIdAndDeletedAtIsNull(studentId)
            ?: errorBlock("StudentDiscord for student: '$studentId' not found.")

    fun findByStudentIdNonDeleted(studentId: UUID): StudentDiscord? =
        studentDiscordRepository.findByStudentIdAndDeletedAtIsNull(studentId)

    fun existsByStudentIdNonDeleted(studentId: UUID): Boolean =
        studentDiscordRepository.existsByStudentIdAndDeletedAtIsNull(studentId)

    fun findAllNonDeleted(): List<StudentDiscord> = studentDiscordRepository.findAllByDeletedAtIsNull()
}
