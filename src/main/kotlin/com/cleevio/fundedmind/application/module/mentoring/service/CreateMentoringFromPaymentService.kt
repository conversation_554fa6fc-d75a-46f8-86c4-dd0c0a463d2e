package com.cleevio.fundedmind.application.module.mentoring.service

import com.cleevio.fundedmind.application.common.type.StripeProductId
import com.cleevio.fundedmind.application.common.type.StripeSessionId
import com.cleevio.fundedmind.application.common.util.expirationInDays
import com.cleevio.fundedmind.application.module.mentoring.event.MentoringCreatedFromPaymentEvent
import com.cleevio.fundedmind.application.module.product.finder.ProductFinderService
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.domain.mentoring.CreateMentoringService
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.util.UUID

@Service
class CreateMentoringFromPaymentService(
    private val productFinderService: ProductFinderService,
    private val studentFinderService: StudentFinderService,
    private val createMentoringService: CreateMentoringService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {

    operator fun invoke(
        studentId: UUID,
        productId: StripeProductId,
        sessionId: StripeSessionId,
        today: LocalDate = LocalDate.now(),
    ) {
        val product = productFinderService.getByStripeIdentifier(productId)

        studentFinderService.checkExistsById(studentId)

        val mentoring = createMentoringService.createMentoring(
            sessionIdentifier = sessionId,
            studentId = studentId,
            productId = product.id,
            productName = product.name,
            productAltDescription = product.altDescription,
            expiresAt = product.validityInDays?.let { today.expirationInDays(it) },
            sessionCount = product.sessionsCount,
        )

        applicationEventPublisher.publishEvent(MentoringCreatedFromPaymentEvent(mentoringId = mentoring.id))
    }
}
