package com.cleevio.fundedmind.application.module.user.student.service

import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.LevelVisibility
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class GameLevelPrivacyMappingService(
    private val appUserFinderService: AppUserFinderService,
) {
    /**
     * Determine if logged-in user has access to a student's game level.
     *
     * @param meUserId The ID of the logged in user
     * @return The game level if the user can see student's game level, null otherwise
     */
    fun gameLevelOrNull(
        meUserId: UUID,
        studentGameLevel: GameLevel,
        studentGameLevelVisibility: LevelVisibility,
    ): GameLevel? {
        val meUser = appUserFinderService.getById(meUserId)

        val studentHasPublicGameLevel = studentGameLevelVisibility == LevelVisibility.ENABLED

        return when (meUser.role) {
            UserRole.ADMIN -> studentGameLevel
            UserRole.TRADER -> studentGameLevel
            UserRole.STUDENT -> if (studentHasPublicGameLevel) studentGameLevel else null
        }
    }
}
