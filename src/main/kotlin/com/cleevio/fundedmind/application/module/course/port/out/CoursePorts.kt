package com.cleevio.fundedmind.application.module.course.port.out

import com.cleevio.fundedmind.adapter.`in`.InfiniteScroll
import com.cleevio.fundedmind.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fundedmind.application.module.course.query.GetPublishedCourseQuery
import com.cleevio.fundedmind.application.module.course.query.ListAttachmentsOfPublishedCourseQuery
import com.cleevio.fundedmind.application.module.course.query.ListCoursesByCategoryQuery
import com.cleevio.fundedmind.application.module.course.query.ListModulesOfPublishedCourseQuery
import com.cleevio.fundedmind.application.module.course.query.ListPublishedCoursesByCategoryQuery
import com.cleevio.fundedmind.application.module.course.query.PublicGetCourseEducationalPillarsQuery
import com.cleevio.fundedmind.application.module.course.query.PublicGetPublicCourseQuery
import com.cleevio.fundedmind.application.module.course.query.PublicListPublicCoursesByCategoryQuery
import com.cleevio.fundedmind.application.module.course.query.SearchCoursesQuery
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import java.util.UUID

interface ListCoursesByCategoryPort {
    operator fun invoke(filter: ListCoursesByCategoryQuery.Filter): ListCoursesByCategoryQuery.Result
}

interface GetPublishedCoursePort {
    operator fun invoke(
        userId: UUID,
        courseId: UUID,
        isLockedForMeMapper: IsCourseLockedForMeMapper,
    ): GetPublishedCourseQuery.Result
}

interface PublicGetPublicCoursePort {
    operator fun invoke(courseId: UUID): PublicGetPublicCourseQuery.Result
}

interface PublicGetCourseEducationalPillarsPort {
    operator fun invoke(): PublicGetCourseEducationalPillarsQuery.Result
}

interface ListModulesOfPublishedCoursePort {
    operator fun invoke(
        userId: UUID,
        courseId: UUID,
        isLockedForMeMapper: IsCourseLockedForMeMapper,
    ): ListModulesOfPublishedCourseQuery.Result
}

interface ListAttachmentsOfPublishedCoursePort {
    operator fun invoke(courseId: UUID): ListAttachmentsOfPublishedCourseQuery.Result
}

interface ListPublishedCoursesByCategoryPort {
    operator fun invoke(
        userId: UUID,
        isLockedForMeMapper: IsCourseLockedForMeMapper,
        filter: ListPublishedCoursesByCategoryQuery.Filter,
    ): ListPublishedCoursesByCategoryQuery.Result
}
typealias IsCourseLockedForMeMapper = (allowedTiers: List<StudentTier>, allowedDiscordUsers: Boolean) -> Boolean

interface PublicListPublicCoursesByCategoryPort {
    operator fun invoke(courseCategory: CourseCategory): PublicListPublicCoursesByCategoryQuery.Result
}

interface SearchCoursesPort {
    operator fun invoke(
        filter: SearchCoursesQuery.Filter,
        infiniteScroll: InfiniteScroll<UUID>,
    ): InfiniteScrollSlice<SearchCoursesQuery.Result, UUID>
}
