package com.cleevio.fundedmind.application.module.payment

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.port.out.GetSubscriptionDataPort
import com.cleevio.fundedmind.application.module.payment.command.ProcessSubscriptionPaymentFailureCommand
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import com.cleevio.fundedmind.application.module.user.student.event.StudentDiscordSubscriptionPaymentFailedEvent
import com.cleevio.fundedmind.infrastructure.config.logger
import com.cleevio.fundedmind.infrastructure.properties.StripeProperties
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service

@Service
class ProcessSubscriptionPaymentFailureCommandHandler(
    private val appUserFinderService: AppUserFinderService,
    private val getSubscriptionDataPort: GetSubscriptionDataPort,
    private val applicationEventPublisher: ApplicationEventPublisher,
    private val stripeProperties: StripeProperties,
) : CommandHandler<Unit, ProcessSubscriptionPaymentFailureCommand> {
    override val command = ProcessSubscriptionPaymentFailureCommand::class

    private val logger = logger()

    @SentrySpan
    override fun handle(command: ProcessSubscriptionPaymentFailureCommand) {
        logger.info("Processing payment failure of subscription: '${command.subscriptionIdentifier}'...")

        when (command.productIdentifier) {
            stripeProperties.product.discord -> {
                logger.debug("Subscription: '${command.subscriptionIdentifier}' for DISCORD product.")
                processDiscordSubscriptionPaymentFailure(command)
            }

            stripeProperties.product.masterclass -> {
                logger.debug("Subscription: '${command.subscriptionIdentifier}' for MASTERCLASS product.")
                processMasterclassSubscriptionPaymentFailure(command)
            }

            stripeProperties.product.exclusive -> {
                logger.warn(
                    "Subscription: '${command.subscriptionIdentifier}' for EXCLUSIVE product. " +
                        "This case should not be viable as EXCLUSIVE shouldn't be paid via subscription model.",
                )
            }

            else -> {
                logger.warn(
                    "Subscription: '${command.subscriptionIdentifier}' is " +
                        "related to unknown product: '${command.productIdentifier}'.",
                )
                return
            }
        }

        logger.info("Payment failure of Subscription: '${command.subscriptionIdentifier}' successfully processed.")
    }

    private fun processMasterclassSubscriptionPaymentFailure(command: ProcessSubscriptionPaymentFailureCommand) {
        val studentUser = appUserFinderService.getByStripeIdentifier(command.customerIdentifier)

        logger.error(
            "Student: '${studentUser.id}' has failed to pay " +
                "for MASTERCLASS subscription: '${command.subscriptionIdentifier}'.",
        )
    }

    private fun processDiscordSubscriptionPaymentFailure(command: ProcessSubscriptionPaymentFailureCommand) {
        val studentUser = appUserFinderService.getByStripeIdentifier(command.customerIdentifier)
        val subscription = getSubscriptionDataPort.getSubscriptionBySubscriptionId(command.subscriptionIdentifier)

        applicationEventPublisher.publishEvent(
            StudentDiscordSubscriptionPaymentFailedEvent(
                studentId = studentUser.id,
                subscriptionEndsAt = subscription.endsAt,
            ),
        )
    }
}
