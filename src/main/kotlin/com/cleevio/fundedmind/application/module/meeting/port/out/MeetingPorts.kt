package com.cleevio.fundedmind.application.module.meeting.port.out

import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.module.meeting.query.GetMeetingDetailQuery
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import java.time.Instant
import java.util.UUID

interface UpcomingMeetingPort {
    fun findAllByFinishAfter(instant: Instant): List<UpcomingMeeting>

    data class UpcomingMeeting(
        val id: UUID,
        val name: String,
        val color: Color,
        val startAt: Instant,
        val finishAt: Instant,
        val invitedTiers: List<StudentTier>,
        val invitedDiscordUsers: <PERSON><PERSON><PERSON>,
        val coverPhoto: ImageResult?,
    )
}

interface GetMeetingDetailPort {
    operator fun invoke(meetingId: UUID): GetMeetingDetailQuery.Result
}
