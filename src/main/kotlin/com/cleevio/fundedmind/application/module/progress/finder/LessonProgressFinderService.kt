package com.cleevio.fundedmind.application.module.progress.finder

import com.cleevio.fundedmind.application.common.finder.BaseFinderService
import com.cleevio.fundedmind.domain.progress.LessonProgress
import com.cleevio.fundedmind.domain.progress.LessonProgressRepository
import com.cleevio.fundedmind.domain.progress.exception.LessonProgressNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
@Transactional(readOnly = true)
class LessonProgressFinderService(
    private val lessonProgressRepository: LessonProgressRepository,
) : BaseFinderService<LessonProgress>(lessonProgressRepository) {

    override fun errorBlock(message: String) = throw LessonProgressNotFoundException(message)

    override fun getEntityType() = LessonProgress::class

    fun findByLessonIdAndUserId(
        lessonId: UUID,
        userId: UUID,
    ): LessonProgress? = lessonProgressRepository.findByLessonIdAndUserId(lessonId = lessonId, userId = userId)

    fun getByLessonIdAndUserId(
        lessonId: UUID,
        userId: UUID,
    ): LessonProgress = findByLessonIdAndUserId(lessonId = lessonId, userId = userId)
        ?: errorBlock("LessonProgress for lesson: '$lessonId' and user: '$userId' not found.")

    fun findAllByUserIdAndLessonIdIn(
        userId: UUID,
        lessonIds: List<UUID>,
    ): List<LessonProgress> =
        lessonProgressRepository.findAllByUserIdAndLessonIdIn(userId = userId, lessonIds = lessonIds)

    fun countUserFinishedLessonsIn(
        userId: UUID,
        lessonIds: List<UUID>,
    ): Int = lessonProgressRepository.countUserFinishedLessonsIn(userId = userId, lessonIds = lessonIds)
}
