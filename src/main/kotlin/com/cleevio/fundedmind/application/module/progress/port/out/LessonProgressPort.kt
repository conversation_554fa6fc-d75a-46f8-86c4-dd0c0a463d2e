package com.cleevio.fundedmind.application.module.progress.port.out

import java.util.UUID

interface SaveUserLessonProgressPort {
    operator fun invoke(
        userId: UUID,
        lessonId: UUID,
        seconds: Int,
    )
}

interface GetLastWatchedLessonPort {
    operator fun invoke(userId: UUID): Result?

    data class Result(
        val lessonId: UUID,
        val courseModuleId: UUID,
        val courseId: UUID,
        val title: String,
        val thumbnailUrl: String,
        val thumbnailAnimationUrl: String,
        val durationInSeconds: Int,
        val watchedSeconds: Int,
    )
}

interface GetFirstBasecampLessonPort {
    operator fun invoke(userId: UUID): Result

    data class Result(
        val lessonId: UUID,
        val courseModuleId: UUID,
        val courseId: UUID,
        val title: String,
        val thumbnailUrl: String,
        val thumbnailAnimationUrl: String,
        val durationInSeconds: Int,
    )
}
