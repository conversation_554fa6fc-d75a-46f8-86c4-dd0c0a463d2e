package com.cleevio.fundedmind.application.module.email.event.listener

import com.cleevio.fundedmind.application.module.comment.event.ThreadCommentNotificationCreatedEvent
import com.cleevio.fundedmind.application.module.email.SendEmailService
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class CommentEmailEventListener(
    private val sendEmailService: SendEmailService,
) {

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleThreadCommentNotificationCreatedEvent(event: ThreadCommentNotificationCreatedEvent) {
        sendEmailService.sendEmailNewThreadComment(threadCommentNotificationId = event.threadCommentNotificationId)
    }
}
