package com.cleevio.fundedmind.application.module.user.student.service

import com.cleevio.fundedmind.application.module.user.student.event.StudentGameLevelUpdatedEvent
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class UpdateStudentGameLevelService(
    private val studentFinderService: StudentFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {

    @Transactional
    fun update(
        studentId: UUID,
        newLevel: GameLevel,
    ) {
        studentFinderService
            .getById(studentId)
            .apply { updateGameLevel(newLevel) }
            .also {
                applicationEventPublisher.publishEvent(
                    StudentGameLevelUpdatedEvent(
                        studentId = studentId,
                        gameLevel = newLevel,
                    ),
                )
            }
    }
}
