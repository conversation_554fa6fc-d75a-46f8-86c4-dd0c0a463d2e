package com.cleevio.fundedmind.application.module.user.appuser.event.listener

import com.cleevio.fundedmind.application.module.user.appuser.service.SyncUserWithStripeCustomersService
import com.cleevio.fundedmind.application.module.user.student.event.StudentOnboardingFinishedEvent
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class OnboardingUserEventListener(
    private val syncStudentUserWithStripeCustomersService: SyncUserWithStripeCustomersService,
) {
    @SentryTransaction(operation = "async.user.onboarding-finished")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleOnboardingFinishedEvent(event: StudentOnboardingFinishedEvent) {
        syncStudentUserWithStripeCustomersService.sync(event.studentId)
    }
}
