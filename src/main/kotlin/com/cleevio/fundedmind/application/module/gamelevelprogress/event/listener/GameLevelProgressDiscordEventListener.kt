package com.cleevio.fundedmind.application.module.gamelevelprogress.event.listener

import com.cleevio.fundedmind.application.common.port.out.DiscordUserRoleUpdatePort
import com.cleevio.fundedmind.application.module.gamelevelprogress.event.GameLevelProgressCreatedEvent
import com.cleevio.fundedmind.application.module.gamelevelprogress.event.GameLevelProgressDeletedEvent
import com.cleevio.fundedmind.application.module.gamelevelprogress.event.GameLevelProgressUpdatedEvent
import com.cleevio.fundedmind.application.module.user.student.finder.StudentDiscordFinderService
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class GameLevelProgressDiscordEventListener(
    private val studentFinderService: StudentFinderService,
    private val studentDiscordFinderService: StudentDiscordFinderService,
    private val discordUserRoleUpdatePort: DiscordUserRoleUpdatePort,
) {

    @SentryTransaction(operation = "async.discord.game-level-progress-created")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleGameLevelProgressCreatedEvent(event: GameLevelProgressCreatedEvent) {
        val studentDiscord = studentDiscordFinderService.findByStudentIdNonDeleted(event.studentId)
        if (studentDiscord == null) return // student has no discord connection

        val student = studentFinderService.getById(event.studentId)

        discordUserRoleUpdatePort.updateDiscordUserRolesForGameLevel(
            discordUserId = studentDiscord.discordId,
            gameLevel = student.gameLevel,
            hasDiscordPremiumAccess = student.discordSubscription,
        )
    }

    @SentryTransaction(operation = "async.discord.game-level-progress-updated")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleGameLevelProgressUpdatedEvent(event: GameLevelProgressUpdatedEvent) {
        val studentDiscord = studentDiscordFinderService.findByStudentIdNonDeleted(event.studentId)
        if (studentDiscord == null) return // student has no discord connection

        val student = studentFinderService.getById(event.studentId)

        discordUserRoleUpdatePort.updateDiscordUserRolesForGameLevel(
            discordUserId = studentDiscord.discordId,
            gameLevel = student.gameLevel,
            hasDiscordPremiumAccess = student.discordSubscription,
        )
    }

    @SentryTransaction(operation = "async.discord.game-level-progress-deleted")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleGameLevelProgressDeletedEvent(event: GameLevelProgressDeletedEvent) {
        val studentDiscord = studentDiscordFinderService.findByStudentIdNonDeleted(event.studentId)
        if (studentDiscord == null) return // student has no discord connection

        val student = studentFinderService.getById(event.studentId)

        discordUserRoleUpdatePort.updateDiscordUserRolesForGameLevel(
            discordUserId = studentDiscord.discordId,
            gameLevel = student.gameLevel,
            hasDiscordPremiumAccess = student.discordSubscription,
        )
    }
}
