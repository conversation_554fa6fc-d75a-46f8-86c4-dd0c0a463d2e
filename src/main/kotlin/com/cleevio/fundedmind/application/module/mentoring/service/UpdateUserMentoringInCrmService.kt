package com.cleevio.fundedmind.application.module.mentoring.service

import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserMentoringWithMentorUpdatePort
import com.cleevio.fundedmind.application.module.mentoring.finder.MentoringFinderService
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import com.cleevio.fundedmind.application.module.user.trader.finder.TraderFinderService
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class UpdateUserMentoringInCrmService(
    private val crmUserMentoringWithMentorUpdatePort: CrmUserMentoringWithMentorUpdatePort,
    private val mentoringFinderService: MentoringFinderService,
    private val appUserFinderService: AppUserFinderService,
    private val traderFinderService: TraderFinderService,
) {

    fun updateMentoringInCrm(mentoringId: UUID) {
        val mentoring = mentoringFinderService.getById(mentoringId)
        val trader = traderFinderService.getByProductId(mentoring.productId)
        val appUser = appUserFinderService.getById(mentoring.studentId)

        crmUserMentoringWithMentorUpdatePort.updateCrmUserMentoring(
            hubspotIdentifier = appUser.hubspotIdentifier,
            mentorPropertyName = trader.hubspotPropertyNameOrThrow,
            newSessionLeft = mentoring.sessionsLeft,
            allSessionsCount = mentoring.sessionCount,
        )
    }
}
