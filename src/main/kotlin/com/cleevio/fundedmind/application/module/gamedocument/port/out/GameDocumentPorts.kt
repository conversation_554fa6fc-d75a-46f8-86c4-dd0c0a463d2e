package com.cleevio.fundedmind.application.module.gamedocument.port.out

import com.cleevio.fundedmind.adapter.`in`.InfiniteScroll
import com.cleevio.fundedmind.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fundedmind.application.module.gamedocument.query.SearchGameDocumentsQuery
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import java.math.BigDecimal
import java.util.UUID

interface SearchGameDocumentsPort {
    operator fun invoke(
        filter: SearchGameDocumentsQuery.Filter,
        infiniteScroll: InfiniteScroll<UUID>,
    ): InfiniteScrollSlice<SearchGameDocumentsQuery.Result, UUID>
}

interface StudentGameProgressPort {
    fun getStudentGameProgress(studentId: UUID): StudentGameProgress

    data class StudentGameProgress(
        val firstName: String,
        val lastName: String,
        val gameLevel: GameLevel,
        val overallPayoutAmount: BigDecimal,
        val backtesting: GameDocumentInfo?,
        val hasStrategyModuleFinished: Bo<PERSON>an,
        val certificate: GameDocumentInfo?,
        val firstPayout: GameDocumentInfo?,
    )

    data class GameDocumentInfo(
        val gameDocumentId: UUID,
        val state: GameDocumentApprovalState,
    )
}
