package com.cleevio.fundedmind.application.module.mentoring.query

import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.common.command.MoneyResult
import com.cleevio.fundedmind.application.common.query.Query
import com.cleevio.fundedmind.application.common.type.CalendlyUserUri
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.TraderMentoring
import com.cleevio.fundedmind.domain.user.trader.constant.TraderTag
import io.swagger.v3.oas.annotations.media.Schema
import java.time.Instant
import java.util.UUID

data class StudentGetsTheirMentoringsQuery(
    val studentId: UUID,
) : Query<StudentGetsTheirMentoringsQuery.Result> {

    @Schema(name = "StudentGetsTheirMentoringsResult")
    data class Result(
        val data: List<StudentMentoringListing>,
    )

    @Schema(name = "StudentGetsTheirMentoringsMentoring")
    data class StudentMentoringListing(
        val mentoringId: UUID,
        val boughtAt: Instant,
        val productId: UUID,
        val productName: String,
        val productAltDescription: String,
        val expiresAt: Instant?,
        val sessionCount: Int,
        val usedSessions: Int,
        val trader: MentoringTraderBio,
    )

    @Schema(name = "StudentGetsTheirMentoringsMentoringTraderBio")
    data class MentoringTraderBio(
        val traderId: UUID,
        val position: String,
        val firstName: String,
        val lastName: String,
        val profilePicture: ImageResult?,
        val badgeColor: BadgeColor,
        val calendlyUrl: String?,
        val mentoring: TraderMentoring,
    )
}

data class TraderGetsTheirMentoringsQuery(
    val traderId: UUID,
) : Query<TraderGetsTheirMentoringsQuery.Result> {

    @Schema(name = "TraderGetsTheirMentoringsResult")
    data class Result(
        val data: List<TraderMentoringListing>,
    )

    @Schema(name = "TraderGetsTheirMentoringsMentoring")
    data class TraderMentoringListing(
        val mentoringId: UUID,
        val boughtAt: Instant,
        val productId: UUID,
        val productName: String,
        val productAltDescription: String,
        val expiresAt: Instant?,
        val sessionCount: Int,
        val usedSessions: Int,
        val student: MentoringStudentBio,
    )

    @Schema(name = "TraderGetsTheirMentoringsMentoringStudentBio")
    data class MentoringStudentBio(
        val studentId: UUID,
        val firstName: String,
        val lastName: String,
        val phone: String?,
        val email: String,
        val profilePicture: ImageResult?,
    )
}

data class GetTraderMentorInfoQuery(
    val traderId: UUID,
) : Query<GetTraderMentorInfoQuery.Result> {

    @Schema(name = "GetTraderMentorInfoResult")
    data class Result(
        val traderId: UUID,
        val firstName: String,
        val lastName: String,
        val profilePicture: ImageResult?,
        val badgeColor: BadgeColor,
        val biography: String?,
        val tags: List<TraderTag>,
        val socialLinkInstagram: String?,
        val socialLinkLinkedin: String?,
        val socialLinkFacebook: String?,
        val socialLinkTwitter: String?,
        val calendly: TraderCalendly?,
        val mentoring: TraderMentoring,
    )

    @Schema(name = "GetTraderMentorInfoTraderCalendly")
    data class TraderCalendly(
        val calendlyUrl: String?,
        val calendlyUserUri: CalendlyUserUri?,
    )
}

data class ExistsOngoingMentoringQuery(
    val studentId: UUID,
    val traderId: UUID,
) : Query<ExistsOngoingMentoringQuery.Result> {

    @Schema(name = "ExistsOngoingMentoringResult")
    data class Result(
        val mentoringId: UUID?,
    ) {
        @Schema(description = "Ongoing mentoring means that there is a bought mentoring with remaining uses.")
        val mentoringExists: Boolean = mentoringId != null
    }
}

data class GetAllTraderMentorsQuery(
    val userId: UUID,
) : Query<GetAllTraderMentorsQuery.Result> {

    @Schema(name = "GetAllTraderMentorsResult")
    data class Result(
        val data: List<TraderMentor>,
    )

    @Schema(name = "GetAllTraderMentorsTraderMentor")
    data class TraderMentor(
        val traderId: UUID,
        val firstName: String,
        val lastName: String,
        val tags: List<TraderTag>,
        val traderCourse: TraderCourse?,
        val introPicture: ImageResult?,
        val isMentoringLockedForMe: Boolean,
        val mentoring: TraderMentoring,
    )

    @Schema(name = "GetAllTraderMentorsTraderCourse")
    data class TraderCourse(
        val courseId: UUID,
        val courseName: String,
        val lessonCount: Int,
    )
}

@Deprecated("use StudentGetsMentoringOverviewForInquiryQuery")
data class StudentGetsCheckoutTraderMentorDataQuery(
    val studentId: UUID,
    val traderId: UUID,
) : Query<StudentGetsCheckoutTraderMentorDataQuery.Result> {

    @Schema(name = "StudentGetsCheckoutTraderMentorDataResult")
    data class Result(
        val traderId: UUID,
        val firstName: String,
        val lastName: String,
        val checkoutVideoUrl: String?,
        val biography: String?,
        val profilePicture: ImageResult?,
        val mentoringProducts: List<CheckoutMentoringProduct>,
    )

    @Schema(name = "StudentGetsCheckoutTraderMentorDataProduct")
    data class CheckoutMentoringProduct(
        val productId: UUID,
        val name: String,
        val description: String,
        val altDescription: String,
        val sessionsCount: Int,
        val validityInDays: Int?,
        val price: MoneyResult,
    )
}

data class StudentGetsMentoringOverviewForInquiryQuery(
    val studentId: UUID,
    val traderId: UUID,
) : Query<StudentGetsMentoringOverviewForInquiryQuery.Result> {

    @Schema(name = "StudentGetsMentoringOverviewForInquiryResult")
    data class Result(
        val traderId: UUID,
        val position: String,
        val firstName: String,
        val lastName: String,
        val badgeColor: BadgeColor,
        val tags: List<TraderTag>,
        val profilePicture: ImageResult?,
        val checkoutVideoUrl: String?,
        val mentoringDescription: String?,
        val biography: String?,
        val testimonials: List<MentoringTestimonials>,
        val mentoringKeypoints: List<MentoringKeypointResult>,
    )

    @Schema(name = "StudentGetsMentoringOverviewForInquiryTestimonial")
    data class MentoringTestimonials(
        val id: UUID,
        val name: String,
        val rating: Int,
        val description: String,
        val picture: ImageResult?,
    )

    @Schema(name = "StudentGetsMentoringOverviewForInquiryMentoringKeypoint")
    data class MentoringKeypointResult(
        val title: String,
        val text: String,
    )
}

data class TraderGetsTheirMentorDataQuery(
    val traderId: UUID,
) : Query<TraderGetsTheirMentorDataQuery.Result> {

    @Schema(name = "TraderGetsTheirMentorDataResult")
    data class Result(
        val traderId: UUID,
        val firstName: String,
        val lastName: String,
        val checkoutVideoUrl: String?,
        val biography: String?,
        val profilePicture: ImageResult?,
        val mentoringProducts: List<CheckoutMentoringProduct>,
    )

    @Schema(name = "TraderGetsTheirMentorDataProduct")
    data class CheckoutMentoringProduct(
        val productId: UUID,
        val name: String,
        val description: String,
        val altDescription: String,
        val sessionsCount: Int,
        val validityInDays: Int?,
        val price: MoneyResult,
    )
}
