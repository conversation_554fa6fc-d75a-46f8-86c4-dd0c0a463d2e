package com.cleevio.fundedmind.application.module.user.trader

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.command.IdResult
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.file.service.AppFileService
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import com.cleevio.fundedmind.application.module.user.student.exception.StudentOnboardingNotYetFinishedException
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.application.module.user.trader.command.CreateNewTraderCommand
import com.cleevio.fundedmind.application.module.user.trader.event.TraderCreatedEvent
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderAlreadyExistsException
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderCalendlyUserUriAlreadyTakenException
import com.cleevio.fundedmind.application.module.user.trader.finder.TraderFinderService
import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.domain.user.trader.CreateTraderService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class CreateNewTraderCommandHandler(
    private val traderFinderService: TraderFinderService,
    private val appUserFinderService: AppUserFinderService,
    private val createTraderService: CreateTraderService,
    private val studentFinderService: StudentFinderService,
    private val appFileService: AppFileService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<IdResult, CreateNewTraderCommand> {

    override val command = CreateNewTraderCommand::class

    @SentrySpan
    @Transactional
    @Lock(module = Locks.Trader.MODULE, lockName = Locks.Trader.CREATE)
    override fun handle(@LockFieldParameter("studentId") command: CreateNewTraderCommand): IdResult {
        val appUserId = command.studentId

        val traderId = appUserId
        if (traderFinderService.existsById(id = traderId)) {
            throw TraderAlreadyExistsException("Trader with id: '$traderId' already exists.")
        }

        command.calendly?.calendlyUserUri?.let { calendlyUserUri ->
            if (traderFinderService.existsByCalendlyUserUri(calendlyUserUri = calendlyUserUri)) {
                throw TraderCalendlyUserUriAlreadyTakenException(
                    "Trader already registered with Calendly user uri: '$calendlyUserUri'.",
                )
            }
        }

        // change role to TRADER
        appUserFinderService.getById(appUserId)
            .also { it.checkRole(expectedRole = UserRole.STUDENT) }
            .apply { changeRole(newRole = UserRole.TRADER) }

        val student = studentFinderService.findById(command.studentId)
            ?: throw StudentOnboardingNotYetFinishedException(
                "Chosen student '${command.studentId}' represents a user that has not yet finished onboarding.",
            )

        val currentMaxOrder = traderFinderService.findMaxListingOrder() ?: 0

        val createdTrader = createTraderService.createTrader(
            id = traderId,
            profilePictureFileId = student.profilePictureFileId
                ?.let { studentProfilePictureFileId ->
                    appFileService.copy(
                        sourceFileId = studentProfilePictureFileId,
                        destinationFileType = FileType.TRADER_PROFILE_PICTURE,
                    )
                }
                ?.id,
            listingOrder = currentMaxOrder + 1,
            position = command.position,
            firstName = command.firstName,
            lastName = command.lastName,
            biography = command.biography,
            tags = command.tags,
            badgeColor = command.badgeColor,
            commentControl = command.commentControl,
            socialLinkInstagram = command.socialLinkInstagram,
            socialLinkLinkedin = command.socialLinkLinkedin,
            socialLinkFacebook = command.socialLinkFacebook,
            socialLinkTwitter = command.socialLinkTwitter,
            calendlyUrl = command.calendly?.calendlyUrl,
            calendlyUserUri = command.calendly?.calendlyUserUri,
            checkoutVideoUrl = command.checkoutVideoUrl,
            phone = student.phone,
            country = student.country ?: Country.CZ,
            networkingVisibility = student.networkingVisibility,
            locationId = student.locationId,
            mentoringDescription = command.mentoringDescription,
            mentoringKeypoints = command.mentoringKeypoints.map { it.toData() },
        )

        applicationEventPublisher.publishEvent(
            TraderCreatedEvent(traderId = createdTrader.id),
        )

        return IdResult(createdTrader.id)
    }
}
