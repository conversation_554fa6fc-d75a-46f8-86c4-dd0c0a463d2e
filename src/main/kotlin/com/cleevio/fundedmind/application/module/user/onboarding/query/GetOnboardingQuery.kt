package com.cleevio.fundedmind.application.module.user.onboarding.query

import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.common.command.UserLocationResult
import com.cleevio.fundedmind.application.common.query.Query
import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.user.student.constant.OnboardingState
import io.swagger.v3.oas.annotations.media.Schema
import java.util.UUID

data class GetOnboardingQuery(
    val studentId: UUID,
) : Query<GetOnboardingQuery.Result> {

    @Schema(name = "GetOnboardingResult")
    data class Result(
        val userId: UUID,
        val state: OnboardingState,
        val studentTier: StudentTier,
        val firstName: String?,
        val lastName: String?,
        val phone: String?,
        val biography: String?,
        val country: Country?,
        val questionnaire: Map<String, Any>?,
        val profilePicture: ImageResult?,
        val location: UserLocationResult?,
        val masterclassInOnboarding: <PERSON>olean,
    )
}
