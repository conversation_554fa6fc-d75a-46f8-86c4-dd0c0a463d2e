package com.cleevio.fundedmind.application.module.product.query

import com.cleevio.fundedmind.application.common.command.MoneyResult
import com.cleevio.fundedmind.application.common.query.Query
import com.cleevio.fundedmind.application.common.type.StripeProductId
import io.swagger.v3.oas.annotations.media.Schema
import java.util.UUID

data class GetProductDetailQuery(
    val productId: UUID,
) : Query<GetProductDetailQuery.Result> {

    @Schema(name = "GetProductDetailResult")
    data class Result(
        val productId: UUID,
        val traderId: UUID,
        val price: MoneyResult,
        val name: String,
        val stripeIdentifier: StripeProductId,
        val description: String,
        val altDescription: String,
        val sessionsCount: Int,
        val validityInDays: Int?,
        val saleable: Boolean,
    ) {
        val infiniteValidity: Boolean = validityInDays == null
    }
}
