package com.cleevio.fundedmind.application.module.progress.service

import com.cleevio.fundedmind.application.module.progress.event.CourseModuleFinishRevertedEvent
import com.cleevio.fundedmind.application.module.progress.finder.CourseModuleProgressFinderService
import com.cleevio.fundedmind.domain.progress.service.DeleteCourseModuleProgressService
import com.cleevio.fundedmind.infrastructure.config.logger
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

/**
 * Service for reverting the finish status of a course module.
 * @see FinishCourseModuleService
 */
@Service
class RevertCourseModuleFinishService(
    private val deleteCourseModuleProgressService: DeleteCourseModuleProgressService,
    private val courseModuleProgressFinderService: CourseModuleProgressFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {
    private val logger = logger()

    @Transactional
    fun revertFinish(
        userId: UUID,
        courseModuleId: UUID,
    ) {
        val courseModuleProgress = courseModuleProgressFinderService
            .findByCourseModuleIdAndUserId(courseModuleId, userId)
            ?: return // If there's no progress, there's nothing to revert

        logger.debug("Deleting course module progress of module: '$courseModuleId'.")
        deleteCourseModuleProgressService.deleteById(courseModuleProgress.id)

        applicationEventPublisher.publishEvent(
            CourseModuleFinishRevertedEvent(
                userId = userId,
                courseModuleId = courseModuleId,
            ),
        )
    }
}
