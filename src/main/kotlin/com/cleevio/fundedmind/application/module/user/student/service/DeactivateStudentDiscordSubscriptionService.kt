package com.cleevio.fundedmind.application.module.user.student.service

import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.module.user.student.event.StudentDiscordSubscriptionEndedEvent
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.infrastructure.config.logger
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockArgumentParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class DeactivateStudentDiscordSubscriptionService(
    private val studentFinderService: StudentFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {

    private val logger = logger()

    @Transactional
    @Lock(module = Locks.Student.MODULE, lockName = Locks.Student.UPDATE)
    fun deactivate(@LockArgumentParameter userId: UUID) {
        studentFinderService
            .getById(userId)
            .apply {
                deactivateDiscordSubscription()

                logger.info("Student: '${this.id}' has discord subscription deactivated.")
            }
            .also { applicationEventPublisher.publishEvent(StudentDiscordSubscriptionEndedEvent(studentId = it.id)) }
    }
}
