package com.cleevio.fundedmind.application.module.mentoring.port.out

import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.common.command.MoneyResult
import com.cleevio.fundedmind.application.common.port.out.ProductIdentifiersToPriceMapMapper
import com.cleevio.fundedmind.application.module.mentoring.query.ExistsOngoingMentoringQuery
import com.cleevio.fundedmind.application.module.mentoring.query.GetAllTraderMentorsQuery
import com.cleevio.fundedmind.application.module.mentoring.query.StudentGetsTheirMentoringsQuery
import com.cleevio.fundedmind.application.module.mentoring.query.TraderGetsTheirMentoringsQuery
import java.util.UUID

interface StudentGetsTheirMentoringsPort {
    operator fun invoke(studentId: UUID): StudentGetsTheirMentoringsQuery.Result
}

interface TraderGetsTheirMentoringsPort {
    operator fun invoke(traderId: UUID): TraderGetsTheirMentoringsQuery.Result
}

interface ExistsOngoingMentoringPort {
    operator fun invoke(
        studentId: UUID,
        traderId: UUID,
    ): ExistsOngoingMentoringQuery.Result
}

interface GetAllTraderMentorsPort {
    operator fun invoke(isMentoringLockedForMeMapper: IsMentoringLockedForMeMapper): GetAllTraderMentorsQuery.Result
}

typealias IsMentoringLockedForMeMapper = () -> Boolean

interface GetMentorCheckoutDataPort {
    fun getByTraderId(
        traderId: UUID,
        pricesMapper: ProductIdentifiersToPriceMapMapper,
    ): MentorCheckoutData

    data class MentorCheckoutData(
        val traderId: UUID,
        val firstName: String,
        val lastName: String,
        val checkoutVideoUrl: String?,
        val biography: String?,
        val profilePicture: ImageResult?,
        val mentoringProducts: List<MentorCheckoutProduct>,
    )

    data class MentorCheckoutProduct(
        val productId: UUID,
        val name: String,
        val description: String,
        val altDescription: String,
        val sessionsCount: Int,
        val validityInDays: Int?,
        val price: MoneyResult,
    )
}
