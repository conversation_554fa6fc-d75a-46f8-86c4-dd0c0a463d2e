package com.cleevio.fundedmind.application.module.email.event.listener

import com.cleevio.fundedmind.application.module.email.SendEmailService
import com.cleevio.fundedmind.application.module.mentoring.event.MentoringCreatedFromPaymentEvent
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component

@Component
class MentoringEmailEventListener(
    private val sendEmailService: SendEmailService,
) {

    @Async
    @EventListener // event is not fired in transaction
    fun handleMentoringCreatedFromPaymentEvent(event: MentoringCreatedFromPaymentEvent) {
        sendEmailService.sendEmailStudentFinishesMentoringCheckout(mentoringId = event.mentoringId)
        sendEmailService.sendEmailTraderFinishesMentoringCheckout(mentoringId = event.mentoringId)
    }
}
