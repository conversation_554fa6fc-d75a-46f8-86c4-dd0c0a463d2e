package com.cleevio.fundedmind.application.module.user.student.query

import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.common.command.MoneyResult
import com.cleevio.fundedmind.application.common.command.UserLocationResult
import com.cleevio.fundedmind.application.common.query.Query
import com.cleevio.fundedmind.application.common.type.StripeCustomerId
import com.cleevio.fundedmind.application.common.type.StripePriceId
import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.LevelVisibility
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.common.constant.SubscriptionStatus
import io.swagger.v3.oas.annotations.media.Schema
import java.time.Instant
import java.util.UUID

data class GetMyStudentProfileQuery(
    val studentId: UUID,
) : Query<GetMyStudentProfileQuery.Result> {

    @Schema(name = "GetMyStudentProfileResult")
    data class Result(
        val studentId: UUID,
        val studentTier: StudentTier,
        val tierUpgradedAt: Instant,
        val firstName: String,
        val lastName: String,
        val biography: String?,
        val phone: String?,
        val country: Country?,
        val profilePicture: ImageResult?,
        val discordSubscription: Boolean,
        val discordSubscriptionExpiresAt: Instant?,
        val discordUsername: String?,
        val networkingVisibility: NetworkingVisibility,
        val levelVisibility: LevelVisibility,
        val gameLevel: GameLevel,
        val location: UserLocationResult?,
    )
}

data class GetDiscordSubscriptionPricesQuery(
    val studentId: UUID,
) : Query<GetDiscordSubscriptionPricesQuery.Result> {

    @Schema(name = "GetDiscordSubscriptionPricesResult")
    data class Result(
        val prices: List<DiscordSubscriptionPrice>,
    )

    @Schema(name = "GetDiscordSubscriptionPricesPrice")
    data class DiscordSubscriptionPrice(
        val priceId: StripePriceId,
        val price: MoneyResult,
        val recurringInterval: String,
        val recurringIntervalCount: Int,
    )
}

data class GetStudentDiscordSubscriptionsQuery(
    val studentId: UUID,
) : Query<GetStudentDiscordSubscriptionsQuery.Result> {

    @Schema(name = "GetStudentDiscordSubscriptionsResult")
    data class Result(
        val subscriptions: List<StudentDiscordSubscription>,
    )

    @Schema(name = "GetStudentDiscordSubscriptionsDiscordSubscription")
    data class StudentDiscordSubscription(
        val status: SubscriptionStatus,
        val subscriptionCancelAt: Instant?,
        val currentPeriodEnd: Instant,
        val recurringInterval: String,
        val recurringIntervalCount: Int,
        val customerId: StripeCustomerId,
    )
}
