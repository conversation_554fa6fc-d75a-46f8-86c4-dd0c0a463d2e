package com.cleevio.fundedmind.application.module.product.port.out

import com.cleevio.fundedmind.adapter.`in`.InfiniteScroll
import com.cleevio.fundedmind.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fundedmind.application.common.port.out.ProductIdentifiersToPriceMapMapper
import com.cleevio.fundedmind.application.module.product.query.SearchProductQuery
import java.util.UUID

interface SearchProductPort {
    operator fun invoke(
        filter: SearchProductQuery.Filter,
        infiniteScroll: InfiniteScroll<UUID>,
        pricesMapper: ProductIdentifiersToPriceMapMapper,
    ): InfiniteScrollSlice<SearchProductQuery.Result, UUID>
}
