package com.cleevio.fundedmind.application.module.user.student.service

import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.common.port.out.GetSubscriptionDataPort
import com.cleevio.fundedmind.application.common.type.StripeSubscriptionId
import com.cleevio.fundedmind.application.module.user.student.event.StudentDiscordSubscriptionUpdatedEvent
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.domain.common.constant.SubscriptionStatus
import com.cleevio.fundedmind.infrastructure.config.logger
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockArgumentParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class UpdateStudentDiscordSubscriptionService(
    private val studentFinderService: StudentFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
    private val getSubscriptionDataPort: GetSubscriptionDataPort,
) {

    private val logger = logger()

    @Transactional
    @Lock(module = Locks.Student.MODULE, lockName = Locks.Student.UPDATE)
    fun updateFromSubscription(
        @LockArgumentParameter userId: UUID,
        subscriptionIdentifier: StripeSubscriptionId,
    ) {
        val subscription = getSubscriptionDataPort.getSubscriptionBySubscriptionId(subscriptionIdentifier)

        if (subscription.status != SubscriptionStatus.ACTIVE) {
            logger.warn("Subscription: '$subscriptionIdentifier' is not active. Silently skipping update.")
            return
        }

        studentFinderService
            .getById(userId)
            .apply {
                activateDiscordSubscription(expiresAt = subscription.endsAt)

                logger.info("Student: '$id' updated Discord subscription with expiration: '${subscription.endsAt}'")
            }
            .also { applicationEventPublisher.publishEvent(StudentDiscordSubscriptionUpdatedEvent(studentId = it.id)) }
    }
}
