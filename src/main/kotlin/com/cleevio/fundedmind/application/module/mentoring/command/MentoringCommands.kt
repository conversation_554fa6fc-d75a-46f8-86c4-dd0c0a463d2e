package com.cleevio.fundedmind.application.module.mentoring.command

import com.cleevio.fundedmind.application.common.command.Command
import com.cleevio.fundedmind.application.common.command.IdResult
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate
import java.util.UUID

data class InitiateCheckoutMentoringCommand(
    val studentId: UUID,
    val traderId: UUID,
    val productId: UUID,
) : Command<InitiateCheckoutMentoringCommand.Result> {

    @Schema(name = "InitiateCheckoutMentoringResult")
    data class Result(
        val sessionId: String,
        val sessionSecret: String,
    )
}

data class TraderCreatesManualMentoringCommand(
    val traderId: UUID,
    val productId: UUID,
    val studentId: UUID,
    val today: LocalDate = LocalDate.now(),
) : Command<IdResult>

data class StudentInquiriesMentoringCommand(
    val studentId: UUID,
    val traderId: UUID,
    val inquiryAnswers: List<MentoringInquiryAnswerInput>,
) : Command<IdResult> {
    data class MentoringInquiryAnswerInput(
        val question: String,
        val answer: String,
    )
}
