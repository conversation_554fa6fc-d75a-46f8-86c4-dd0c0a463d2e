package com.cleevio.fundedmind.application.module.progress.finder

import com.cleevio.fundedmind.application.common.finder.BaseFinderService
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.progress.CourseProgress
import com.cleevio.fundedmind.domain.progress.CourseProgressRepository
import com.cleevio.fundedmind.domain.progress.exception.CourseProgressNotFoundException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
@Transactional(readOnly = true)
class CourseProgressFinderService(
    private val courseProgressRepository: CourseProgressRepository,
) : BaseFinderService<CourseProgress>(courseProgressRepository) {

    override fun errorBlock(message: String) = throw CourseProgressNotFoundException(message)

    override fun getEntityType() = CourseProgress::class

    fun findByCourseIdAndUserId(
        courseId: UUID,
        userId: UUID,
    ): CourseProgress? = courseProgressRepository.findByCourseIdAndUserId(
        courseId = courseId,
        userId = userId,
    )

    fun existsCompletedStrategyByStudentId(studentId: UUID): Boolean =
        courseProgressRepository.existsByStudentIdAndCourseCategory(
            studentId = studentId,
            courseCategory = CourseCategory.STRATEGY,
        )
}
