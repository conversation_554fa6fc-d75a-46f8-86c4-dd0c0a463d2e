package com.cleevio.fundedmind.application.module.mentoring.event.listener

import com.cleevio.fundedmind.application.module.mentoring.event.MentoringCreatedByTrader
import com.cleevio.fundedmind.application.module.mentoring.event.MentoringCreatedFromPaymentEvent
import com.cleevio.fundedmind.application.module.mentoring.service.UpdateUserMentoringInCrmService
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class MentoringMentoringEventListener(
    private val updateUserMentoringInCrmService: UpdateUserMentoringInCrmService,
) {

    @SentryTransaction(operation = "async.crm.mentoring-from-payment")
    @Async
    @EventListener // event is not fired in transaction
    fun handleMentoringCreatedFromPaymentEvent(event: MentoringCreatedFromPaymentEvent) {
        updateUserMentoringInCrmService.updateMentoringInCrm(event.mentoringId)
    }

    @SentryTransaction(operation = "async.crm.mentoring-from-trader")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleMentoringCreatedByTraderEvent(event: MentoringCreatedByTrader) {
        updateUserMentoringInCrmService.updateMentoringInCrm(event.mentoringId)
    }
}
