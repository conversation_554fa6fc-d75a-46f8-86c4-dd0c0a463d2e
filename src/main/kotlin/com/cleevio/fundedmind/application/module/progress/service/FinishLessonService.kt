package com.cleevio.fundedmind.application.module.progress.service

import com.cleevio.fundedmind.application.module.progress.event.LessonFinishedEvent
import com.cleevio.fundedmind.application.module.progress.finder.LessonProgressFinderService
import com.cleevio.fundedmind.infrastructure.config.logger
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

/**
 * @see FinishCourseModuleService
 * @see FinishCourseService
 */
@Service
class FinishLessonService(
    private val lessonProgressFinderService: LessonProgressFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {
    private val logger = logger()

    @Transactional
    fun finish(
        userId: UUID,
        lessonId: UUID,
    ) {
        lessonProgressFinderService
            .getByLessonIdAndUserId(lessonId, userId)
            .apply {
                if (!this.finished) {
                    finish()
                    applicationEventPublisher.publishEvent(LessonFinishedEvent(userId = userId, lessonId = lessonId))
                } else {
                    logger.info(
                        "Lesson: '$lessonId' already finished for user: '$userId' at: ${this.finishedAt}. " +
                            "Silently continuing.",
                    )
                }
            }
    }
}
