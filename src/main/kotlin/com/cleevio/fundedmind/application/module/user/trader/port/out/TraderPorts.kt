package com.cleevio.fundedmind.application.module.user.trader.port.out

import com.cleevio.fundedmind.application.common.port.out.ProductIdentifiersToPriceMapMapper
import com.cleevio.fundedmind.application.module.user.trader.query.GetMyTraderProfileQuery
import com.cleevio.fundedmind.application.module.user.trader.query.GetTraderDetailQuery
import com.cleevio.fundedmind.application.module.user.trader.query.ListAllTradersQuery
import java.util.UUID

interface GetMyTraderProfileQueryPort {
    operator fun invoke(traderId: UUID): GetMyTraderProfileQuery.Result
}

interface GetTraderDetailPort {
    operator fun invoke(
        traderId: UUID,
        pricesMapper: ProductIdentifiersToPriceMapMapper,
    ): GetTraderDetailQuery.Result
}

interface ListAllTradersPort {
    operator fun invoke(filter: ListAllTradersQuery.Filter): ListAllTradersQuery.Result
}
