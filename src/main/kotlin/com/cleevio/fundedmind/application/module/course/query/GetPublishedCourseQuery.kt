package com.cleevio.fundedmind.application.module.course.query

import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.common.query.Query
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.common.constant.CourseState
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.common.constant.TraderMentoring
import com.cleevio.fundedmind.domain.user.trader.constant.TraderTag
import io.swagger.v3.oas.annotations.media.Schema
import java.util.UUID

data class GetPublishedCourseQuery(
    val userId: UUID,
    val courseId: UUID,
) : Query<GetPublishedCourseQuery.Result> {

    @Schema(name = "GetPublishedCourseResult")
    data class Result(
        val courseId: UUID,
        val courseCategory: CourseCategory,
        val visibleToTiers: List<StudentTier>,
        val visibleToDiscordUsers: <PERSON><PERSON><PERSON>,
        val traderInfo: TraderInfo,
        val trailerUrl: String,
        val title: String,
        val moduleCount: Int,
        val totalDurationInSeconds: Long,
        val description: String,
        val color: Color,
        val introPictureDesktop: ImageResult?,
        val introPictureMobile: ImageResult?,
        val attachmentCount: Int,
        val isLockedForMe: Boolean,
        val unlockedData: PublishedCourseUnlockedData?,
    )

    @Schema(name = "GetPublishedCourseUnlockedData")
    data class PublishedCourseUnlockedData(
        val courseState: CourseState,
        val lessonToWatch: LessonToWatch?,
    )

    @Schema(name = "GetPublishedCourseLessonToWatch")
    data class LessonToWatch(
        val lessonId: UUID,
        val courseModuleId: UUID,
        val courseId: UUID,
    )

    @Schema(name = "GetPublishedCourseTraderInfo")
    data class TraderInfo(
        val traderId: UUID,
        val position: String,
        val firstName: String,
        val lastName: String,
        val profilePicture: ImageResult?,
        val badgeColor: BadgeColor,
        val biography: String?,
        val tags: List<TraderTag>,
        val socialLinkInstagram: String?,
        val socialLinkLinkedin: String?,
        val socialLinkFacebook: String?,
        val socialLinkTwitter: String?,
        val calendlyUrl: String?,
        val mentoring: TraderMentoring,
    )
}
