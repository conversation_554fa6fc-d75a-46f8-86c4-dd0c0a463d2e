package com.cleevio.fundedmind.application.module.email.event.listener

import com.cleevio.fundedmind.application.module.email.SendEmailService
import com.cleevio.fundedmind.application.module.user.student.event.StudentTierUpdatedFromHubspotEvent
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class StudentEmailEventListener(
    private val sendEmailService: SendEmailService,
    private val studentFinderService: StudentFinderService,
) {

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleStudentTierUpdatedFromHubspotEvent(event: StudentTierUpdatedFromHubspotEvent) {
        val student = studentFinderService.getById(event.studentId)

        when (student.studentTier) {
            StudentTier.NO_TIER -> return
            StudentTier.BASECAMP -> return
            StudentTier.MASTERCLASS -> return
            StudentTier.EXCLUSIVE -> sendEmailService.sendEmailExclusiveUpgraded(studentId = event.studentId)
        }
    }
}
