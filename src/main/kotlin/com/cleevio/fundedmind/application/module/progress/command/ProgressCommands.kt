package com.cleevio.fundedmind.application.module.progress.command

import com.cleevio.fundedmind.application.common.command.Command
import jakarta.validation.constraints.PositiveOrZero
import java.util.UUID

data class SaveUserLessonProgressCommand(
    val userId: UUID,
    val lessonId: UUID,
    @field:PositiveOrZero val seconds: Int,
) : Command<Unit>

data class UserFinishesLessonCommand(
    val lessonId: UUID,
    val userId: UUID,
) : Command<Unit>

data class UserRevertsFinishLessonCommand(
    val lessonId: UUID,
    val userId: UUID,
) : Command<Unit>
