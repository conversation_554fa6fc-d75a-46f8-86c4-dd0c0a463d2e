package com.cleevio.fundedmind.application.module.payment.port.out

import com.cleevio.fundedmind.application.common.type.FakturoidInvoiceId
import com.cleevio.fundedmind.application.common.type.StripeInvoiceId
import com.cleevio.fundedmind.domain.common.constant.TaxBehaviour
import java.math.BigDecimal
import java.time.LocalDate

/**
 * Port for creating invoices in external invoicing systems.
 */
interface InvoicingPort {

    fun findInvoiceIdByCustomId(customId: StripeInvoiceId): InvoiceResponse?

    /**
     * Creates an invoice in the external invoicing system.
     */
    fun createInvoice(request: InvoiceCreationRequest): FakturoidInvoiceId

    /**
     * Marks an invoice as paid in the external invoicing system.
     */
    fun payInvoice(
        invoiceId: FakturoidInvoiceId,
        paidAt: LocalDate,
    )

    data class InvoiceResponse(
        val id: FakturoidInvoiceId,
        val status: String,
    )

    /**
     * Data class containing all the information needed to create an invoice.
     */
    data class InvoiceCreationRequest(
        val name: String,
        val customId: String,
        val email: String,
        val street: String?,
        val city: String?,
        val zip: String?,
        val country: String?,
        val ico: String?,
        val dic: String?,
        val issuedOn: String,
        val products: List<Product>,
    ) {
        /**
         * Data class representing a product line item in an invoice.
         */
        data class Product(
            val name: String,
            val price: Double,
            val taxBehaviour: TaxBehaviour? = null,
            val vatRate: BigDecimal? = null,
        )
    }
}
