package com.cleevio.fundedmind.application.module.user.student.service

import com.cleevio.fundedmind.application.common.port.out.DiscordUserRoleUpdatePort
import com.cleevio.fundedmind.application.module.user.student.finder.StudentDiscordFinderService
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class UpdateStudentPremiumDiscordRoleService(
    private val discordUserRoleUpdatePort: DiscordUserRoleUpdatePort,
    private val studentDiscordFinderService: StudentDiscordFinderService,
    private val studentFinderService: StudentFinderService,
) {

    fun grantStudentFundedMindProUserRole(studentId: UUID) {
        val student = studentFinderService.getById(studentId)
        studentDiscordFinderService
            .findByStudentIdNonDeleted(studentId)
            ?.run {
                discordUserRoleUpdatePort.updateDiscordUserRolesForGameLevel(
                    discordUserId = discordId,
                    gameLevel = student.gameLevel,
                    hasDiscordPremiumAccess = student.discordSubscription,
                )
            }
    }

    fun demoteStudentToEntryRole(studentId: UUID) {
        studentDiscordFinderService
            .findByStudentIdNonDeleted(studentId)
            ?.run { discordUserRoleUpdatePort.demoteStudentToEntryRole(discordUserId = discordId) }
    }
}
