package com.cleevio.fundedmind.application.module.highlight.query

import com.cleevio.fundedmind.application.common.command.AppButtonResult
import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.common.query.Query
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import io.swagger.v3.oas.annotations.media.Schema
import java.util.UUID

data class GetHighlightDetailQuery(
    val highlightId: UUID,
) : Query<GetHighlightDetailQuery.Result> {

    @Schema(name = "GetHighlightDetailResult")
    data class Result(
        val highlightId: UUID,
        val published: Boolean,
        val imageDesktop: ImageResult?,
        val imageMobile: ImageResult?,
        val title: String?,
        val description: String?,
        val visibleToTiers: List<StudentTier>,
        val visibleToDiscordUsers: Boolean,
        val linkUrl: String?,
        val button: AppButtonResult?,
    )
}

data class ListHighlightsQuery(
    val userId: UUID,
) : Query<ListHighlightsQuery.Result> {

    @Schema(name = "ListHighlightsResult")
    data class Result(
        val data: List<HighlightListing>,
    )

    @Schema(name = "ListHighlightsHighlight")
    data class HighlightListing(
        val highlightId: UUID,
        val listingOrder: Int,
        val published: Boolean,
        val imageDesktop: ImageResult?,
        val imageMobile: ImageResult?,
        val title: String?,
        val description: String?,
        val linkUrl: String?,
        val button: AppButtonResult?,
        val visibleToTiers: List<StudentTier>,
        val visibleToDiscordUsers: Boolean,
    )
}
