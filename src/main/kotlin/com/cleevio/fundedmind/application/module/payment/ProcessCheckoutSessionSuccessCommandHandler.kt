package com.cleevio.fundedmind.application.module.payment

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.port.out.CheckoutSessionPort
import com.cleevio.fundedmind.application.common.type.StripeProductId
import com.cleevio.fundedmind.application.common.util.ifFalse
import com.cleevio.fundedmind.application.module.mentoring.service.CreateMentoringFromPaymentService
import com.cleevio.fundedmind.application.module.payment.command.ProcessCheckoutSessionSuccessCommand
import com.cleevio.fundedmind.application.module.payment.event.ExclusivePurchasedEvent
import com.cleevio.fundedmind.application.module.payment.event.MasterclassPurchasedEvent
import com.cleevio.fundedmind.application.module.payment.event.UserMissingStripeCustomerEvent
import com.cleevio.fundedmind.application.module.product.finder.ProductFinderService
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import com.cleevio.fundedmind.application.module.user.student.service.ActivateStudentDiscordSubscriptionService
import com.cleevio.fundedmind.application.module.user.student.service.UpgradeStudentTierService
import com.cleevio.fundedmind.domain.common.constant.PaymentTierState
import com.cleevio.fundedmind.domain.user.appuser.AppUser
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.infrastructure.config.logger
import com.cleevio.fundedmind.infrastructure.properties.StripeProperties
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service

@Service
class ProcessCheckoutSessionSuccessCommandHandler(
    private val stripeProperties: StripeProperties,
    private val appUserFinderService: AppUserFinderService,
    private val productFinderService: ProductFinderService,
    private val checkoutSessionPort: CheckoutSessionPort,
    private val upgradeStudentTierService: UpgradeStudentTierService,
    private val createMentoringFromPaymentService: CreateMentoringFromPaymentService,
    private val activateStudentDiscordSubscriptionService: ActivateStudentDiscordSubscriptionService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<Unit, ProcessCheckoutSessionSuccessCommand> {
    override val command = ProcessCheckoutSessionSuccessCommand::class

    private val logger = logger()

    @SentrySpan
    override fun handle(command: ProcessCheckoutSessionSuccessCommand) {
        logger.info("Processing successful checkout session: '${command.sessionIdentifier}'...")

        val sessionData = checkoutSessionPort.getCheckoutSessionCompletedData(command.sessionIdentifier)

        val studentUser: AppUser? = appUserFinderService.findByEmail(sessionData.customerEmail)
        if (studentUser == null) {
            logger.warn(
                "Checkout session: '${command.sessionIdentifier}' has email '${sessionData.customerEmail}' " +
                    "that doesn't belong to any user. Silently skipping processing.",
            )
            return
        }
        if (studentUser.role != UserRole.STUDENT) {
            logger.warn(
                "Checkout session: '${command.sessionIdentifier}' has email '${sessionData.customerEmail}' " +
                    "that belongs to a non-student user: '${studentUser.id}' [${studentUser.role}]. " +
                    "Silently skipping processing.",
            )
            return
        }

        if (sessionData.customerId == null) {
            logger.warn(
                "Checkout session: '${command.sessionIdentifier}' has no Customer. PL: ${sessionData.paymentLinkId}. " +
                    "Won't attempt to to sync user with Stripe customers.",
            )
        } else {
            studentUser.hasStripeIdentifier(sessionData.customerId).ifFalse {
                applicationEventPublisher.publishEvent(
                    UserMissingStripeCustomerEvent(userId = studentUser.id),
                )
            }
        }

        val paymentType: PaymentType = determinePaymentType(sessionData.soldProductId)

        when (paymentType) {
            PaymentType.UPGRADE_TO_MASTERCLASS -> {
                upgradeUserToMasterclass(studentUser)
                processCrmForMasterclass(sessionData, studentUser)
            }

            PaymentType.UPGRADE_TO_EXCLUSIVE -> {
                // Client wants to first sign NDA, then they will upgrade the student in Hubspot

                // -- do not upgrade the tier of the student --

                processCrmForExclusive(sessionData, studentUser)
            }

            PaymentType.ONE_ON_ONE_MENTORING -> {
                createMentoringFromPaymentService(
                    studentId = studentUser.id,
                    productId = sessionData.soldProductId,
                    sessionId = command.sessionIdentifier,
                )
            }

            PaymentType.DISCORD_SUBSCRIPTION -> {
                activateStudentDiscordSubscriptionService.activate(
                    userId = studentUser.id,
                    sessionId = command.sessionIdentifier,
                )
            }

            PaymentType.UNKNOWN -> {
                logger.warn(
                    "Checkout session: '${command.sessionIdentifier}' sold product: '${sessionData.soldProductId}' " +
                        "that is unknown. Silently skipping processing.",
                )
                return
            }
        }

        logger.info("Checkout session: '${command.sessionIdentifier}' successfully processed.")
    }

    private fun upgradeUserToMasterclass(studentUser: AppUser) {
        upgradeStudentTierService.upgradeStudentToMasterclassTier(studentId = studentUser.id)
    }

    private fun processCrmForExclusive(
        sessionData: CheckoutSessionPort.CheckoutSessionCompletedData,
        studentUser: AppUser,
    ) {
        if (sessionData.paymentLinkId == null) {
            // should not happen as we expect that exclusive is sold only via payment links
            logger.error(
                "EXCLUSIVE bough via session: '${sessionData.checkoutSessionId}' " +
                    "was not paid via payment link.",
            )
        }

        applicationEventPublisher.publishEvent(
            ExclusivePurchasedEvent(
                userId = studentUser.id,
                paymentTierState = PaymentTierState.EXCLUSIVE_SALES_WON,
            ),
        )
    }

    private fun processCrmForMasterclass(
        sessionData: CheckoutSessionPort.CheckoutSessionCompletedData,
        studentUser: AppUser,
    ) {
        // masterclass bought via web app checkout
        if (sessionData.paymentLinkId == null) {
            applicationEventPublisher.publishEvent(
                MasterclassPurchasedEvent(
                    userId = studentUser.id,
                    paymentTierState = PaymentTierState.MASTERCLASS_ORGANIC_PAY,
                ),
            )

            return
        }

        // masterclass bought via payment links
        if (sessionData.subscriptionId == null) {
            applicationEventPublisher.publishEvent(
                MasterclassPurchasedEvent(
                    userId = studentUser.id,
                    paymentTierState = PaymentTierState.MASTERCLASS_SALES_WON,
                ),
            )
        } else {
            applicationEventPublisher.publishEvent(
                MasterclassPurchasedEvent(
                    userId = studentUser.id,
                    paymentTierState = PaymentTierState.MASTERCLASS_SALES_PARTIAL,
                ),
            )
        }
    }

    private fun determinePaymentType(productId: StripeProductId): PaymentType = when {
        productId == stripeProperties.product.masterclass -> PaymentType.UPGRADE_TO_MASTERCLASS
        productId == stripeProperties.product.exclusive -> PaymentType.UPGRADE_TO_EXCLUSIVE
        productId == stripeProperties.product.discord -> PaymentType.DISCORD_SUBSCRIPTION
        productFinderService.existsNonDeletedByStripeIdentifier(productId) -> PaymentType.ONE_ON_ONE_MENTORING
        else -> PaymentType.UNKNOWN
    }
}
