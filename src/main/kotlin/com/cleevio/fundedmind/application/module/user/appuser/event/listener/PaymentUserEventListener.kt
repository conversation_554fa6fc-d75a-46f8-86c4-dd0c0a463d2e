package com.cleevio.fundedmind.application.module.user.appuser.event.listener

import com.cleevio.fundedmind.application.module.payment.event.MasterclassPurchasedEvent
import com.cleevio.fundedmind.application.module.payment.event.UserMissingStripeCustomerEvent
import com.cleevio.fundedmind.application.module.user.appuser.service.ApplyUserReferralService
import com.cleevio.fundedmind.application.module.user.appuser.service.SyncUserWithStripeCustomersService
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component

@Component
class PaymentUserEventListener(
    private val applyUserReferralService: ApplyUserReferralService,
    private val syncStudentUserWithStripeCustomersService: SyncUserWithStripeCustomersService,
) {
    @SentryTransaction(operation = "async.user.missing-stripe-customer")
    @Async
    @EventListener
    fun handleStudentUserMissingStripeCustomerEvent(event: UserMissingStripeCustomerEvent) {
        syncStudentUserWithStripeCustomersService.sync(event.userId)
    }

    @SentryTransaction(operation = "async.user.masterclass-purchased")
    @Async
    @EventListener
    fun handleMasterclassPurchasedEvent(event: MasterclassPurchasedEvent) {
        applyUserReferralService.apply(event.userId)
    }
}
