package com.cleevio.fundedmind.application.module.progress.service

import com.cleevio.fundedmind.application.module.lesson.finder.LessonFinderService
import com.cleevio.fundedmind.application.module.progress.event.CourseModuleFinishedEvent
import com.cleevio.fundedmind.application.module.progress.finder.CourseModuleProgressFinderService
import com.cleevio.fundedmind.application.module.progress.finder.LessonProgressFinderService
import com.cleevio.fundedmind.domain.progress.CreateCourseModuleProgressService
import com.cleevio.fundedmind.infrastructure.config.logger
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

/**
 * @see FinishLessonService
 * @see FinishCourseService
 */
@Service
class FinishCourseModuleService(
    private val lessonFinderService: LessonFinderService,
    private val lessonProgressFinderService: LessonProgressFinderService,
    private val courseModuleProgressFinderService: CourseModuleProgressFinderService,
    private val createCourseModuleProgressService: CreateCourseModuleProgressService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {
    private val logger = logger()

    /**
     * Finish module if all of its lessons are finished by the given user.
     * If module is already finished, nothing happens.
     */
    @Transactional
    fun finish(
        userId: UUID,
        courseModuleId: UUID,
    ) {
        val lessonsInCourseModule = lessonFinderService.findAllNonDeletedByCourseModuleId(courseModuleId)

        val lessonIds = lessonsInCourseModule.map { it.id }
        val userLessonProgresses = lessonProgressFinderService.findAllByUserIdAndLessonIdIn(
            userId = userId,
            lessonIds = lessonIds,
        )

        if (userLessonProgresses.size != lessonsInCourseModule.size) {
            // not all lessons in module were started by user
            return
        }

        if (userLessonProgresses.any { !it.finished }) {
            // not all lessons in module were finished by user
            return
        }

        // all lessons in module were finished by user
        val courseModuleProgress = courseModuleProgressFinderService.findByCourseModuleIdAndUserId(
            courseModuleId = courseModuleId,
            userId = userId,
        )

        if (courseModuleProgress != null) {
            logger.info(
                "Course module: '$courseModuleId' was already finished for user: '$userId' " +
                    " at: ${courseModuleProgress.finishedAt}. Silently continuing.",
            )
            return
        }

        val finishedAt = userLessonProgresses.mapNotNull { it.finishedAt }.max()

        createCourseModuleProgressService.create(
            courseModuleId = courseModuleId,
            userId = userId,
            finishedAt = finishedAt,
        )

        applicationEventPublisher.publishEvent(
            CourseModuleFinishedEvent(
                userId = userId,
                courseModuleId = courseModuleId,
            ),
        )
    }
}
