package com.cleevio.fundedmind.application.module.summerbootcamp.port.out

import java.time.ZonedDateTime

interface SummerBootcampPort {
    fun getRemainingSpots(): Int

    fun updateRemainingSpots(newValue: Int)

    fun getTimeline(): DiscordSummerBootcamp2025

    data class DiscordSummerBootcamp2025(
        val startDate: ZonedDateTime,
        val endDate: ZonedDateTime,
        val discordTrialEndDate: ZonedDateTime,
    )
}
