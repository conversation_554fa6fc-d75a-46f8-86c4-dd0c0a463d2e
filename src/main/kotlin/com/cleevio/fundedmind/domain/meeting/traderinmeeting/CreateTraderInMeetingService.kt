package com.cleevio.fundedmind.domain.meeting.traderinmeeting

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class CreateTraderInMeetingService(
    private val traderInMeetingRepository: TraderInMeetingRepository,
) {

    @Transactional
    fun create(
        meetingId: UUID,
        traderIds: List<UUID>,
    ): Unit = traderIds.forEachIndexed { idx, traderId ->
        traderInMeetingRepository.save(
            TraderInMeeting.addTraderToMeeting(
                displayOrder = idx + 1,
                traderId = traderId,
                meetingId = meetingId,
            ),
        )
    }
}
