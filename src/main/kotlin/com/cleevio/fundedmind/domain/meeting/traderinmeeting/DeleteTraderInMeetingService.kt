package com.cleevio.fundedmind.domain.meeting.traderinmeeting

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class DeleteTraderInMeetingService(private val traderInMeetingRepository: TraderInMeetingRepository) {

    @Transactional
    fun deleteAllTradersFromMeeting(meetingId: UUID) {
        traderInMeetingRepository.deleteAllByMeetingId(meetingId)
    }
}
