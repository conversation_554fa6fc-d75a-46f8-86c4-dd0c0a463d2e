package com.cleevio.fundedmind.domain.meeting.traderinmeeting

import com.cleevio.fundedmind.application.common.util.UUIDv7
import com.cleevio.fundedmind.domain.DomainEntity
import jakarta.persistence.Entity
import jakarta.persistence.Table
import org.hibernate.annotations.DynamicUpdate
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.UUID

/**
 * Trader can be either added or removed from a meeting by admin.
 */
@Table(name = "trader_in_meeting")
@Entity
@DynamicUpdate
class TraderInMeeting private constructor(
    id: UUID,
    val displayOrder: Int,
    val traderId: UUID,
    val meetingId: UUID,
) : DomainEntity(id) {

    companion object {
        fun addTraderToMeeting(
            id: UUID = UUIDv7.randomUUID(),
            displayOrder: Int,
            traderId: UUID,
            meetingId: UUID,
        ) = TraderInMeeting(
            id = id,
            displayOrder = displayOrder,
            traderId = traderId,
            meetingId = meetingId,
        ).also {
            require(displayOrder >= 1)
        }
    }
}

@Repository
interface TraderInMeetingRepository : JpaRepository<TraderInMeeting, UUID> {
    fun deleteAllByMeetingId(meetingId: UUID)
    fun findAllByMeetingIdOrderByDisplayOrderAsc(meetingId: UUID): List<TraderInMeeting>
}
