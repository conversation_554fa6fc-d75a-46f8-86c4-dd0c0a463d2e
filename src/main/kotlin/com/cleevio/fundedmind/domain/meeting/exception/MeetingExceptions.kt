package com.cleevio.fundedmind.domain.meeting.exception

import com.cleevio.fundedmind.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fundedmind.infrastructure.exception.FundedmindApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class MeetingNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.MEETING_NOT_FOUND,
    message = message,
)

@ResponseStatus(HttpStatus.NOT_FOUND)
class TraderInMeetingNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.TRADER_IN_MEETING_NOT_FOUND,
    message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class MeetingTimeIsIncorrectException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.MEETING_INCORRECT_TIME,
    message = message,
)
