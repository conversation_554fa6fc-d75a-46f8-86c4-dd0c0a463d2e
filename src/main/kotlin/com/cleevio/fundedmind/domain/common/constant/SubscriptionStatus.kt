package com.cleevio.fundedmind.domain.common.constant

enum class SubscriptionStatus {
    ACTIVE,
    CANCELED,
    PAST_DUE,
    ;

    companion object {
        /**
         * Maps a Stripe subscription status to our SubscriptionState enum.
         * https://docs.stripe.com/api/subscriptions/object#subscription_object-status
         *
         * Stripe allows for multiple states but our current settings of subscriptions should allow only 3 states:
         * 'active, canceled, past_due'.
         */
        fun fromStripeStatus(stripeStatus: String): SubscriptionStatus = when (stripeStatus.lowercase()) {
            "active", "trialing" -> ACTIVE
            "canceled", "incomplete", "incomplete_expired", "unpaid", "paused" -> CANCELED
            "past_due" -> PAST_DUE
            else -> CANCELED
        }
    }
}
