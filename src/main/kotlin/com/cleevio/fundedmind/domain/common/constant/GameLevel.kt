package com.cleevio.fundedmind.domain.common.constant

import com.cleevio.fundedmind.application.common.util.isNegative
import java.math.BigDecimal

enum class GameLevel(val order: Int, val levelPayoutGoal: Int?) {
    ZERO(order = 0, levelPayoutGoal = null),
    ONE(order = 1, levelPayoutGoal = null),
    TWO(order = 2, levelPayoutGoal = null),
    THREE(order = 3, levelPayoutGoal = null),
    FOUR(order = 4, levelPayoutGoal = 0), // first payout can be of any value therefore zero amount
    FIVE(order = 5, levelPayoutGoal = 5_000),
    SIX(order = 6, levelPayoutGoal = 10_000),
    SEVEN(order = 7, levelPayoutGoal = 20_000),
    EIGHT(order = 8, levelPayoutGoal = 50_000),
    NINE(order = 9, levelPayoutGoal = 100_000),
    TEN(order = 10, levelPayoutGoal = null),
    ;

    val nextLevelOrThrow: GameLevel
        get() = entries.find { it.order == this.order + 1 } ?: error("$this has no next level.")

    val levelPayoutGoalOrThrow: Int
        get() = levelPayoutGoal ?: error("$this has no payout goal.")

    companion object {

        /**
         * Returns a list of levels that a student needs to gain to reach the reachedLevel.
         *
         * E.g.
         * - if currentLevel = 4; reachedLevel = 6; it returns [5, 6]
         * - if currentLevel = 4; reachedLevel = 4; it returns empty list
         * - if currentLevel = 6; reachedLevel = 4; it returns empty list
         *
         * @param currentLevel The current level of the student
         * @param reachedLevel The level that the student has reached
         * @return The list of levels that the student needs to gain, sorted by order
         */
        fun levelsToGain(
            currentLevel: GameLevel,
            reachedLevel: GameLevel,
        ): List<GameLevel> = entries
            .filter { it.order in currentLevel.order + 1..reachedLevel.order }
            .sortedBy { it.order }

        /**
         * Returns a list of levels that a student needs to lose to be demoted to the demotedToLevel.
         *
         * E.g.
         * - if currentLevel = 6; demotedToLevel = 4; it returns [5, 6]
         * - if currentLevel = 4; demotedToLevel = 4; it returns empty list
         * - if currentLevel = 4; demotedToLevel = 6; it returns empty list
         *
         * @param currentLevel The current level of the student
         * @param demotedToLevel The level that the student has been demoted to
         * @return The list of levels that the student needs to lose, sorted by order
         */
        fun levelsToLose(
            currentLevel: GameLevel,
            demotedToLevel: GameLevel,
        ): List<GameLevel> = entries
            .filter { it.order in demotedToLevel.order + 1..currentLevel.order }
            .sortedBy { it.order }

        /**
         * Determines what level would be reached with a given payout amount.
         *
         * @return The level that would be reached with the given amount
         */
        fun determineLevelByPayoutAmount(totalPayoutAmount: BigDecimal): GameLevel {
            check(!totalPayoutAmount.isNegative()) { "Payout amount cannot be negative: $totalPayoutAmount" }

            val levelsWithGoals = GameLevel
                .entries
                .filter { it.levelPayoutGoal != null }
                .sortedByDescending { it.order } // Sort in descending order to find highest level first

            // Find the highest level where the total payout meets or exceeds the level's goal
            for (level in levelsWithGoals) {
                val goalAmount = level.levelPayoutGoal!!.toBigDecimal()
                if (totalPayoutAmount >= goalAmount) {
                    return level
                }
            }

            // If no level is found (payout is less than the minimum goal), return level FOUR
            return FOUR
        }
    }
}
