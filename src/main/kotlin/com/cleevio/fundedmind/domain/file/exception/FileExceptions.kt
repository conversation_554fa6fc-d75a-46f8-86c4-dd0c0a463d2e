package com.cleevio.fundedmind.domain.file.exception

import com.cleevio.fundedmind.domain.file.constant.CompressionStatus
import com.cleevio.fundedmind.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fundedmind.infrastructure.exception.FundedmindApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus
import java.util.UUID

@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
class AppFileNotDeletedException(id: UUID, compression: CompressionStatus) : FundedmindApiException(
    reason = ExtendedErrorReasonType.FILE_NOT_DELETED,
    message = "Unable to delete file: '$id' [$compression].",
)

@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
class AppFileNotCopiedException(id: UUID, compression: CompressionStatus) : FundedmindApiException(
    reason = ExtendedErrorReasonType.FILE_NOT_COPIED,
    message = "Unable to copy file: '$id' [$compression].",
)

@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
class AppFileNotSavedException : FundedmindApiException(
    reason = ExtendedErrorReasonType.FILE_NOT_SAVED,
    message = "Unable to save file.",
)

@ResponseStatus(HttpStatus.NOT_FOUND)
class AppFileNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.FILE_NOT_FOUND,
    message = message,
)

@ResponseStatus(HttpStatus.BAD_GATEWAY)
class AppFileDownloadFailedException : FundedmindApiException(
    reason = ExtendedErrorReasonType.DOWNLOAD_FAILED_EXCEPTION,
    message = "Download of user external media failed",
)
