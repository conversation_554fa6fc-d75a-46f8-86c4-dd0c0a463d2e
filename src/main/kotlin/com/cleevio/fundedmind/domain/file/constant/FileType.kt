package com.cleevio.fundedmind.domain.file.constant

import java.util.UUID

enum class FileType(val isPrivate: Boolean) {
    STUDENT_PROFILE_PICTURE(false),
    TRADER_PROFILE_PICTURE(false),
    TRADER_INTRO_PICTURE(false),
    TRADER_MENTORING_PHOTO(false),
    MEETING_COVER_PHOTO(false),
    HIGHLIGHT_DESKTOP_PHOTO(false),
    HIGHLIGHT_MOBILE_PHOTO(false),
    COURSE_DESKTOP_INTRO_PHOTO(false),
    COURSE_MOBILE_INTRO_PHOTO(false),
    COURSE_MODULE_DESKTOP_THUMBNAIL(false),
    COURSE_MODULE_MOBILE_THUMBNAIL(false),
    COURSE_MODULE_REWARD_PICTURE(false),
    REFERRAL_DESKTOP_PHOTO(false),
    REFERRAL_MOBILE_PHOTO(false),
    GAME_LEVEL_REWARD_PHOTO(false),
    MENTORING_TESTIMONIAL_PICTURE(false),

    LESSON_ATTACHMENT(true),
    GAME_DOCUMENT(true),
    ;

    fun isProfilePicture(): Boolean = this in setOf(
        STUDENT_PROFILE_PICTURE,
        TRADER_PROFILE_PICTURE,
    )

    fun isDocument(): Boolean = this in setOf(
        LESSON_ATTACHMENT,
        GAME_DOCUMENT,
    )

    fun isImage(): Boolean = isDocument().not()
}

/**
 * Annotation serves as a reminder that an entity field is a file and should be related to a file type and Replacer.
 * Presence of this annotation is verified by a test.
 */
@Target(AnnotationTarget.PROPERTY)
@Retention(AnnotationRetention.RUNTIME)
annotation class File(val type: FileType)

enum class CompressionStatus(val nameSuffix: String) {
    ORIGINAL("orig"),
    COMPRESSED("comp"),
    ;

    fun composeFileName(
        fileId: UUID,
        fileExtension: String,
    ): String = "$fileId-$nameSuffix.$fileExtension"
}
