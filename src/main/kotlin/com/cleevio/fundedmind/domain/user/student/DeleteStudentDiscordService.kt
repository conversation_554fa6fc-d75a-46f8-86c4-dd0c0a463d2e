package com.cleevio.fundedmind.domain.user.student

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class DeleteStudentDiscordService(
    private val studentDiscordRepository: StudentDiscordRepository,
) {

    @Transactional
    fun deleteById(studentDiscordId: UUID) {
        studentDiscordRepository.deleteById(studentDiscordId)
    }

    @Transactional
    fun deleteAllByStudentIdIn(studentIds: List<UUID>) {
        studentDiscordRepository.deleteAllByStudentIdIn(studentIds)
    }
}
