package com.cleevio.fundedmind.domain.user.appuser

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class DeleteAppUserService(
    private val appUserRepository: AppUserRepository,
) {

    @Transactional
    fun deleteAppUsersByIds(appUserIds: List<UUID>) {
        appUserRepository.deleteAllById(appUserIds)
    }
}
