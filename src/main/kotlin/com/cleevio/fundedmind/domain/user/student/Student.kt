package com.cleevio.fundedmind.domain.user.student

import com.cleevio.fundedmind.application.common.util.ifFalse
import com.cleevio.fundedmind.application.common.util.ifTrue
import com.cleevio.fundedmind.application.module.user.student.exception.CannotUpgradeStudentTierException
import com.cleevio.fundedmind.application.module.user.student.exception.StudentAlreadyOnboardedException
import com.cleevio.fundedmind.application.module.user.student.exception.StudentHasInsufficientGameLevelException
import com.cleevio.fundedmind.application.module.user.student.exception.StudentHasNoAccessToMentoringException
import com.cleevio.fundedmind.application.module.user.student.exception.StudentHasNoActiveDiscordException
import com.cleevio.fundedmind.application.module.user.student.exception.StudentHasWrongStudentTierException
import com.cleevio.fundedmind.application.module.user.student.exception.StudentOnboardingNotYetFinishedException
import com.cleevio.fundedmind.application.module.user.student.exception.StudentQuestionnaireMissingException
import com.cleevio.fundedmind.domain.SoftDeletableEntity
import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.LevelVisibility
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.file.constant.File
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.Questionnaire
import com.cleevio.fundedmind.domain.user.student.constant.OnboardingState
import com.cleevio.fundedmind.infrastructure.config.logger
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Table
import org.hibernate.annotations.DynamicUpdate
import org.hibernate.annotations.Type
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.time.Instant
import java.util.UUID

/**
 * Student is created once onboarding is finished. (see Onboarding)
 */
@Table(name = "student")
@Entity
@DynamicUpdate
class Student private constructor(
    id: UUID,
    profilePictureFileId: UUID?,
    firstName: String,
    lastName: String,
    phone: String?,
    biography: String?,
    country: Country?,
    studentTier: StudentTier,
    questionnaire: Questionnaire?,
    discordSubscription: Boolean,
    discordSubscriptionExpiresAt: Instant?,
    firstNameVocative: String,
    lastNameVocative: String,
    tierUpgradedAt: Instant,
    networkingVisibility: NetworkingVisibility,
    levelVisibility: LevelVisibility,
    locationId: UUID?,
    gameLevel: GameLevel,
    onboardingState: OnboardingState,
    masterclassInOnboarding: Boolean,
) : SoftDeletableEntity(id) {

    @File(type = FileType.STUDENT_PROFILE_PICTURE)
    var profilePictureFileId: UUID? = profilePictureFileId
        private set
    var firstName: String = firstName
        private set
    var lastName: String = lastName
        private set
    var firstNameVocative: String = firstNameVocative
        private set
    var lastNameVocative: String = lastNameVocative
        private set
    var phone: String? = phone // nullable at init, expected not null after onboarding finish
        private set
    var biography: String? = biography
        private set
    var discordSubscription: Boolean = discordSubscription
        private set
    var discordSubscriptionExpiresAt: Instant? = discordSubscriptionExpiresAt
        private set

    @Type(JsonType::class)
    @Column(columnDefinition = "jsonb")
    var questionnaire: Questionnaire? = questionnaire // nullable at init, expected not null after onboarding finish
        private set

    val questionnaireOrThrow: Questionnaire
        get() = questionnaire ?: throw StudentQuestionnaireMissingException("Student: '$id' has no questionnaire")

    @Enumerated(EnumType.STRING)
    var country: Country? = country // nullable at init, expected not null after onboarding finish
        private set

    @Enumerated(EnumType.STRING)
    var studentTier: StudentTier = studentTier
        private set

    var tierUpgradedAt: Instant = tierUpgradedAt
        private set

    @Enumerated(EnumType.STRING)
    var networkingVisibility: NetworkingVisibility = networkingVisibility
        private set

    @Enumerated(EnumType.STRING)
    var levelVisibility: LevelVisibility = levelVisibility
        private set

    var locationId: UUID? = locationId
        private set

    @Enumerated(EnumType.STRING)
    var gameLevel: GameLevel = gameLevel
        private set

    @Enumerated(EnumType.STRING)
    var onboardingState: OnboardingState = onboardingState
        private set

    var masterclassInOnboarding: Boolean = masterclassInOnboarding
        private set

    val isOnboardingFinished: Boolean
        get() = onboardingState.isComplete()

    val fullName: String
        get() = "$firstName $lastName"

    val hasMentoringAccess: Boolean
        get() = this.studentTier != StudentTier.BASECAMP

    companion object {
        // Use defaults instead of nullable types,
        // values are not shown to the user until they finish onboarding anyway
        // and during onboarding the values are updated to user's values anyway
        fun initiateAfterSignUp(
            userId: UUID,
            phone: String?,
        ) = Student(
            id = userId,
            profilePictureFileId = null,
            firstName = ONBOARDING_FIRST_NAME_PLACEHOLDER,
            lastName = ONBOARDING_LAST_NAME_PLACEHOLDER,
            firstNameVocative = ONBOARDING_FIRST_NAME_VOCATIVE_PLACEHOLDER,
            lastNameVocative = ONBOARDING_LAST_NAME_VOCATIVE_PLACEHOLDER,
            phone = phone,
            biography = null,
            country = null,
            studentTier = StudentTier.NO_TIER,
            questionnaire = null,
            discordSubscription = false,
            discordSubscriptionExpiresAt = null,
            tierUpgradedAt = ONBOARDING_TIER_UPGRADED_AT_PLACEHOLDER,
            networkingVisibility = NetworkingVisibility.ENABLED,
            levelVisibility = LevelVisibility.ENABLED,
            locationId = null,
            gameLevel = GameLevel.ZERO,
            onboardingState = OnboardingState.CHOOSE_PLAN,
            masterclassInOnboarding = false,
        )

        fun afterOnboarding(
            id: UUID,
            profilePictureFileId: UUID?,
            studentTier: StudentTier,
            firstName: String,
            lastName: String,
            phone: String,
            biography: String?,
            country: Country,
            questionnaire: Questionnaire,
            firstNameVocative: String,
            lastNameVocative: String,
            tierUpgradedAt: Instant,
            locationId: UUID?,
        ) = Student(
            id = id,
            profilePictureFileId = profilePictureFileId,
            studentTier = studentTier,
            firstName = firstName,
            lastName = lastName,
            phone = phone,
            biography = biography,
            country = country,
            questionnaire = questionnaire,
            discordSubscription = false,
            discordSubscriptionExpiresAt = null,
            firstNameVocative = firstNameVocative,
            lastNameVocative = lastNameVocative,
            tierUpgradedAt = tierUpgradedAt,
            networkingVisibility = NetworkingVisibility.ENABLED,
            levelVisibility = LevelVisibility.ENABLED,
            locationId = locationId,
            gameLevel = when (studentTier) {
                StudentTier.NO_TIER -> GameLevel.ZERO
                StudentTier.BASECAMP -> GameLevel.ZERO
                StudentTier.MASTERCLASS -> GameLevel.ONE
                StudentTier.EXCLUSIVE -> GameLevel.ONE
            },
            onboardingState = OnboardingState.CHOOSE_PLAN,
            masterclassInOnboarding = studentTier == StudentTier.MASTERCLASS,
        )
    }

    fun changeProfilePicture(fileId: UUID?) {
        this.profilePictureFileId = fileId
    }

    fun changeState(newState: OnboardingState) {
//        checkOnboardingNotYetFinished()
        this.onboardingState = newState
    }

    fun saveUserSurvey(
        firstName: String,
        lastName: String,
        phone: String,
        biography: String?,
        country: Country,
        firstNameVocative: String,
        lastNameVocative: String,
        locationId: UUID?,
    ) {
        checkOnboardingNotYetFinished()
        this.firstName = firstName
        this.lastName = lastName
        this.phone = phone
        this.biography = biography
        this.country = country
        this.firstNameVocative = firstNameVocative
        this.lastNameVocative = lastNameVocative
        this.locationId = locationId
    }

    fun saveQuestionnaire(questionnaire: Questionnaire) {
        checkOnboardingNotYetFinished()
        this.questionnaire = questionnaire
    }

    fun upgradeToBasecamp(tierUpgradedAt: Instant = Instant.now()) {
        checkOnboardingNotYetFinished()
        upgradeTier(desiredTier = StudentTier.BASECAMP, tierUpgradedAt = tierUpgradedAt, withCheck = true)
    }

    private fun shiftStateAfterMasterclassInOnboardingIfNeeded() {
        if (isOnboardingFinished) return
        if (studentTier != StudentTier.MASTERCLASS) return

        val logger = logger()
        if (onboardingState == OnboardingState.CHOOSE_PLAN) {
            logger.warn(
                "Onboarding: '$id' was in 'CHOOSE_PLAN' state " +
                    "even though they paid for 'MASTERCLASS'. Changing state to 'SURVEY'.",
            )
            changeState(OnboardingState.SURVEY)
        }

        if (onboardingState == OnboardingState.MASTERCLASS_CHECKOUT) {
            // move state forward just in case something unexpected happens on FE and they don't move it themselves,
            // user can reload and fetch the correct state if we move it to SURVEY on BE
            changeState(OnboardingState.SURVEY)
        }
    }

    fun finishOnboarding() {
//        checkOnboardingNotYetFinished()

        if (firstName == ONBOARDING_FIRST_NAME_PLACEHOLDER) {
            throw StudentOnboardingNotYetFinishedException(
                "Student: '$id' has not finished onboarding - missing first name",
            )
        }
        if (lastName == ONBOARDING_LAST_NAME_PLACEHOLDER) {
            throw StudentOnboardingNotYetFinishedException(
                "Student: '$id' has not finished onboarding - missing last name",
            )
        }
        if (firstNameVocative == ONBOARDING_FIRST_NAME_VOCATIVE_PLACEHOLDER) {
            throw StudentOnboardingNotYetFinishedException(
                "Student: '$id' has not finished onboarding - missing first name vocative",
            )
        }
        if (lastNameVocative == ONBOARDING_LAST_NAME_VOCATIVE_PLACEHOLDER) {
            throw StudentOnboardingNotYetFinishedException(
                "Student: '$id' has not finished onboarding - missing last name vocative",
            )
        }
        if (phone == null) {
            throw StudentOnboardingNotYetFinishedException("Student: '$id' has not finished onboarding - missing phone")
        }
        if (tierUpgradedAt == ONBOARDING_TIER_UPGRADED_AT_PLACEHOLDER) {
            throw StudentOnboardingNotYetFinishedException(
                "Student: '$id' has not finished onboarding - missing tier upgrade",
            )
        }
        if (country == null) {
            throw StudentOnboardingNotYetFinishedException(
                "Student: '$id' has not finished onboarding - missing country",
            )
        }
        if (questionnaire == null) {
            throw StudentOnboardingNotYetFinishedException(
                "Student: '$id' has not finished onboarding - missing questionnaire",
            )
        }

        changeState(OnboardingState.ONBOARDING_COMPLETE)
        updateGameLevel(
            newLevel = when (studentTier) {
                StudentTier.NO_TIER -> GameLevel.ZERO
                StudentTier.BASECAMP -> GameLevel.ZERO
                StudentTier.MASTERCLASS -> GameLevel.ONE
                StudentTier.EXCLUSIVE -> GameLevel.ONE
            },
        )
    }

    fun updateProfile(
        firstName: String,
        lastName: String,
        phone: String,
        biography: String?,
        country: Country,
        firstNameVocative: String,
        lastNameVocative: String,
        locationId: UUID?,
    ) {
        this.firstName = firstName
        this.lastName = lastName
        this.phone = phone
        this.biography = biography
        this.country = country
        this.firstNameVocative = firstNameVocative
        this.lastNameVocative = lastNameVocative
        this.locationId = locationId
    }

    fun activateDiscordSubscription(expiresAt: Instant) {
        this.discordSubscription = true
        this.discordSubscriptionExpiresAt = expiresAt
    }

    fun deactivateDiscordSubscription() {
        this.discordSubscription = false
    }

    fun adminUpdatesDiscordSubscription(expiresAt: Instant?) {
        if (expiresAt == null || expiresAt <= Instant.now()) {
            deactivateDiscordSubscription()
        } else {
            activateDiscordSubscription(expiresAt)
        }
    }

    fun adminUpdatesTier(
        studentTier: StudentTier,
        tierUpgradedAt: Instant = Instant.now(),
    ) {
        upgradeTier(desiredTier = studentTier, tierUpgradedAt = tierUpgradedAt, withCheck = false)
        if (gameLevel == GameLevel.ZERO) updateGameLevel(GameLevel.ONE)
    }

    fun upgradeToMasterclass(tierUpgradedAt: Instant = Instant.now()) {
        upgradeTier(desiredTier = StudentTier.MASTERCLASS, tierUpgradedAt = tierUpgradedAt, withCheck = true)
        if (gameLevel == GameLevel.ZERO) updateGameLevel(GameLevel.ONE)
    }

    fun upgradeToExclusive(tierUpgradedAt: Instant = Instant.now()) {
        upgradeTier(desiredTier = StudentTier.EXCLUSIVE, tierUpgradedAt = tierUpgradedAt, withCheck = true)
        if (gameLevel == GameLevel.ZERO) updateGameLevel(GameLevel.ONE)
    }

    fun updateQuestionnaireInsidePlatform(questionnaire: Questionnaire) {
        // questionnaire may be updated in the platform (not in the onboarding)
        // if user begins with BASECAMP and buys MASTERCLASS later in the platform
        this.questionnaire = questionnaire
    }

    fun patchPrivacy(
        networkingVisibility: NetworkingVisibility?,
        levelVisibility: LevelVisibility?,
    ) {
        networkingVisibility?.let { this.networkingVisibility = it }
        levelVisibility?.let { this.levelVisibility = it }
    }

    fun patchLocation(locationId: UUID?) {
        this.locationId = locationId
    }

    fun updateGameLevel(newLevel: GameLevel) {
        this.gameLevel = newLevel
    }

    private fun upgradeTier(
        desiredTier: StudentTier,
        tierUpgradedAt: Instant,
        withCheck: Boolean,
    ) {
        if (studentTier == desiredTier) return // no need to upgrade

        withCheck.ifTrue { checkCurrentTierIsBellow(desiredTier) }

        // Set masterclassInOnboarding flag if student is in onboarding and upgrading to MASTERCLASS
        if (desiredTier == StudentTier.MASTERCLASS && !isOnboardingFinished) {
            this.masterclassInOnboarding = true
        }

        this.studentTier = desiredTier
        this.tierUpgradedAt = tierUpgradedAt

        shiftStateAfterMasterclassInOnboardingIfNeeded()
    }

    fun checkCurrentTierIsBellow(other: StudentTier) {
        this.studentTier.isBelow(other).ifFalse {
            throw CannotUpgradeStudentTierException(
                "Student: '$id' has tier: $studentTier that is not below '$other'",
            )
        }
    }

    fun checkStudentTierIn(vararg allowedTiers: StudentTier) {
        if (this.studentTier !in allowedTiers) {
            throw StudentHasWrongStudentTierException(
                "Student: '$id' has wrong tier: $studentTier, allowed: ${allowedTiers.asList()}",
            )
        }
    }

    fun checkGameLevelIsAtLeast(minimumLevel: GameLevel) {
        if (this.gameLevel.order < minimumLevel.order) {
            throw StudentHasInsufficientGameLevelException(
                "Student: '$id' has wrong game level: $gameLevel, allowed: $minimumLevel and above",
            )
        }
    }

    fun checkHasActiveDiscord() {
        if (!this.discordSubscription) {
            throw StudentHasNoActiveDiscordException("Student: '$id' has no active discord subscription")
        }
    }

    fun isLockedForStudent(
        allowedTiers: List<StudentTier>,
        allowedDiscordUser: Boolean,
    ): Boolean {
        // If no one is allowed (empty tiers and no Discord users), it's locked
        if (allowedTiers.isEmpty() && !allowedDiscordUser) {
            return true
        }

        val accessViaTier = this.studentTier in allowedTiers
        val accessViaDiscord = allowedDiscordUser && this.discordSubscription

        val hasAccess = accessViaTier || accessViaDiscord

        val isLocked = !hasAccess

        return isLocked
    }

    fun checkHasMentoringAccess() {
        if (!this.hasMentoringAccess) {
            throw StudentHasNoAccessToMentoringException("Student: '$id' has no access to mentoring")
        }
    }

    fun checkOnboardingNotYetFinished() {
        if (isOnboardingFinished) {
            throw StudentAlreadyOnboardedException("Student with id: '$id' already onboarded.")
        }
    }
}

@Repository
interface StudentRepository : JpaRepository<Student, UUID>

const val ONBOARDING_FIRST_NAME_PLACEHOLDER: String = "uživatel"
const val ONBOARDING_FIRST_NAME_VOCATIVE_PLACEHOLDER: String = "uživateli"

const val ONBOARDING_LAST_NAME_PLACEHOLDER: String = "platformy"
const val ONBOARDING_LAST_NAME_VOCATIVE_PLACEHOLDER: String = "platformo"

val ONBOARDING_TIER_UPGRADED_AT_PLACEHOLDER: Instant = Instant.EPOCH
