package com.cleevio.fundedmind.domain.user.trader

import com.cleevio.fundedmind.application.common.type.CalendlyUserUri
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import com.cleevio.fundedmind.domain.user.trader.constant.TraderTag
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class CreateTraderService(
    private val traderRepository: TraderRepository,
) {
    @Transactional
    fun createTrader(
        id: UUID,
        profilePictureFileId: UUID?,
        position: String,
        listingOrder: Int,
        firstName: String,
        lastName: String,
        phone: String?,
        biography: String?,
        country: Country,
        tags: List<TraderTag>,
        badgeColor: BadgeColor,
        commentControl: Boolean,
        socialLinkInstagram: String?,
        socialLinkLinkedin: String?,
        socialLinkFacebook: String?,
        socialLinkTwitter: String?,
        calendlyUrl: String?,
        calendlyUserUri: CalendlyUserUri?,
        checkoutVideoUrl: String?,
        networkingVisibility: NetworkingVisibility,
        locationId: UUID?,
        mentoringDescription: String?,
        mentoringKeypoints: List<MentoringKeypoint>,
    ): Trader = traderRepository.save(
        Trader.promoteAccount(
            id = id,
            profilePictureFileId = profilePictureFileId,
            listingOrder = listingOrder,
            position = position,
            firstName = firstName,
            lastName = lastName,
            phone = phone,
            biography = biography,
            country = country,
            tags = tags,
            badgeColor = badgeColor,
            commentControl = commentControl,
            socialLinkInstagram = socialLinkInstagram,
            socialLinkLinkedin = socialLinkLinkedin,
            socialLinkFacebook = socialLinkFacebook,
            socialLinkTwitter = socialLinkTwitter,
            calendlyUrl = calendlyUrl,
            calendlyUserUri = calendlyUserUri,
            checkoutVideoUrl = checkoutVideoUrl,
            networkingVisibility = networkingVisibility,
            locationId = locationId,
            mentoringDescription = mentoringDescription,
            mentoringKeypoints = mentoringKeypoints,
        ),
    )
}
