package com.cleevio.fundedmind.domain.user.student

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class DeleteStudentService(
    private val studentRepository: StudentRepository,
) {

    @Transactional
    fun deleteStudentsByIds(studentIds: List<UUID>) {
        studentRepository.deleteAllById(studentIds)
    }

    @Transactional
    fun deleteById(studentId: UUID) {
        studentRepository.deleteById(studentId)
    }
}
