package com.cleevio.fundedmind.domain.user.student

import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.user.Questionnaire
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.util.UUID

@Service
class CreateStudentService(
    private val studentRepository: StudentRepository,
) {

    @Transactional
    fun createStudent(
        userId: UUID,
        phone: String?,
    ): Student = studentRepository.save(
        Student.initiateAfterSignUp(
            userId = userId,
            phone = phone,
        ),
    )

    @Transactional
    fun createStudent(
        id: UUID,
        profilePictureFileId: UUID?,
        studentTier: StudentTier,
        tierUpgradedAt: Instant,
        firstName: String,
        lastName: String,
        phone: String,
        biography: String?,
        country: Country,
        questionnaire: Questionnaire,
        firstNameVocative: String,
        lastNameVocative: String,
        locationId: UUID?,
    ): Student = studentRepository.save(
        Student.afterOnboarding(
            id = id,
            profilePictureFileId = profilePictureFileId,
            studentTier = studentTier,
            tierUpgradedAt = tierUpgradedAt,
            firstName = firstName,
            lastName = lastName,
            phone = phone,
            biography = biography,
            country = country,
            questionnaire = questionnaire,
            firstNameVocative = firstNameVocative,
            lastNameVocative = lastNameVocative,
            locationId = locationId,
        ),
    )
}
