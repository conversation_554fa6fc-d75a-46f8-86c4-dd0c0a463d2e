package com.cleevio.fundedmind.domain.user.appuser

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class DeleteVerificationCodeService(
    private val verificationCodeRepository: VerificationCodeRepository,
) {

    @Transactional
    fun deleteAllById(verificationCodeIds: List<UUID>) = verificationCodeRepository.deleteAllById(verificationCodeIds)
}
