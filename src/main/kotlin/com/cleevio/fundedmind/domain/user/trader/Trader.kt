package com.cleevio.fundedmind.domain.user.trader

import com.cleevio.fundedmind.application.common.type.CalendlyUserUri
import com.cleevio.fundedmind.application.common.util.replaceContents
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderHasWrongCalendlyDataException
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderHubspotPropertyNameMissingException
import com.cleevio.fundedmind.application.module.user.trader.exception.TraderOrderCannotBeNegativeException
import com.cleevio.fundedmind.domain.UpdatableEntity
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import com.cleevio.fundedmind.domain.common.constant.TraderMentoringAvailability
import com.cleevio.fundedmind.domain.file.constant.File
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.trader.constant.TraderTag
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Table
import org.hibernate.annotations.DynamicUpdate
import org.hibernate.annotations.Type
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.UUID

/**
 * Trader is created by ADMIN by promoting an account to a trader account.
 * Client (ADMIN) knows the trader personally and traders know they should create an account and
 * ADMIN will promote this account to a TRADER role.
 */
@Table(name = "trader")
@Entity
@DynamicUpdate
class Trader private constructor(
    id: UUID,
    listingOrder: Int,
    position: String,
    profilePictureFileId: UUID?,
    introPictureFileId: UUID?,
    firstName: String,
    lastName: String,
    phone: String?,
    biography: String?,
    country: Country,
    badgeColor: BadgeColor,
    tags: List<TraderTag>,
    commentControl: Boolean,
    socialLinkInstagram: String?,
    socialLinkLinkedin: String?,
    socialLinkFacebook: String?,
    socialLinkTwitter: String?,
    mentoringPhotoFileId: UUID?,
    calendlyUrl: String?,
    calendlyUserUri: CalendlyUserUri?,
    checkoutVideoUrl: String?,
    mentoringAvailability: TraderMentoringAvailability,
    networkingVisibility: NetworkingVisibility,
    locationId: UUID?,
    mentoringDescription: String?,
    mentoringKeypoints: List<MentoringKeypoint>,
    private var hubspotPropertyName: String?,
) : UpdatableEntity(id) {

    init {
        checkListingOrderIsPositiveOrZero(listingOrder)
        checkCalendlyData(calendlyUrl = calendlyUrl, calendlyUserUri = calendlyUserUri)
    }

    var listingOrder: Int = listingOrder
        private set
    var position: String = position
        private set
    var firstName: String = firstName
        private set
    var lastName: String = lastName
        private set
    var phone: String? = phone
        private set
    var biography: String? = biography
        private set

    @Enumerated(EnumType.STRING)
    var country: Country = country
        private set

    var checkoutVideoUrl: String? = checkoutVideoUrl
        private set

    @Enumerated(EnumType.STRING)
    @Column(columnDefinition = "text[]")
    private val _tags: MutableList<TraderTag> = tags.toMutableList()
    val tags: List<TraderTag>
        get() = _tags.toList()

    @Enumerated(EnumType.STRING)
    var badgeColor: BadgeColor = badgeColor
        private set

    var socialLinkInstagram: String? = socialLinkInstagram
        private set
    var socialLinkLinkedin: String? = socialLinkLinkedin
        private set
    var socialLinkFacebook: String? = socialLinkFacebook
        private set
    var socialLinkTwitter: String? = socialLinkTwitter
        private set
    var calendlyUrl: String? = calendlyUrl
        private set
    var calendlyUserUri: CalendlyUserUri? = calendlyUserUri
        private set

    var commentControl: Boolean = commentControl
        private set

    @File(type = FileType.TRADER_PROFILE_PICTURE)
    var profilePictureFileId: UUID? = profilePictureFileId
        private set

    @File(type = FileType.TRADER_INTRO_PICTURE)
    var introPictureFileId: UUID? = introPictureFileId
        private set

    @File(type = FileType.TRADER_MENTORING_PHOTO)
    var mentoringPhotoFileId: UUID? = mentoringPhotoFileId
        private set

    @Enumerated(EnumType.STRING)
    var mentoringAvailability: TraderMentoringAvailability = mentoringAvailability
        private set

    @Enumerated(EnumType.STRING)
    var networkingVisibility: NetworkingVisibility = networkingVisibility
        private set

    var locationId: UUID? = locationId
        private set

    var mentoringDescription: String? = mentoringDescription
        private set

    @Type(JsonType::class)
    @Column(columnDefinition = "jsonb")
    private val _mentoringKeypoints: MutableList<MentoringKeypoint> = mentoringKeypoints.toMutableList()
    val mentoringKeypoints: List<MentoringKeypoint>
        get() = _mentoringKeypoints.toList()

    val fullName: String
        get() = "$firstName $lastName"

    val hasHubspotPropertyName: Boolean
        get() = hubspotPropertyName != null

    val hubspotPropertyNameOrThrow: String
        get() = hubspotPropertyName
            ?: throw TraderHubspotPropertyNameMissingException("Hubspot property name is not set for trader: '$id'.")

    companion object {
        fun promoteAccount(
            id: UUID,
            profilePictureFileId: UUID?,
            listingOrder: Int,
            position: String,
            firstName: String,
            lastName: String,
            phone: String?,
            biography: String?,
            country: Country,
            tags: List<TraderTag>,
            badgeColor: BadgeColor,
            commentControl: Boolean,
            socialLinkInstagram: String?,
            socialLinkLinkedin: String?,
            socialLinkFacebook: String?,
            socialLinkTwitter: String?,
            calendlyUrl: String?,
            calendlyUserUri: CalendlyUserUri?,
            checkoutVideoUrl: String?,
            networkingVisibility: NetworkingVisibility,
            locationId: UUID?,
            mentoringDescription: String?,
            mentoringKeypoints: List<MentoringKeypoint>,
        ) = Trader(
            id = id,
            listingOrder = listingOrder,
            profilePictureFileId = profilePictureFileId,
            introPictureFileId = null,
            position = position,
            firstName = firstName,
            lastName = lastName,
            phone = phone,
            biography = biography,
            country = country,
            badgeColor = badgeColor,
            tags = tags,
            commentControl = commentControl,
            socialLinkInstagram = socialLinkInstagram,
            socialLinkLinkedin = socialLinkLinkedin,
            socialLinkFacebook = socialLinkFacebook,
            socialLinkTwitter = socialLinkTwitter,
            mentoringPhotoFileId = null,
            calendlyUrl = calendlyUrl,
            calendlyUserUri = calendlyUserUri,
            checkoutVideoUrl = checkoutVideoUrl,
            hubspotPropertyName = null,
            mentoringAvailability = TraderMentoringAvailability.AUTOMATIC,
            networkingVisibility = networkingVisibility,
            locationId = locationId,
            mentoringDescription = mentoringDescription,
            mentoringKeypoints = mentoringKeypoints,
        )
    }

    fun changeProfilePicture(fileId: UUID?) {
        this.profilePictureFileId = fileId
    }

    fun changeIntroPicture(fileId: UUID?) {
        this.introPictureFileId = fileId
    }

    fun changeMentoringPhoto(fileId: UUID?) {
        this.mentoringPhotoFileId = fileId
    }

    fun updateListingOrder(newOrder: Int) {
        checkListingOrderIsPositiveOrZero(newOrder)
        this.listingOrder = newOrder
    }

    fun updateProfile(
        firstName: String,
        lastName: String,
        phone: String?,
        biography: String?,
        country: Country,
        locationId: UUID?,
    ) {
        this.firstName = firstName
        this.lastName = lastName
        this.phone = phone
        this.biography = biography
        this.country = country
        this.locationId = locationId
    }

    fun update(
        position: String,
        firstName: String,
        lastName: String,
        biography: String?,
        tags: List<TraderTag>,
        badgeColor: BadgeColor,
        commentControl: Boolean,
        socialLinkInstagram: String?,
        socialLinkLinkedin: String?,
        socialLinkFacebook: String?,
        socialLinkTwitter: String?,
        calendlyUrl: String?,
        calendlyUserUri: CalendlyUserUri?,
        checkoutVideoUrl: String?,
        mentoringAvailability: TraderMentoringAvailability,
        mentoringDescription: String?,
        mentoringKeypoints: List<MentoringKeypoint>,
    ) {
        checkCalendlyData(calendlyUrl = calendlyUrl, calendlyUserUri = calendlyUserUri)

        this.position = position
        this.firstName = firstName
        this.lastName = lastName
        this.biography = biography
        this._tags.replaceContents(tags)
        this.badgeColor = badgeColor
        this.commentControl = commentControl
        this.socialLinkInstagram = socialLinkInstagram
        this.socialLinkLinkedin = socialLinkLinkedin
        this.socialLinkFacebook = socialLinkFacebook
        this.socialLinkTwitter = socialLinkTwitter
        this.calendlyUrl = calendlyUrl
        this.calendlyUserUri = calendlyUserUri
        this.checkoutVideoUrl = checkoutVideoUrl
        this.mentoringAvailability = mentoringAvailability
        this.mentoringDescription = mentoringDescription
        this._mentoringKeypoints.replaceContents(mentoringKeypoints)
    }

    fun updateHubspotPropertyName(hubspotPropertyName: String?) {
        this.hubspotPropertyName = hubspotPropertyName
    }

    fun patchPrivacy(networkingVisibility: NetworkingVisibility?) {
        networkingVisibility?.let { this.networkingVisibility = it }
    }

    fun patchLocation(locationId: UUID?) {
        this.locationId = locationId
    }

    // for testing purposes
    internal fun patchMentoringAvailability(mentoringAvailability: TraderMentoringAvailability) {
        this.mentoringAvailability = mentoringAvailability
    }

    private fun checkListingOrderIsPositiveOrZero(listingOrder: Int) {
        if (listingOrder >= 0) return

        throw TraderOrderCannotBeNegativeException("Listing order cannot be negative: '$listingOrder'")
    }

    private fun checkCalendlyData(
        calendlyUrl: String?,
        calendlyUserUri: CalendlyUserUri?,
    ) {
        // either both are present or both are null
        when {
            calendlyUrl == null && calendlyUserUri == null -> return

            calendlyUrl != null && calendlyUserUri != null -> return

            else -> throw TraderHasWrongCalendlyDataException(
                "Trader: '$id' must either have full Calendly data or none, " +
                    "but received: url: $calendlyUrl and uri: $calendlyUserUri ",
            )
        }
    }
}

@Repository
interface TraderRepository : JpaRepository<Trader, UUID> {
    fun countAllByIdIn(ids: Collection<UUID>): Long

    @Query("SELECT MAX(t.listingOrder) FROM Trader t")
    fun findMaxListingOrder(): Int?

    fun existsByCalendlyUserUri(calendlyUserUri: CalendlyUserUri): Boolean
    fun findByCalendlyUserUri(calendlyUserUri: CalendlyUserUri): Trader?

    @Query(
        """
            SELECT (count(t) > 0)
            FROM Trader t
            INNER JOIN AppUser au ON au.id = t.id
            WHERE t.id = :traderId
            AND au.accountActive = true
        """,
    )
    fun existsByIdAndAccountActiveTrue(traderId: UUID): Boolean

    fun findAllByCalendlyUserUriIn(calendlyUserUris: List<CalendlyUserUri>): List<Trader>

    @Query(
        """
            SELECT t
            FROM Product p
            INNER JOIN Trader t ON p.traderId = t.id
            WHERE p.id = :productId
        """,
    )
    fun findByProductId(productId: UUID): Trader?
}
