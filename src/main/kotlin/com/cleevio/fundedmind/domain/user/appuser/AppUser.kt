package com.cleevio.fundedmind.domain.user.appuser

import com.cleevio.fundedmind.application.common.type.FirebaseId
import com.cleevio.fundedmind.application.common.type.HubspotId
import com.cleevio.fundedmind.application.common.type.StripeCustomerId
import com.cleevio.fundedmind.application.common.util.UUIDv7
import com.cleevio.fundedmind.application.module.user.appuser.exception.UserAccountHasWrongActiveStatusException
import com.cleevio.fundedmind.application.module.user.appuser.exception.UserHasWrongRoleException
import com.cleevio.fundedmind.application.module.user.appuser.exception.UserIsNotTrackedInPaymentSystemException
import com.cleevio.fundedmind.domain.UpdatableEntity
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Table
import org.hibernate.annotations.DynamicUpdate
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.UUID

@Table(name = "app_user")
@Entity
@DynamicUpdate
class AppUser private constructor(
    id: UUID,
    role: UserRole,
    accountActive: Boolean,
    email: String,
    val firebaseIdentifier: FirebaseId,
    val hubspotIdentifier: HubspotId,
    stripeIdentifiers: List<StripeCustomerId> = emptyList(),
    val traderReferral: String?,
    referralApplied: Boolean,
) : UpdatableEntity(id) {

    @Column(columnDefinition = "text[]")
    private val _stripeIdentifiers: MutableList<StripeCustomerId> = stripeIdentifiers.toMutableList()
    val stripeIdentifiers: List<StripeCustomerId>
        get() = _stripeIdentifiers.toList()

    /**
     * All student users have at least one Stripe identifier.
     * All checkout sessions (masterclass/discord/exclusive) are created for the first (and initial) Stripe customer.
     *
     * Once a person uses PaymentLink to buy something,
     * there is a new Stripe Customer its id will be appended to the list `_stripeIdentifiers` via [addStripeIdentifier]
     */
    val firstStripeIdentifierOrThrow: StripeCustomerId
        get() = stripeIdentifiers.firstOrNull()
            ?: throw UserIsNotTrackedInPaymentSystemException("User: '$id' is not tracked by Stripe.")

    var email: String = email
        private set

    var accountActive: Boolean = accountActive
        private set

    @Enumerated(EnumType.STRING)
    var role: UserRole = role
        private set

    var referralApplied: Boolean = referralApplied
        private set

    companion object {
        fun newStudentAccount(
            id: UUID = UUIDv7.randomUUID(),
            email: String,
            firebaseIdentifier: FirebaseId,
            hubspotIdentifier: HubspotId,
            stripeIdentifier: StripeCustomerId,
            traderReferral: String?,
        ) = AppUser(
            id = id,
            email = email.lowercase(),
            role = UserRole.STUDENT,
            hubspotIdentifier = hubspotIdentifier,
            firebaseIdentifier = firebaseIdentifier,
            stripeIdentifiers = listOf(stripeIdentifier),
            accountActive = true,
            traderReferral = traderReferral,
            referralApplied = false,
        )

        fun newAdminAccount(
            id: UUID = UUIDv7.randomUUID(),
            email: String,
            firebaseIdentifier: FirebaseId,
            hubspotIdentifier: HubspotId,
        ) = AppUser(
            id = id,
            email = email.lowercase(),
            role = UserRole.ADMIN,
            firebaseIdentifier = firebaseIdentifier,
            hubspotIdentifier = hubspotIdentifier,
            stripeIdentifiers = emptyList(),
            accountActive = true,
            traderReferral = null,
            referralApplied = false,
        )
    }

    fun updateEmail(newEmail: String) {
        this.email = newEmail
    }

    fun changeRole(newRole: UserRole) {
        this.role = newRole
    }

    fun disableAccount() {
        checkActive(true)
        this.accountActive = false
    }

    fun enableAccount() {
        checkActive(false)
        this.accountActive = true
    }

    fun checkRole(expectedRole: UserRole) {
        if (this.role != expectedRole) {
            throw UserHasWrongRoleException("User: '$id' has role: '$role' but expected: '$expectedRole'")
        }
    }

    fun applyReferralIfPossible(now: Instant = Instant.now()): Boolean {
        if (referralApplied) {
            return false // referral already applied
        }
        if (traderReferral == null) {
            return false // user was not registered via trader and therefore no referral is given to the trader
        }

        val oneHourAgo = now.minus(1, ChronoUnit.HOURS)

        if (createdAt.isAfter(oneHourAgo)) {
            this.referralApplied = true
        }

        return referralApplied
    }

    private fun checkActive(expectedActive: Boolean) {
        if (this.accountActive != expectedActive) {
            throw UserAccountHasWrongActiveStatusException(
                "User: '$id' has active: '$accountActive' but expected: '$expectedActive'",
            )
        }
    }

    fun addStripeIdentifier(stripeIdentifier: StripeCustomerId) {
        if (!_stripeIdentifiers.contains(stripeIdentifier)) {
            _stripeIdentifiers.add(stripeIdentifier)
        }
    }

    fun hasStripeIdentifier(stripeIdentifier: StripeCustomerId): Boolean = _stripeIdentifiers.contains(stripeIdentifier)
}

@Repository
interface AppUserRepository : JpaRepository<AppUser, UUID> {
    fun findByEmailIgnoreCase(email: String): AppUser?
    fun existsByEmailIgnoreCase(email: String): Boolean
    fun findByHubspotIdentifier(hubspotIdentifier: Long): AppUser?
    fun existsByFirebaseIdentifier(firebaseIdentifier: FirebaseId): Boolean
    fun findByFirebaseIdentifier(firebaseIdentifier: FirebaseId): AppUser?

    @Query(
        value = "SELECT * FROM app_user WHERE :stripeId = ANY(stripe_identifiers)",
        nativeQuery = true,
    )
    fun findByContainingStripeIdentifier(stripeId: StripeCustomerId): AppUser?
}
