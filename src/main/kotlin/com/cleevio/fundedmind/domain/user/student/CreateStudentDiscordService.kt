package com.cleevio.fundedmind.domain.user.student

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.util.UUID

@Service
class CreateStudentDiscordService(
    private val studentDiscordRepository: StudentDiscordRepository,
) {

    @Transactional
    fun createStudentDiscord(
        studentId: UUID,
        discordId: String,
        username: String,
        globalName: String?,
        joinedAt: Instant,
    ): StudentDiscord = studentDiscordRepository.save(
        StudentDiscord.newLinkedDiscordAccount(
            studentId = studentId,
            discordId = discordId,
            userName = username,
            globalName = globalName,
            joinedAt = joinedAt,
        ),
    )
}
