package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.module.lesson.command.CreateNewLessonCommand
import com.cleevio.fundedmind.application.module.lesson.command.LessonAttachmentInput
import com.cleevio.fundedmind.application.module.lesson.command.ReorderLessonsInCourseModuleCommand
import com.cleevio.fundedmind.application.module.lesson.command.ReorderLessonsInCourseModuleCommand.LessonOrderingInput
import com.cleevio.fundedmind.application.module.lesson.command.UpdateLessonCommand
import com.cleevio.fundedmind.application.module.lesson.query.ListLessonsInCourseModuleQuery
import com.cleevio.fundedmind.domain.lesson.constant.LessonAttachmentType
import java.util.UUID

data class CreateNewLessonRequest(
    val title: String,
    val description: String?,
    val videoUrl: String,
    val durationInSeconds: Int,
    val thumbnailUrl: String,
    val thumbnailAnimationUrl: String,
    val attachments: List<LessonAttachmentRequest>,
) {
    fun toCommand(
        courseId: UUID,
        courseModuleId: UUID,
    ) = CreateNewLessonCommand(
        courseId = courseId,
        courseModuleId = courseModuleId,
        title = title,
        description = description,
        videoUrl = videoUrl,
        durationInSeconds = durationInSeconds,
        thumbnailUrl = thumbnailUrl,
        thumbnailAnimationUrl = thumbnailAnimationUrl,
        attachments = attachments.map { it.toInput() },
    )
}

data class UpdateLessonRequest(
    val title: String,
    val description: String?,
    val videoUrl: String,
    val durationInSeconds: Int,
    val thumbnailUrl: String,
    val thumbnailAnimationUrl: String,
    val attachments: List<LessonAttachmentRequest>,
) {
    fun toCommand(
        courseId: UUID,
        courseModuleId: UUID,
        lessonId: UUID,
    ) = UpdateLessonCommand(
        courseId = courseId,
        courseModuleId = courseModuleId,
        lessonId = lessonId,
        title = title,
        description = description,
        videoUrl = videoUrl,
        durationInSeconds = durationInSeconds,
        thumbnailUrl = thumbnailUrl,
        thumbnailAnimationUrl = thumbnailAnimationUrl,
        attachments = attachments.map { it.toInput() },
    )
}

data class ListLessonsInCourseModuleRequest(
    val searchString: String?,
) {
    fun toQuery(
        courseId: UUID,
        courseModuleId: UUID,
    ) = ListLessonsInCourseModuleQuery(
        filter = ListLessonsInCourseModuleQuery.Filter(
            searchString = searchString,
            courseId = courseId,
            courseModuleId = courseModuleId,
        ),
    )
}

data class ReorderLessonsInCourseModuleRequest(
    val lessonOrderings: List<ReorderLessonsInCourseModuleLessonRequest>,
) {
    fun toCommand(
        courseId: UUID,
        courseModuleId: UUID,
    ) = ReorderLessonsInCourseModuleCommand(
        courseId = courseId,
        courseModuleId = courseModuleId,
        lessonOrderings = lessonOrderings.map {
            LessonOrderingInput(
                lessonId = it.lessonId,
                newListingOrder = it.newListingOrder,
            )
        },
    )

    data class ReorderLessonsInCourseModuleLessonRequest(
        val lessonId: UUID,
        val newListingOrder: Int,
    )
}

data class LessonAttachmentRequest(
    val lessonAttachmentId: UUID?, // null when creating new attachment, not null when updating
    val name: String,
    val type: LessonAttachmentType,
) {
    fun toInput() = LessonAttachmentInput(
        lessonAttachmentId = lessonAttachmentId,
        name = name,
        type = type,
    )
}
