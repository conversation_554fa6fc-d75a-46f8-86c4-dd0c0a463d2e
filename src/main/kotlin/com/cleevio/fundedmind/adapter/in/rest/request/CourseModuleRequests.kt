package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.module.coursemodule.command.CreateNewCourseModuleCommand
import com.cleevio.fundedmind.application.module.coursemodule.command.ReorderCourseModulesInCourseCommand
import com.cleevio.fundedmind.application.module.coursemodule.command.ReorderCourseModulesInCourseCommand.CourseModuleOrderingInput
import com.cleevio.fundedmind.application.module.coursemodule.command.UpdateCourseModuleCommand
import com.cleevio.fundedmind.application.module.coursemodule.query.ListCourseModulesInCourseQuery
import java.util.UUID

data class CreateNewCourseModuleRequest(
    val title: String,
    val description: String,
    val shortDescription: String,
    val rewardDescription: String?,
    val rewardCouponCode: String?,
    val rewardButton: AppButtonWithLinkRequest?,
    val comingSoon: Boolean,
) {
    fun toCommand(courseId: UUID) = CreateNewCourseModuleCommand(
        courseId = courseId,
        title = title,
        description = description,
        shortDescription = shortDescription,
        rewardDescription = rewardDescription,
        rewardCouponCode = rewardCouponCode,
        rewardButton = rewardButton?.toInput(),
        comingSoon = comingSoon,
    )
}

data class UpdateCourseModuleRequest(
    val title: String,
    val description: String,
    val shortDescription: String,
    val rewardDescription: String?,
    val rewardCouponCode: String?,
    val rewardButton: AppButtonWithLinkRequest?,
    val comingSoon: Boolean,
) {
    fun toCommand(
        courseId: UUID,
        courseModuleId: UUID,
    ) = UpdateCourseModuleCommand(
        courseId = courseId,
        courseModuleId = courseModuleId,
        title = title,
        description = description,
        shortDescription = shortDescription,
        rewardDescription = rewardDescription,
        rewardCouponCode = rewardCouponCode,
        rewardButton = rewardButton?.toInput(),
        comingSoon = comingSoon,
    )
}

data class ListCourseModulesInCourseRequest(
    val searchString: String?,
) {
    fun toQuery(courseId: UUID) = ListCourseModulesInCourseQuery(
        filter = ListCourseModulesInCourseQuery.Filter(
            searchString = searchString,
            courseId = courseId,
        ),
    )
}

data class ReorderCourseModulesInCourseRequest(
    val courseModuleOrderings: List<ReorderCourseModulesInCourseModuleRequest>,
) {
    fun toCommand(courseId: UUID) = ReorderCourseModulesInCourseCommand(
        courseId = courseId,
        courseModuleOrderings = courseModuleOrderings.map {
            CourseModuleOrderingInput(
                courseModuleId = it.courseModuleId,
                newListingOrder = it.newListingOrder,
            )
        },
    )

    data class ReorderCourseModulesInCourseModuleRequest(
        val courseModuleId: UUID,
        val newListingOrder: Int,
    )
}
