package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.module.gamelevelreward.command.CreateNewGameLevelRewardCommand
import com.cleevio.fundedmind.application.module.gamelevelreward.command.ReorderGameLevelRewardsCommand
import com.cleevio.fundedmind.application.module.gamelevelreward.command.UpdateGameLevelRewardCommand
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.gamelevelreward.constant.GameLevelRewardType
import java.util.UUID

data class CreateNewGameLevelRewardRequest(
    val name: String,
    val gameLevel: GameLevel,
    val type: GameLevelRewardType,
    val description: String?,
    val rewardCouponCode: String?,
    val rewardButton: AppButtonWithLinkRequest?,
) {
    fun toCommand() = CreateNewGameLevelRewardCommand(
        name = name,
        gameLevel = gameLevel,
        type = type,
        description = description,
        rewardCouponCode = rewardCouponCode,
        rewardButton = rewardButton?.toInput(),
    )
}

data class UpdateGameLevelRewardRequest(
    val name: String,
    val gameLevel: GameLevel,
    val type: GameLevelRewardType,
    val description: String?,
    val rewardCouponCode: String?,
    val rewardButton: AppButtonWithLinkRequest?,
) {
    fun toCommand(gameLevelRewardId: UUID) = UpdateGameLevelRewardCommand(
        gameLevelRewardId = gameLevelRewardId,
        name = name,
        gameLevel = gameLevel,
        type = type,
        description = description,
        rewardCouponCode = rewardCouponCode,
        rewardButton = rewardButton?.toInput(),
    )
}

data class ReorderGameLevelRewardsRequest(
    val gameLevelRewardOrderings: List<ReorderGameLevelRewardsGameLevelRewardRequest>,
) {
    fun toCommand() = ReorderGameLevelRewardsCommand(
        gameLevelRewardOrderings = gameLevelRewardOrderings.map {
            ReorderGameLevelRewardsCommand.GameLevelRewardOrderingInput(
                gameLevelRewardId = it.gameLevelRewardId,
                newListingOrder = it.newListingOrder,
            )
        },
    )

    data class ReorderGameLevelRewardsGameLevelRewardRequest(
        val gameLevelRewardId: UUID,
        val newListingOrder: Int,
    )
}
