package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.adapter.`in`.InfiniteScrollAsc
import com.cleevio.fundedmind.application.module.course.command.CreateNewCourseCommand
import com.cleevio.fundedmind.application.module.course.command.ReorderCoursesByCategoryCommand
import com.cleevio.fundedmind.application.module.course.command.ReorderCoursesByCategoryCommand.CourseOrderingInput
import com.cleevio.fundedmind.application.module.course.command.UpdateCourseCommand
import com.cleevio.fundedmind.application.module.course.query.ListCoursesByCategoryQuery
import com.cleevio.fundedmind.application.module.course.query.ListPublishedCoursesByCategoryQuery
import com.cleevio.fundedmind.application.module.course.query.SearchCoursesQuery
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import java.util.UUID

data class CreateNewCourseRequest(
    val title: String,
    val courseCategory: CourseCategory,
    val visibleToTiers: List<StudentTier>,
    val visibleToDiscordUsers: Boolean,
    val description: String,
    val traderId: UUID,
    val color: Color,
    val thumbnailUrl: String,
    val thumbnailAnimationUrl: String,
    val trailerUrl: String,
    val public: Boolean,
    val homepage: Boolean,
) {
    fun toCommand() = CreateNewCourseCommand(
        title = title,
        courseCategory = courseCategory,
        visibleToTiers = visibleToTiers,
        visibleToDiscordUsers = visibleToDiscordUsers,
        description = description,
        traderId = traderId,
        color = color,
        thumbnailUrl = thumbnailUrl,
        thumbnailAnimationUrl = thumbnailAnimationUrl,
        trailerUrl = trailerUrl,
        public = public,
        homepage = homepage,
    )
}

data class UpdateCourseRequest(
    val title: String,
    val courseCategory: CourseCategory,
    val visibleToTiers: List<StudentTier>,
    val visibleToDiscordUsers: Boolean,
    val description: String,
    val traderId: UUID,
    val color: Color,
    val thumbnailUrl: String,
    val thumbnailAnimationUrl: String,
    val trailerUrl: String,
    val public: Boolean,
    val homepage: Boolean,
) {
    fun toCommand(courseId: UUID) = UpdateCourseCommand(
        courseId = courseId,
        title = title,
        courseCategory = courseCategory,
        visibleToTiers = visibleToTiers,
        visibleToDiscordUsers = visibleToDiscordUsers,
        description = description,
        traderId = traderId,
        color = color,
        thumbnailUrl = thumbnailUrl,
        thumbnailAnimationUrl = thumbnailAnimationUrl,
        trailerUrl = trailerUrl,
        public = public,
        homepage = homepage,
    )
}

data class SearchCoursesRequest(
    val searchString: String?,
) {
    fun toQuery(infiniteScroll: InfiniteScrollAsc.Identifier) = SearchCoursesQuery(
        infiniteScroll = infiniteScroll,
        filter = SearchCoursesQuery.Filter(
            searchString = searchString,
        ),
    )
}

data class ListCoursesByCategoryRequest(
    val searchString: String?,
    val courseCategory: CourseCategory,
) {
    fun toQuery() = ListCoursesByCategoryQuery(
        filter = ListCoursesByCategoryQuery.Filter(
            searchString = searchString,
            courseCategory = courseCategory,
        ),
    )
}

data class ListPublishedCoursesByCategoryRequest(
    val courseCategory: CourseCategory,
) {
    fun toQuery(userId: UUID) = ListPublishedCoursesByCategoryQuery(
        userId = userId,
        filter = ListPublishedCoursesByCategoryQuery.Filter(
            courseCategory = courseCategory,
        ),
    )
}

data class ReorderCoursesByCategoryRequest(
    val courseOrderings: List<ReorderCoursesCourseRequest>,
    val courseCategory: CourseCategory,
) {
    fun toCommand() = ReorderCoursesByCategoryCommand(
        courseCategory = courseCategory,
        courseOrderings = courseOrderings.map {
            CourseOrderingInput(
                courseId = it.courseId,
                newListingOrder = it.newListingOrder,
            )
        },
    )

    data class ReorderCoursesCourseRequest(
        val courseId: UUID,
        val newListingOrder: Int,
    )
}
