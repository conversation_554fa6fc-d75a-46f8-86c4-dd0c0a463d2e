package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.module.progress.command.SaveUserLessonProgressCommand
import java.util.UUID

data class SaveUserLessonProgressRequest(
    val seconds: Int,
) {
    fun toCommand(
        lessonId: UUID,
        userId: UUID,
    ) = SaveUserLessonProgressCommand(
        lessonId = lessonId,
        userId = userId,
        seconds = seconds,
    )
}
