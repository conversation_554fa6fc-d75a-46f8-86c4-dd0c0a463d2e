package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.module.mentoringtestimonial.command.CreateMentoringTestimonialCommand
import com.cleevio.fundedmind.application.module.mentoringtestimonial.command.UpdateMentoringTestimonialCommand
import java.util.UUID

data class CreateMentoringTestimonialRequest(
    val firstName: String,
    val lastName: String,
    val rating: Int,
    val description: String,
    val pictureFileId: UUID?,
) {
    fun toCommand(traderId: UUID) = CreateMentoringTestimonialCommand(
        traderId = traderId,
        firstName = firstName,
        lastName = lastName,
        rating = rating,
        description = description,
        pictureFileId = pictureFileId,
    )
}

data class UpdateMentoringTestimonialRequest(
    val firstName: String,
    val lastName: String,
    val rating: Int,
    val description: String,
    val pictureFileId: UUID?,
) {
    fun toCommand(
        testimonialId: UUID,
        traderId: UUID,
    ) = UpdateMentoringTestimonialCommand(
        testimonialId = testimonialId,
        traderId = traderId,
        firstName = firstName,
        lastName = lastName,
        rating = rating,
        description = description,
        pictureFileId = pictureFileId,
    )
}
