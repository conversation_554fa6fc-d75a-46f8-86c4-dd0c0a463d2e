package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.module.user.onboarding.command.ChangeOnboardingStateCommand
import com.cleevio.fundedmind.application.module.user.onboarding.command.SaveOnboardQuestionnaireCommand
import com.cleevio.fundedmind.application.module.user.onboarding.command.SaveOnboardSurveyCommand
import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.user.student.constant.OnboardingState
import java.util.UUID

data class ChangeOnboardingStateRequest(
    val newState: OnboardingState,
) {
    fun toCommand(studentId: UUID) = ChangeOnboardingStateCommand(
        studentId = studentId,
        newState = newState,
    )
}

data class SaveOnboardSurveyRequest(
    val firstName: String,
    val lastName: String,
    val phone: String,
    val biography: String?,
    val country: Country,
    val firstNameVocative: String,
    val lastNameVocative: String,
    val location: UserLocationRequest?,
) {
    fun toCommand(studentId: UUID) = SaveOnboardSurveyCommand(
        studentId = studentId,
        firstName = firstName,
        lastName = lastName,
        phone = phone,
        biography = biography,
        country = country,
        firstNameVocative = firstNameVocative,
        lastNameVocative = lastNameVocative,
        location = location?.toInput(),
    )
}

data class SaveOnboardQuestionnaireRequest(
    val questionnaire: QuestionnaireInput,
) {
    fun toCommand(studentId: UUID) = SaveOnboardQuestionnaireCommand(
        studentId = studentId,
        questionnaire = questionnaire,
    )
}
