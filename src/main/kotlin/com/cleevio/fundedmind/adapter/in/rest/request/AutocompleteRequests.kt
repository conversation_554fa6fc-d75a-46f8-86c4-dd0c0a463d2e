package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.module.product.query.AutocompleteProductQuery
import com.cleevio.fundedmind.application.module.user.appuser.query.AutocompleteStudentsQuery
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import java.util.UUID

data class AutocompleteStudentsRequest(
    val searchString: String?,
    val studentId: UUID?,
    val studentTiers: List<StudentTier>?,
    val limit: Int = 10,
) {
    fun toQuery() = AutocompleteStudentsQuery(
        limit = limit,
        filter = AutocompleteStudentsQuery.Filter(
            searchString = searchString,
            studentId = studentId,
            studentTiers = studentTiers,
        ),
    )
}

data class AutocompleteProductQueryRequest(
    val searchString: String?,
    val traderId: UUID,
    val limit: Int = 10,
) {
    fun toQuery() = AutocompleteProductQuery(
        limit = limit,
        filter = AutocompleteProductQuery.Filter(
            traderId = traderId,
            searchString = searchString,
        ),
    )
}
