package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.module.referral.command.CreateNewReferralCommand
import com.cleevio.fundedmind.application.module.referral.command.ReorderReferralsCommand
import com.cleevio.fundedmind.application.module.referral.command.ReorderReferralsCommand.ReferralOrderingInput
import com.cleevio.fundedmind.application.module.referral.command.UpdateReferralCommand
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import java.util.UUID

data class CreateNewReferralRequest(
    val title: String?,
    val description: String?,
    val visibleToTiers: List<StudentTier>,
    val visibleToDiscordUsers: Boolean,
    val linkUrl: String?,
    val rewardCouponCode: String?,
) {
    fun toCommand() = CreateNewReferralCommand(
        title = title,
        description = description,
        visibleToTiers = visibleToTiers,
        visibleToDiscordUsers = visibleToDiscordUsers,
        linkUrl = linkUrl,
        rewardCouponCode = rewardCouponCode,
    )
}

data class UpdateReferralRequest(
    val title: String?,
    val description: String?,
    val visibleToTiers: List<StudentTier>,
    val visibleToDiscordUsers: Boolean,
    val linkUrl: String?,
    val rewardCouponCode: String?,
) {
    fun toCommand(referralId: UUID) = UpdateReferralCommand(
        referralId = referralId,
        title = title,
        description = description,
        visibleToTiers = visibleToTiers,
        visibleToDiscordUsers = visibleToDiscordUsers,
        linkUrl = linkUrl,
        rewardCouponCode = rewardCouponCode,
    )
}

data class ReorderReferralsRequest(
    val referralOrderings: List<ReorderReferralsReferralRequest>,
) {
    fun toCommand() = ReorderReferralsCommand(
        referralOrderings = referralOrderings.map {
            ReferralOrderingInput(
                referralId = it.referralId,
                newListingOrder = it.newListingOrder,
            )
        },
    )

    data class ReorderReferralsReferralRequest(
        val referralId: UUID,
        val newListingOrder: Int,
    )
}
