package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.common.type.CalendlyUserUri
import com.cleevio.fundedmind.application.module.user.trader.command.CreateNewTraderCommand
import com.cleevio.fundedmind.application.module.user.trader.command.MentoringKeypointInput
import com.cleevio.fundedmind.application.module.user.trader.command.ReorderTradersCommand
import com.cleevio.fundedmind.application.module.user.trader.command.ReorderTradersCommand.TraderOrderingInput
import com.cleevio.fundedmind.application.module.user.trader.command.TraderCalendlyInput
import com.cleevio.fundedmind.application.module.user.trader.command.UpdateTraderCommand
import com.cleevio.fundedmind.application.module.user.trader.query.ListAllTradersQuery
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.TraderMentoringAvailability
import com.cleevio.fundedmind.domain.user.trader.constant.TraderTag
import java.util.UUID

data class CreateNewTraderRequest(
    val studentId: UUID,
    val position: String,
    val firstName: String,
    val lastName: String,
    val biography: String?,
    val tags: List<TraderTag>,
    val badgeColor: BadgeColor,
    val commentControl: Boolean,
    val socialLinkInstagram: String?,
    val socialLinkLinkedin: String?,
    val socialLinkFacebook: String?,
    val socialLinkTwitter: String?,
    val calendly: TraderCalendlyRequest?,
    val checkoutVideoUrl: String?,
    val mentoringDescription: String?,
    val mentoringKeypoints: List<MentoringKeypointRequest>,
) {
    fun toCommand() = CreateNewTraderCommand(
        studentId = studentId,
        position = position,
        firstName = firstName,
        lastName = lastName,
        biography = biography,
        tags = tags.toList(),
        badgeColor = badgeColor,
        commentControl = commentControl,
        socialLinkInstagram = socialLinkInstagram,
        socialLinkLinkedin = socialLinkLinkedin,
        socialLinkFacebook = socialLinkFacebook,
        socialLinkTwitter = socialLinkTwitter,
        calendly = calendly?.toInput(),
        checkoutVideoUrl = checkoutVideoUrl,
        mentoringDescription = mentoringDescription,
        mentoringKeypoints = mentoringKeypoints.map { it.toInput() },
    )
}

data class UpdateTraderRequest(
    val position: String,
    val firstName: String,
    val lastName: String,
    val biography: String?,
    val tags: List<TraderTag>,
    val badgeColor: BadgeColor,
    val commentControl: Boolean,
    val socialLinkInstagram: String?,
    val socialLinkLinkedin: String?,
    val socialLinkFacebook: String?,
    val socialLinkTwitter: String?,
    val calendly: TraderCalendlyRequest?,
    val checkoutVideoUrl: String?,
    val mentoringAvailability: TraderMentoringAvailability,
    val mentoringDescription: String?,
    val mentoringKeypoints: List<MentoringKeypointRequest>,
) {
    fun toCommand(traderId: UUID) = UpdateTraderCommand(
        traderId = traderId,
        position = position,
        firstName = firstName,
        lastName = lastName,
        biography = biography,
        tags = tags.toList(),
        badgeColor = badgeColor,
        commentControl = commentControl,
        socialLinkInstagram = socialLinkInstagram,
        socialLinkLinkedin = socialLinkLinkedin,
        socialLinkFacebook = socialLinkFacebook,
        socialLinkTwitter = socialLinkTwitter,
        calendly = calendly?.toInput(),
        checkoutVideoUrl = checkoutVideoUrl,
        mentoringAvailability = mentoringAvailability,
        mentoringDescription = mentoringDescription,
        mentoringKeypoints = mentoringKeypoints.map { it.toInput() },
    )
}

data class TraderCalendlyRequest(
    val calendlyUrl: String,
    val calendlyUserUri: CalendlyUserUri,
) {
    fun toInput() = TraderCalendlyInput(
        calendlyUrl = calendlyUrl,
        calendlyUserUri = calendlyUserUri,
    )
}

data class ListAllTradersRequest(
    val searchString: String?,
) {
    fun toQuery() = ListAllTradersQuery(
        filter = ListAllTradersQuery.Filter(
            searchString = searchString,
        ),
    )
}

data class ReorderTradersRequest(
    val traderOrderings: List<ReorderTradersTraderRequest>,
) {
    fun toCommand() = ReorderTradersCommand(
        traderOrderings = traderOrderings.map {
            TraderOrderingInput(
                traderId = it.traderId,
                newListingOrder = it.newListingOrder,
            )
        },
    )

    data class ReorderTradersTraderRequest(
        val traderId: UUID,
        val newListingOrder: Int,
    )
}

data class MentoringKeypointRequest(
    val title: String,
    val text: String,
) {
    fun toInput() = MentoringKeypointInput(
        title = title,
        text = text,
    )
}
