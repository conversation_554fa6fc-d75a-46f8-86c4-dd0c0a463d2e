package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.module.user.appuser.command.ApplyVerificationCodeCommand
import com.cleevio.fundedmind.application.module.user.appuser.command.SendVerificationCodeCommand
import java.util.UUID

data class SendVerificationCodeRequest(
    val code: String,
) {
    fun toCommand(userId: UUID) = SendVerificationCodeCommand(
        userId = userId,
        code = code,
    )
}

data class ApplyVerificationCodeRequest(
    val code: String,
) {
    fun toCommand(userId: UUID) = ApplyVerificationCodeCommand(
        userId = userId,
        code = code,
    )
}
