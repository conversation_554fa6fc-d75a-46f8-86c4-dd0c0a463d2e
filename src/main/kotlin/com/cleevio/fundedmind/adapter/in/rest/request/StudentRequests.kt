package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.common.type.StripeSessionId
import com.cleevio.fundedmind.application.module.user.student.command.StudentUpdatesProfileCommand
import com.cleevio.fundedmind.application.module.user.student.command.StudentUpdatesQuestionnaireCommand
import com.cleevio.fundedmind.application.module.user.student.command.UpdateStudentBusinessPaymentDataCommand
import com.cleevio.fundedmind.domain.common.constant.Country
import java.util.UUID

data class StudentUpdatesProfileRequest(
    val firstName: String,
    val lastName: String,
    val phone: String,
    val biography: String?,
    val country: Country,
    val firstNameVocative: String,
    val lastNameVocative: String,
    val location: UserLocationRequest?,
) {
    fun toCommand(studentId: UUID) = StudentUpdatesProfileCommand(
        studentId = studentId,
        firstName = firstName,
        lastName = lastName,
        phone = phone,
        biography = biography,
        country = country,
        firstNameVocative = firstNameVocative,
        lastNameVocative = lastNameVocative,
        location = location?.toInput(),
    )
}

data class StudentUpdatesQuestionnaireRequest(
    val questionnaire: QuestionnaireInput,
) {
    fun toCommand(studentId: UUID) = StudentUpdatesQuestionnaireCommand(
        studentId = studentId,
        questionnaire = questionnaire,
    )
}

data class UpdateStudentBusinessPaymentDataRequest(
    val checkoutSessionId: StripeSessionId,
    val businessPayment: Boolean,
    val ico: String?,
    val dic: String?,
) {
    fun toCommand(studentId: UUID) = UpdateStudentBusinessPaymentDataCommand(
        studentId = studentId,
        checkoutSessionId = checkoutSessionId,
        businessPayment = businessPayment,
        ico = ico,
        dic = dic,
    )
}
