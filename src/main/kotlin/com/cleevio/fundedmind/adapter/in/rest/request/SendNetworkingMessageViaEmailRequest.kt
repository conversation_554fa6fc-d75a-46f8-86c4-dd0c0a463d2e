package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.module.networking.command.SendNetworkingMessageViaEmailCommand
import java.util.UUID

data class SendNetworkingMessageViaEmailRequest(
    val text: String,
) {
    fun toCommand(
        senderUserId: UUID,
        recipientUserId: UUID,
    ) = SendNetworkingMessageViaEmailCommand(
        senderUserId = senderUserId,
        recipientUserId = recipientUserId,
        text = text,
    )
}
