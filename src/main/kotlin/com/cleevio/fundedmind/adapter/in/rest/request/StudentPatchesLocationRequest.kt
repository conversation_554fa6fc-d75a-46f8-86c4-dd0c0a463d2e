package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.common.command.UserLocationInput
import com.cleevio.fundedmind.application.module.user.student.command.StudentPatchesLocationCommand
import java.util.UUID

data class StudentPatchesLocationRequest(
    val location: UserLocationInput?,
) {
    fun toCommand(studentId: UUID) = StudentPatchesLocationCommand(
        studentId = studentId,
        location = location,
    )
}
