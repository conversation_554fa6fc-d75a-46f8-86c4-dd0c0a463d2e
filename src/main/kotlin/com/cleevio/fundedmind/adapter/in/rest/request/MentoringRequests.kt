package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.module.mentoring.command.StudentInquiriesMentoringCommand
import com.cleevio.fundedmind.application.module.mentoring.command.TraderCreatesManualMentoringCommand
import com.cleevio.fundedmind.application.module.mentoring.query.ExistsOngoingMentoringQuery
import java.util.UUID

data class ExistsOngoingMentoringRequest(
    val traderId: UUID,
) {
    fun toQuery(studentId: UUID) = ExistsOngoingMentoringQuery(
        studentId = studentId,
        traderId = traderId,
    )
}

data class TraderCreatesManualMentoringRequest(
    val productId: UUID,
    val studentId: UUID,
) {
    fun toCommand(traderId: UUID) = TraderCreatesManualMentoringCommand(
        traderId = traderId,
        productId = productId,
        studentId = studentId,
    )
}

data class StudentInquiriesMentoringRequest(
    val inquiryAnswers: List<MentoringInquiryAnswerRequest>,
    val traderId: UUID,
) {
    data class MentoringInquiryAnswerRequest(
        val question: String,
        val answer: String,
    )

    fun toCommand(studentId: UUID) = StudentInquiriesMentoringCommand(
        studentId = studentId,
        traderId = traderId,
        inquiryAnswers = inquiryAnswers.map {
            StudentInquiriesMentoringCommand.MentoringInquiryAnswerInput(
                question = it.question,
                answer = it.answer,
            )
        },
    )
}
