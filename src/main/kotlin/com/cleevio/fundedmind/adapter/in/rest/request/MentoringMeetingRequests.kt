package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.common.type.CalendlyEventUri
import com.cleevio.fundedmind.application.module.mentoringmeeting.command.TraderUpdatesMentoringMeetingRecordingCommand
import com.cleevio.fundedmind.application.module.mentoringmeeting.query.ListMentoringMeetingsInCalendarQuery
import com.cleevio.fundedmind.application.module.mentoringmeeting.query.SearchMentoringMeetingsQuery
import java.time.LocalDate
import java.util.UUID

data class ListMentoringMeetingsInCalendarRequest(
    val startDate: LocalDate,
    val endDate: LocalDate,
) {
    fun toQuery(userId: UUID) = ListMentoringMeetingsInCalendarQuery(
        userId = userId,
        filter = ListMentoringMeetingsInCalendarQuery.Filter(
            startDate = startDate,
            endDate = endDate,
        ),
    )
}

data class SearchMentoringMeetingsRequest(
    val scheduledEventUri: CalendlyEventUri,
) {
    fun toQuery(userId: UUID) = SearchMentoringMeetingsQuery(
        userId = userId,
        filter = SearchMentoringMeetingsQuery.Filter(
            calendlyEventUri = scheduledEventUri,
        ),
    )
}

data class TraderUpdatesMentoringMeetingRecordingRequest(
    val recordingUrl: String?,
) {
    fun toCommand(
        traderId: UUID,
        mentoringMeetingId: UUID,
    ) = TraderUpdatesMentoringMeetingRecordingCommand(
        traderId = traderId,
        mentoringMeetingId = mentoringMeetingId,
        recordingUrl = recordingUrl,
    )
}
