package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.common.command.AppButtonInput
import com.cleevio.fundedmind.application.common.command.AppButtonWithLinkInput
import com.cleevio.fundedmind.domain.common.constant.Color

data class AppButtonRequest(
    val text: String,
    val color: Color,
) {
    fun toInput() = AppButtonInput(
        text = text,
        color = color,
    )
}

data class AppButtonWithLinkRequest(
    val text: String,
    val color: Color,
    val linkUrl: String,
) {
    fun toInput() = AppButtonWithLinkInput(
        text = text,
        color = color,
        linkUrl = linkUrl,
    )
}
