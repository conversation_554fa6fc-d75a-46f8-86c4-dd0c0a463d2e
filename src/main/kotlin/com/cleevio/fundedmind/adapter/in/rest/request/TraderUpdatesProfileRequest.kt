package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.module.user.trader.command.TraderUpdatesProfileCommand
import com.cleevio.fundedmind.domain.common.constant.Country
import java.util.UUID

data class TraderUpdatesProfileRequest(
    val traderId: UUID,
    val firstName: String,
    val lastName: String,
    val phone: String?,
    val biography: String?,
    val country: Country,
    val location: UserLocationRequest?,
) {
    fun toCommand() = TraderUpdatesProfileCommand(
        traderId = traderId,
        firstName = firstName,
        lastName = lastName,
        phone = phone,
        biography = biography,
        country = country,
        location = location?.toInput(),
    )
}
