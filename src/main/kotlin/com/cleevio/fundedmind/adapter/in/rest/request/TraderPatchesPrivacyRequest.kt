package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.module.user.trader.command.TraderPatchesPrivacyCommand
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import java.util.UUID

data class TraderPatchesPrivacyRequest(
    val networkingVisibility: NetworkingVisibility?,
) {
    fun toCommand(traderId: UUID) = TraderPatchesPrivacyCommand(
        traderId = traderId,
        networkingVisibility = networkingVisibility,
    )
}
