package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.domain.user.student.constant.QuestionnaireQuestionType
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate

@Schema(description = "Version should be a date. Example: \"2025-01-01\"")
data class QuestionnaireInput(
    val version: LocalDate,
    val data: List<QuestionnaireStep>,
) {
    data class QuestionnaireStep(
        val question: QuestionnaireQuestion,
        val answers: List<QuestionnaireAnswer>,
    )

    data class QuestionnaireQuestion(
        val propertyName: String,
        val text: String,
        val type: QuestionnaireQuestionType,
    )

    data class QuestionnaireAnswer(
        val propertyName: String,
        val text: String,
        val selected: <PERSON><PERSON><PERSON>,
    )
}
