package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.module.meeting.command.CreateNewMeetingCommand
import com.cleevio.fundedmind.application.module.meeting.command.UpdateMeetingCommand
import com.cleevio.fundedmind.application.module.meeting.query.ListMeetingsInCalendarQuery
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import java.time.Instant
import java.time.LocalDate
import java.util.UUID

data class CreateNewMeetingRequest(
    val name: String,
    val color: Color,
    val startAt: Instant,
    val finishAt: Instant,
    val traderIds: List<UUID>,
    val description: String?,
    val invitedTiers: List<StudentTier>,
    val invitedDiscordUsers: Boolean,
    val meetingUrl: String?,
    val recordingUrl: String?,
) {
    fun toCommand() = CreateNewMeetingCommand(
        name = name,
        color = color,
        startAt = startAt,
        finishAt = finishAt,
        traderIds = traderIds,
        description = description,
        invitedTiers = invitedTiers,
        invitedDiscordUsers = invitedDiscordUsers,
        meetingUrl = meetingUrl,
        recordingUrl = recordingUrl,
    )
}

data class UpdateMeetingRequest(
    val name: String,
    val color: Color,
    val startAt: Instant,
    val finishAt: Instant,
    val traderIds: List<UUID>,
    val description: String?,
    val invitedTiers: List<StudentTier>,
    val invitedDiscordUsers: Boolean,
    val meetingUrl: String?,
    val recordingUrl: String?,
) {
    fun toCommand(meetingId: UUID) = UpdateMeetingCommand(
        meetingId = meetingId,
        name = name,
        color = color,
        startAt = startAt,
        finishAt = finishAt,
        traderIds = traderIds,
        description = description,
        invitedTiers = invitedTiers,
        invitedDiscordUsers = invitedDiscordUsers,
        meetingUrl = meetingUrl,
        recordingUrl = recordingUrl,
    )
}

data class ListMeetingsInCalendarRequest(
    val startDate: LocalDate,
    val endDate: LocalDate,
) {
    fun toQuery(userId: UUID) = ListMeetingsInCalendarQuery(
        userId = userId,
        filter = ListMeetingsInCalendarQuery.Filter(
            startDate = startDate,
            endDate = endDate,
        ),
    )
}
