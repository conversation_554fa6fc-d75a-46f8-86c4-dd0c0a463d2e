package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.adapter.`in`.InfiniteScroll
import com.cleevio.fundedmind.application.common.command.GeoLocationInput
import com.cleevio.fundedmind.application.module.networking.query.ListPeopleInNetworkingQuery
import com.cleevio.fundedmind.application.module.networking.query.SearchPeopleInNetworkingQuery
import java.util.UUID

data class ListPeopleInNetworkingRequest(
    val searchString: String?,
    val northEast: GeoLocationRequest,
    val southWest: GeoLocationRequest,
) {
    fun toQuery(userId: UUID) = ListPeopleInNetworkingQuery(
        userId = userId,
        filter = ListPeopleInNetworkingQuery.Filter(
            searchString = searchString,
            northEast = GeoLocationInput(
                latitude = northEast.latitude,
                longitude = northEast.longitude,
            ),
            southWest = GeoLocationInput(
                latitude = southWest.latitude,
                longitude = southWest.longitude,
            ),
        ),
    )
}

data class SearchPeopleInNetworkingRequest(
    val searchString: String?,
) {
    fun toQuery(
        userId: UUID,
        infiniteScroll: InfiniteScroll<UUID>,
    ) = SearchPeopleInNetworkingQuery(
        userId = userId,
        infiniteScroll = infiniteScroll,
        filter = SearchPeopleInNetworkingQuery.Filter(
            searchString = searchString,
        ),
    )
}
