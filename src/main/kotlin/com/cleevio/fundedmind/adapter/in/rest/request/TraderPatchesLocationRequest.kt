package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.common.command.UserLocationInput
import com.cleevio.fundedmind.application.module.user.trader.command.TraderPatchesLocationCommand
import java.util.UUID

data class TraderPatchesLocationRequest(
    val location: UserLocationInput?,
) {
    fun toCommand(traderId: UUID) = TraderPatchesLocationCommand(
        traderId = traderId,
        location = location,
    )
}
