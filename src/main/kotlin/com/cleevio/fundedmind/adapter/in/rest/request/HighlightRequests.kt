package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.module.highlight.command.CreateNewHighlightCommand
import com.cleevio.fundedmind.application.module.highlight.command.ReorderHighlightsCommand
import com.cleevio.fundedmind.application.module.highlight.command.ReorderHighlightsCommand.HighlightOrderingInput
import com.cleevio.fundedmind.application.module.highlight.command.UpdateHighlightCommand
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import java.util.UUID

data class CreateNewHighlightRequest(
    val title: String?,
    val description: String?,
    val visibleToTiers: List<StudentTier>,
    val visibleToDiscordUsers: Boolean,
    val linkUrl: String?,
    val button: AppButtonRequest?,
) {
    fun toCommand() = CreateNewHighlightCommand(
        title = title,
        description = description,
        visibleToTiers = visibleToTiers,
        visibleToDiscordUsers = visibleToDiscordUsers,
        linkUrl = linkUrl,
        button = button?.toInput(),
    )
}

data class UpdateHighlightRequest(
    val title: String?,
    val description: String?,
    val visibleToTiers: List<StudentTier>,
    val visibleToDiscordUsers: Boolean,
    val linkUrl: String?,
    val button: AppButtonRequest?,
) {
    fun toCommand(highlightId: UUID) = UpdateHighlightCommand(
        highlightId = highlightId,
        title = title,
        description = description,
        visibleToTiers = visibleToTiers,
        visibleToDiscordUsers = visibleToDiscordUsers,
        linkUrl = linkUrl,
        button = button?.toInput(),
    )
}

data class ReorderHighlightsRequest(
    val highlightOrderings: List<ReorderHighlightsHighlightRequest>,
) {
    fun toCommand() = ReorderHighlightsCommand(
        highlightOrderings = highlightOrderings.map {
            HighlightOrderingInput(
                highlightId = it.highlightId,
                newListingOrder = it.newListingOrder,
            )
        },
    )

    data class ReorderHighlightsHighlightRequest(
        val highlightId: UUID,
        val newListingOrder: Int,
    )
}
