package com.cleevio.fundedmind.adapter.`in`.rest.request

import com.cleevio.fundedmind.application.common.command.GeoLocationInput
import com.cleevio.fundedmind.application.common.command.UserLocationInput

data class UserLocationRequest(
    val street: String?,
    val city: String?,
    val postalCode: String?,
    val state: String?,
    val geoLocation: GeoLocationRequest,
) {
    fun toInput() = UserLocationInput(
        street = street,
        city = city,
        postalCode = postalCode,
        state = state,
        geoLocation = geoLocation.toInput(),
    )
}

data class GeoLocationRequest(
    val latitude: Double,
    val longitude: Double,
) {
    fun toInput() = GeoLocationInput(
        latitude = latitude,
        longitude = longitude,
    )
}
