package com.cleevio.fundedmind.adapter.out.stripe

import com.cleevio.fundedmind.adapter.out.IntegrationException
import com.cleevio.fundedmind.adapter.out.stripe.metadata.StripeCustomerMetadata
import com.cleevio.fundedmind.adapter.out.stripe.metadata.StripeCustomerMetadataParams
import com.cleevio.fundedmind.application.common.command.MoneyResult
import com.cleevio.fundedmind.application.common.type.StripeCouponId
import com.cleevio.fundedmind.application.common.type.StripeCustomerId
import com.cleevio.fundedmind.application.common.type.StripeInvoiceId
import com.cleevio.fundedmind.application.common.type.StripePriceId
import com.cleevio.fundedmind.application.common.type.StripeProductId
import com.cleevio.fundedmind.application.common.type.StripeSessionId
import com.cleevio.fundedmind.application.common.type.StripeSubscriptionId
import com.cleevio.fundedmind.domain.common.constant.MonetaryCurrency
import com.cleevio.fundedmind.domain.common.constant.TaxBehaviour
import com.cleevio.fundedmind.infrastructure.config.logger
import com.cleevio.fundedmind.infrastructure.properties.StripeProperties
import com.stripe.Stripe
import com.stripe.exception.StripeException
import com.stripe.model.Customer
import com.stripe.model.Invoice
import com.stripe.model.Price
import com.stripe.model.Product
import com.stripe.model.PromotionCode
import com.stripe.model.Subscription
import com.stripe.model.TaxRate
import com.stripe.model.checkout.Session
import com.stripe.net.RequestOptions
import com.stripe.param.CustomerCreateParams
import com.stripe.param.CustomerListParams
import com.stripe.param.CustomerUpdateParams
import com.stripe.param.InvoiceListParams
import com.stripe.param.InvoiceRetrieveParams
import com.stripe.param.PriceListParams
import com.stripe.param.PriceRetrieveParams
import com.stripe.param.ProductListParams
import com.stripe.param.PromotionCodeCreateParams
import com.stripe.param.PromotionCodeListParams
import com.stripe.param.SubscriptionListParams
import com.stripe.param.checkout.SessionCreateParams
import com.stripe.param.checkout.SessionRetrieveParams
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.util.UUID

@Component
class StripeConnector(
    private val stripeProperties: StripeProperties,
) {

    private val logger = logger()
    private lateinit var inclusiveTaxRate: BigDecimal
    private lateinit var exclusiveTaxRate: BigDecimal

    init {
        Stripe.apiKey = stripeProperties.apiKey

        executeWithStripeHandling {
            logger.debug("Retrieving tax rates from Stripe...")
            inclusiveTaxRate = TaxRate.retrieve(stripeProperties.czechTaxRate.inclusive).percentage
            exclusiveTaxRate = TaxRate.retrieve(stripeProperties.czechTaxRate.exclusive).percentage
            logger.debug("Tax rates retrieved")
        }
    }

    fun existsProductById(productId: StripeProductId): Boolean = executeWithStripeHandling {
        val productParams = ProductListParams.builder()
            .addId(productId)
            .setLimit(1)
            .build()

        Product.list(productParams).data.isNotEmpty()
    }

    fun getDefaultPriceByProductId(productId: StripeProductId): Price =
        findAllDefaultPricesByProductIds(listOf(productId)).values.single()

    fun findAllDefaultPricesByProductIds(productIds: List<StripeProductId>): Map<StripeProductId, Price> =
        executeWithStripeHandling {
            if (productIds.isEmpty()) return@executeWithStripeHandling emptyMap()

            val productParams = ProductListParams.builder()
                .apply {
                    addAllId(productIds)
                    addExpand("data.default_price") // Expand response with product's default price
                }
                .setLimit(100)
                .build()

            val products = Product.list(productParams).data

            products
                .filter { it.active }
                .associate { product ->
                    val price = product.defaultPriceObject
                        ?: error("Product ${product.id} does not have an expanded default_price")

                    product.id to price
                }
        }

    fun getPriceById(priceId: StripePriceId): Price = executeWithStripeHandling {
        val params: PriceRetrieveParams = PriceRetrieveParams
            .builder()
            .addExpand("product")
            .build()

        Price.retrieve(priceId, params, RequestOptions.getDefault())
    }

    fun getProductPricesByProductId(productId: StripeProductId): List<Price> = executeWithStripeHandling {
        val params: PriceListParams = PriceListParams
            .builder()
            .apply {
                setProduct(productId)
                setLimit(100)
                setActive(true)
            }
            .build()

        Price.list(params).data
    }

    fun findCustomerIdByEmail(email: String): StripeCustomerId? = executeWithStripeHandling {
        val params = CustomerListParams
            .builder()
            .setEmail(email)
            .build()

        Customer
            .list(params)
            .data
            .firstOrNull()
            ?.id
    }

    fun findCustomerIdsByEmail(email: String): List<StripeCustomerId> = executeWithStripeHandling {
        val params = CustomerListParams
            .builder()
            .setEmail(email)
            .setLimit(100)
            .build()

        Customer
            .list(params)
            .data
            .map { it.id }
    }

    fun createCustomer(email: String): StripeCustomerId = executeWithStripeHandling {
        val params = CustomerCreateParams
            .builder()
            .setEmail(email)
            .build()

        val createdCustomer = Customer.create(params)

        createdCustomer.id
    }

    fun updateCustomerEmail(
        customerId: StripeCustomerId,
        newEmail: String,
    ): Unit = executeWithStripeHandling {
        val customer = Customer.retrieve(customerId)

        val params = CustomerUpdateParams
            .builder()
            .setEmail(newEmail)
            .build()

        customer.update(params)
    }

    fun updateCustomerName(
        customerId: StripeCustomerId,
        appUserId: UUID,
        fullName: String,
    ): Unit = executeWithStripeHandling {
        val customer = Customer.retrieve(customerId)

        val oldMetadata = StripeCustomerMetadataParams.fromStripeMetadata(customer.metadata)

        val params = CustomerUpdateParams
            .builder()
            .setName(fullName)
            .putAllMetadata(
                StripeCustomerMetadataParams.of(
                    StripeCustomerMetadata(
                        appUserId = appUserId,
                        ico = oldMetadata.ico,
                        dic = oldMetadata.dic,
                    ),
                ),
            )
            .build()

        customer.update(params)
    }

    fun getCheckoutSessionById(checkoutSessionId: StripeSessionId): Session = executeWithStripeHandling {
        Session.retrieve(
            /* session = */
            checkoutSessionId,
            /* params = */
            SessionRetrieveParams
                .builder()
                .addExpand("line_items")
                .build(),
            /* options = */
            RequestOptions.getDefault(),
        )
    }

    fun patchCustomerBusinessData(
        customerId: StripeCustomerId,
        appUserId: UUID,
        ico: String?,
        dic: String?,
    ): Unit = executeWithStripeHandling {
        val customer = Customer.retrieve(customerId)

        val params = CustomerUpdateParams
            .builder()
            .putAllMetadata(
                StripeCustomerMetadataParams.of(
                    StripeCustomerMetadata(
                        appUserId = appUserId,
                        ico = ico,
                        dic = dic,
                    ),
                ),
            )
            .build()

        customer.update(params)
    }

    fun getCustomerById(customerId: StripeCustomerId): Customer = executeWithStripeHandling {
        Customer.retrieve(customerId)
    }

    fun createCustomerPortalSession(customerId: StripeCustomerId): com.stripe.model.billingportal.Session =
        executeWithStripeHandling {
            val params = com.stripe.param.billingportal.SessionCreateParams.builder()
                .setCustomer(customerId)
                .build()

            com.stripe.model.billingportal.Session.create(params)
        }

    fun createSubscriptionCheckoutSession(
        priceId: StripePriceId,
        customerId: StripeCustomerId,
        trialPeriodDays: Long?,
    ): Session = executeWithStripeHandling {
        val price: Price = getPriceById(priceId)

        val taxBehaviour = TaxBehaviour.fromStripe(price.taxBehavior)

        val itemToSell = SessionCreateParams.LineItem
            .builder()
            .setPrice(price.id)
            .apply {
                when (taxBehaviour) {
                    TaxBehaviour.INCLUSIVE -> addTaxRate(stripeProperties.czechTaxRate.inclusive)
                    TaxBehaviour.EXCLUSIVE -> addTaxRate(stripeProperties.czechTaxRate.exclusive)
                }
            }
            .setQuantity(1L)
            .build()

        val params = SessionCreateParams
            .builder()
            .setMode(SessionCreateParams.Mode.SUBSCRIPTION)
            .apply {
                trialPeriodDays?.let {
                    require(trialPeriodDays >= 2)

                    // Stripe Custom UI doesn't support 'billing_cycle_anchor',
                    // so we use trial days as a workaround not to start billing before the desired date.
                    val subscriptionData = SessionCreateParams.SubscriptionData
                        .builder()
                        .setTrialPeriodDays(trialPeriodDays)
                        .build()

                    setSubscriptionData(subscriptionData)
                }
            }
            .setUiMode(SessionCreateParams.UiMode.CUSTOM)
            .setAllowPromotionCodes(true)
            .setCustomer(customerId)
            .addLineItem(itemToSell)
            .build()

        Session.create(params)
    }

    fun createProductCheckoutSession(
        productId: StripeProductId,
        customerId: StripeCustomerId,
        metadata: Map<String, String>,
    ): Session = executeWithStripeHandling {
        val customer = Customer.retrieve(customerId)

        val price: Price = getDefaultPriceByProductId(productId)

        val taxBehaviour = TaxBehaviour.fromStripe(price.taxBehavior)

        val itemToSell = SessionCreateParams.LineItem
            .builder()
            .setPrice(price.id)
            .apply {
                when (taxBehaviour) {
                    TaxBehaviour.INCLUSIVE -> addTaxRate(stripeProperties.czechTaxRate.inclusive)
                    TaxBehaviour.EXCLUSIVE -> addTaxRate(stripeProperties.czechTaxRate.exclusive)
                }
            }
            .setQuantity(1L)
            .build()

        val paymentIntentDetails = SessionCreateParams.PaymentIntentData
            .builder()
            .setReceiptEmail(customer.email)
            .putAllMetadata(metadata)
            .build()

        val invoiceParams = SessionCreateParams.InvoiceCreation
            .builder()
            .setEnabled(true)
            .build()

        val params = SessionCreateParams
            .builder()
            .setMode(SessionCreateParams.Mode.PAYMENT)
            .setUiMode(SessionCreateParams.UiMode.CUSTOM)
            .setAllowPromotionCodes(true)
            .setCustomer(customerId)
            .addLineItem(itemToSell)
            .setPaymentIntentData(paymentIntentDetails)
            .setInvoiceCreation(invoiceParams)
            .build()

        Session.create(params)
    }

    fun getSubscriptionById(subscriptionId: StripeSubscriptionId): Subscription = executeWithStripeHandling {
        Subscription.retrieve(subscriptionId)
    }

    fun getSubscriptionsByCustomerId(customerId: StripeCustomerId): List<Subscription> = executeWithStripeHandling {
        val params = SubscriptionListParams
            .builder()
            .setStatus(SubscriptionListParams.Status.ALL)
            .setCustomer(customerId)
            .setLimit(100L) // Limit is 100, and we do not expect such amount of subscriptions per user :D
            .build()

        Subscription
            .list(params)
            .data
    }

    fun getInvoiceWithCustomerByInvoiceId(invoiceId: StripeInvoiceId): Invoice = executeWithStripeHandling {
        val params = InvoiceRetrieveParams
            .builder()
            .addExpand("customer")
            .addExpand("lines.data.discount_amounts.discount")
            .build()

        Invoice.retrieve(invoiceId, params, RequestOptions.getDefault())
    }

    fun findAllInvoicesByCustomerId(customerId: StripeCustomerId): List<Invoice> = executeWithStripeHandling {
        val params = InvoiceListParams
            .builder()
            .addExpand("data.customer")
            .setCustomer(customerId)
            .setLimit(100)
            .build()

        Invoice.list(params).data
    }

    fun generatePromotionCodes(
        couponId: StripeCouponId,
        quantity: Int,
        maxRedemptions: Int,
    ): List<String> = executeWithStripeHandling {
        val promotionCodes = mutableListOf<String>()

        repeat(quantity) {
            val params = PromotionCodeCreateParams
                .builder()
                .setCoupon(couponId)
                .setMaxRedemptions(maxRedemptions.toLong())
                .build()

            val promotionCode = PromotionCode.create(params)

            promotionCodes.add(promotionCode.code)
        }

        promotionCodes
    }

    fun getPromotionCodes(couponId: StripeCouponId) = executeWithStripeHandling {
        val allCodes = mutableListOf<String>()
        var startingAfter: String? = null
        var hasMore = true

        while (hasMore) {
            val paramsBuilder = PromotionCodeListParams.builder()
                .setLimit(100)
                .setCoupon(couponId)
            if (startingAfter != null) {
                paramsBuilder.setStartingAfter(startingAfter)
            }

            val params = paramsBuilder.build()
            val result = PromotionCode.list(params)
            val codes = result.data.map { it.code }
            allCodes.addAll(codes)
            hasMore = result.hasMore
            startingAfter = result.data.lastOrNull()?.id
        }

        allCodes
    }

    fun toMoneyResult(price: Price): MoneyResult {
        val taxBehaviour = TaxBehaviour.fromStripe(price.taxBehavior)

        val taxRate = taxRate(taxBehaviour)

        return MoneyResult(
            unitAmount = price.unitAmount,
            currency = MonetaryCurrency.fromIsoCode(price.currency),
            taxBehaviour = taxBehaviour,
            taxRatePercentage = taxRate,
        )
    }

    fun taxRate(taxBehaviour: TaxBehaviour): BigDecimal = when (taxBehaviour) {
        TaxBehaviour.INCLUSIVE -> inclusiveTaxRate
        TaxBehaviour.EXCLUSIVE -> exclusiveTaxRate
    }

    private fun <T> executeWithStripeHandling(block: () -> T): T = try {
        block()
    } catch (e: StripeException) {
        logger.warn(
            """
                An error occurred while calling Stripe.
                Response [${e.message}]
            """.trimIndent(),
        )

        throw IntegrationException(
            statusCode = e.statusCode,
            responseBody = e.message ?: "unknown",
            message = "Error ${e.statusCode} - ${e.requestId} - ${e.code}",
        )
    }
}
