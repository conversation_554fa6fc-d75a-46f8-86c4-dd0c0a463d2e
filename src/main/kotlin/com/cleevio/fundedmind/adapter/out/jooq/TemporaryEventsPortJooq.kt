package com.cleevio.fundedmind.adapter.out.jooq

import com.cleevio.fundedmind.application.module.temporaryevents.port.out.TemporaryEventsPort
import com.cleevio.fundedmind.jooq.tables.references.TEMPORARY_EVENTS
import org.jooq.DSLContext
import org.springframework.stereotype.Component

@Component
class TemporaryEventsPortJooq(
    private val dslContext: DSLContext,
) : TemporaryEventsPort {

    override fun updateMasterclassWaitlistAugust2025(newValue: Int) {
        val exists = dslContext.fetchExists(
            dslContext
                .selectOne()
                .from(TEMPORARY_EVENTS),
        )

        if (exists) {
            dslContext
                .update(TEMPORARY_EVENTS)
                .set(TEMPORARY_EVENTS.MASTERCLASS_WAITLIST_AUGUST_2025, newValue)
                .execute()
        } else {
            dslContext
                .insertInto(TEMPORARY_EVENTS)
                .set(TEMPORARY_EVENTS.MASTERCLASS_WAITLIST_AUGUST_2025, newValue)
                .execute()
        }
    }

    override fun getMasterclassWaitlistAugust2025(): Int = dslContext
        .select(TEMPORARY_EVENTS.MASTERCLASS_WAITLIST_AUGUST_2025)
        .from(TEMPORARY_EVENTS)
        .limit(1)
        .fetchOne()
        ?.map { it[TEMPORARY_EVENTS.MASTERCLASS_WAITLIST_AUGUST_2025]!! }
        ?: error("Temporary Events table not initialized")
}
